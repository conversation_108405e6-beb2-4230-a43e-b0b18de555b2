import * as yup from "yup";

import {
  ApplicationStatus,
  CampaignStatus,
  CampaignVisibility,
} from "../types/campaign";
import { DeliverableType } from "../types/deliverable";

export const campaignSchema = yup.object({
  name: yup
    .string()
    .required("Campaign name is required")
    .min(3, "Campaign name must be at least 3 characters")
    .max(100, "Campaign name must be at most 100 characters"),
  description: yup
    .string()
    .required("Description is required")
    .min(10, "Description must be at least 10 characters")
    .max(2000, "Description must be at most 2000 characters"),
  visibility: yup
    .string()
    .required("Visibility is required")
    .oneOf(Object.values(CampaignVisibility)),
  deliverables: yup
    .array()
    .of(
      yup.object({
        name: yup.string().required(),
        description: yup.string().required(),
        daysToComplete: yup.number().min(1).required(),
        minimumPayment: yup.number().min(0).required(),
        type: yup
          .mixed<DeliverableType>()
          .oneOf(Object.values(DeliverableType))
          .required(),
      }),
    )
    .min(1, "At least one deliverable is required")
    .required("Deliverables are required"),
  startDate: yup.date().required("Start date is required"),
  endDate: yup
    .date()
    .required("End date is required")
    .min(yup.ref("startDate"), "End date must be after start date"),
  status: yup
    .string()
    .oneOf(Object.values(CampaignStatus))
    .default(CampaignStatus.DRAFT),
  price: yup.number().min(0).required("Price is required"),
  interests: yup.array().of(yup.string()).required("Interests are required"),
  files: yup
    .array()
    .of(
      yup.object({
        url: yup.string().required(),
        originalName: yup.string().required(),
      }),
    )
    .optional(),
});

// Schema for campaign updates - same as campaignSchema but without default status
export const campaignUpdateSchema = yup.object({
  name: yup
    .string()
    .required("Campaign name is required")
    .min(3, "Campaign name must be at least 3 characters")
    .max(100, "Campaign name must be at most 100 characters"),
  description: yup
    .string()
    .required("Description is required")
    .min(10, "Description must be at least 10 characters")
    .max(2000, "Description must be at most 2000 characters"),
  visibility: yup
    .string()
    .required("Visibility is required")
    .oneOf(Object.values(CampaignVisibility)),
  deliverables: yup
    .array()
    .of(
      yup.object({
        name: yup.string().required(),
        description: yup.string().required(),
        daysToComplete: yup.number().min(1).required(),
        minimumPayment: yup.number().min(0).required(),
        type: yup
          .mixed<DeliverableType>()
          .oneOf(Object.values(DeliverableType))
          .required(),
      }),
    )
    .min(1, "At least one deliverable is required")
    .required("Deliverables are required"),
  startDate: yup.date().required("Start date is required"),
  endDate: yup
    .date()
    .required("End date is required")
    .min(yup.ref("startDate"), "End date must be after start date"),
  status: yup
    .string()
    .oneOf(Object.values(CampaignStatus))
    .optional(), // No default value for updates
  price: yup.number().min(0).required("Price is required"),
  interests: yup.array().of(yup.string()).required("Interests are required"),
  files: yup
    .array()
    .of(
      yup.object({
        url: yup.string().required(),
        originalName: yup.string().required(),
      }),
    )
    .optional(),
});

export const campaignApplicationSchema = yup.object({
  campaignId: yup.string().required("Campaign ID is required"),
  message: yup
    .string()
    .optional()
    .max(1000, "Message must be at most 1000 characters"),
  compensation: yup
    .number()
    .optional()
    .min(0, "Compensation cannot be negative"),
  deliverables: yup
    .array()
    .of(
      yup.object({
        name: yup.string().required(),
        description: yup.string().required(),
        daysToComplete: yup.number().min(1).required(),
        minimumPayment: yup.number().min(0).required(),
        type: yup
          .mixed<DeliverableType>()
          .oneOf(Object.values(DeliverableType))
          .required(),
      }),
    )
    .min(1, "At least one deliverable is required")
    .required("Deliverables are required"),
});

export const updateApplicationStatusSchema = yup.object({
  applicationId: yup.string().required("Application ID is required"),
  status: yup
    .string()
    .oneOf(Object.values(ApplicationStatus))
    .required("Status is required"),
});
