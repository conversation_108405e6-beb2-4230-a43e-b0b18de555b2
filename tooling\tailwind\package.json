{"name": "@repo/tailwind-config", "version": "0.1.0", "private": true, "type": "module", "exports": {"./native": "./native.ts", "./web": "./web.ts"}, "license": "MIT", "scripts": {"clean": "rm -rf .turbo node_modules", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "typecheck": "tsc --noEmit"}, "dependencies": {"postcss": "8.4.49", "tailwindcss": "3.4.17", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/prettier-config": "workspace:*", "@repo/tsconfig": "workspace:*", "eslint": "9.14.0", "prettier": "^3.5.3", "typescript": "5.7.3"}, "prettier": "@repo/prettier-config"}