"use client";

import { useState, useEffect, useRef } from "react";
import { loadStripe } from "@stripe/stripe-js";
import {
  Elements,
  PaymentElement,
  useStripe,
  useElements,
} from "@stripe/react-stripe-js";
import { trpc } from "@/lib/trpc/client";

import { SerializedContract } from "@repo/server/src/types/contract";

import { Button } from "../ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "../ui/dialog";
import { useToast } from "../ui/toast/use-toast";
import { LoadingSpinner } from "../ui/LoadingSpinner";

// Initialize Stripe
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);

interface PaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  contract: SerializedContract;
  onPaymentComplete?: () => void;
  isRetry?: boolean;
}

interface PaymentFormProps {
  contract: SerializedContract;
  clientSecret: string;
  paymentAmount: number;
  onPaymentComplete?: () => void;
  onClose: () => void;
}

function PaymentForm({ contract, paymentAmount, onPaymentComplete, onClose }: PaymentFormProps) {
  const stripe = useStripe();
  const elements = useElements();
  const { toast } = useToast();
  const [isProcessing, setIsProcessing] = useState(false);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    setIsProcessing(true);

    try {
      const { error } = await stripe.confirmPayment({
        elements,
        confirmParams: {
          return_url: `${window.location.origin}/app/brand/contracts`,
        },
        redirect: "if_required",
      });

      if (error) {
        console.error("Payment failed:", error);
        toast({
          title: "Payment Failed",
          description: error.message || "An error occurred while processing your payment.",
          variant: "destructive",
        });
      } else {
        toast({
          title: "Payment Successful",
          description: "Your payment has been processed successfully. The contract is now active.",
          variant: "default",
        });
        onPaymentComplete?.();
        onClose();
      }
    } catch (error) {
      console.error("Payment error:", error);
      const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred. Please try again.";
      toast({
        title: "Payment Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };



  return (
    <form onSubmit={handleSubmit} className="tw-space-y-6">
      {/* Contract Summary */}
      <div className="tw-bg-gray-50 tw-p-4 tw-rounded-lg">
        <h3 className="tw-font-semibold tw-text-lg tw-mb-2 tw-text-black">Payment Summary</h3>
        <div className="tw-space-y-2">
          <div className="tw-flex tw-justify-between">
            <span>Contract:</span>
            <span className="tw-font-medium">{contract.title}</span>
          </div>
          <div className="tw-flex tw-justify-between">
            <span>Contract Number:</span>
            <span className="tw-font-mono tw-text-sm">{contract.contractNumber}</span>
          </div>
          <div className="tw-flex tw-justify-between tw-text-lg tw-font-semibold tw-border-t tw-pt-2">
            <span>Total Amount:</span>
            <span>
              {paymentAmount.toLocaleString("en-US", {
                style: "currency",
                currency: "USD",
              })}
            </span>
          </div>
        </div>
      </div>

      {/* Payment Method */}
      <div>
        <label className="tw-block tw-text-sm tw-font-medium tw-mb-2 tw-text-aims-text-primary">
          Payment Method
        </label>
          <PaymentElement />
      </div>

      {/* Payment Button */}
      <div className="tw-flex tw-justify-end tw-space-x-3">
        <Button
          type="button"
          variant="outline"
          onClick={onClose}
          disabled={isProcessing}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={!stripe || isProcessing}
          className="tw-min-w-[120px]"
        >
          {isProcessing ? (
            <>
              <LoadingSpinner />
              Processing...
            </>
          ) : (
            `Pay ${paymentAmount.toLocaleString("en-US", {
              style: "currency",
              currency: "USD",
            })}`
          )}
        </Button>
      </div>
    </form>
  );
}

export function PaymentModal({
  isOpen,
  onClose,
  contract,
  onPaymentComplete,
  isRetry = false,
}: PaymentModalProps) {
  const { toast } = useToast();
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const [paymentAmount, setPaymentAmount] = useState<number>(contract.terms.totalCompensation);
  const [isLoading, setIsLoading] = useState(false);
  const initializingRef = useRef(false);

  const createPaymentIntentMutation = trpc.contract.createPaymentIntent.useMutation();
  const retryPaymentMutation = trpc.contract.retryPayment.useMutation();
  const getPaymentStatusQuery = trpc.contract.getPaymentStatus.useQuery(
    { contractId: contract.id },
    { enabled: isOpen }
  );



  useEffect(() => {
    if (isOpen && !clientSecret && !initializingRef.current) {
      const initialize = async () => {
        if (initializingRef.current) {
          return; // Already initializing, prevent duplicate calls
        }

        initializingRef.current = true;
        setIsLoading(true);
        try {
          const result = isRetry
            ? await retryPaymentMutation.mutateAsync({
                contractId: contract.id,
              })
            : await createPaymentIntentMutation.mutateAsync({
                contractId: contract.id,
              });
          setClientSecret(result.clientSecret);
        } catch (error) {
          console.error("Failed to create payment intent:", error);
          toast({
            title: isRetry ? "Payment Retry Failed" : "Payment Setup Failed",
            description: "Unable to initialize payment. Please try again.",
            variant: "destructive",
          });
          onClose();
        } finally {
          setIsLoading(false);
          initializingRef.current = false;
        }
      };

      initialize();
    }
  }, [isOpen, clientSecret, isRetry, retryPaymentMutation, createPaymentIntentMutation, contract.id, toast, onClose]);

  useEffect(() => {
    if (getPaymentStatusQuery.data?.amount) {
      setPaymentAmount(getPaymentStatusQuery.data.amount);
    }
  }, [getPaymentStatusQuery.data]);

  const handleClose = () => {
    setClientSecret(null);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="tw-max-w-2xl">
        <DialogHeader>
          <DialogTitle>Complete Payment</DialogTitle>
          <DialogDescription>
            Complete the payment to activate your contract and begin the campaign.
          </DialogDescription>
        </DialogHeader>

        {isLoading ? (
          <div className="tw-flex tw-justify-center tw-items-center tw-py-8">
            <LoadingSpinner />
            <span className="tw-ml-2">Setting up payment...</span>
          </div>
        ) : clientSecret ? (
          <Elements
            stripe={stripePromise}
            options={{
              clientSecret,
              appearance: {
                theme: 'stripe',
                variables: {
                  colorPrimary: '#0570de',
                  colorBackground: '#ffffff',
                  colorText: '#30313d',
                  colorDanger: '#df1b41',
                  fontFamily: 'system-ui, sans-serif',
                  spacingUnit: '4px',
                  borderRadius: '6px',
                  fontSizeBase: '16px',
                },
                rules: {
                  '.Input': {
                    border: '1px solid #e2e8f0',
                    boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
                  },
                  '.Input:focus': {
                    border: '1px solid #0570de',
                    boxShadow: '0 0 0 2px rgba(5, 112, 222, 0.1)',
                  },
                  '.Input--invalid': {
                    border: '1px solid #df1b41',
                    boxShadow: '0 0 0 2px rgba(223, 27, 65, 0.1)',
                  },
                },
              },
            }}
          >
            <PaymentForm
              contract={contract}
              clientSecret={clientSecret}
              paymentAmount={paymentAmount}
              onPaymentComplete={onPaymentComplete}
              onClose={handleClose}
            />
          </Elements>
        ) : (
          <div className="tw-text-center tw-py-8">
            <p className="tw-text-red-600">Failed to initialize payment</p>
            <Button
              onClick={() => {
                setClientSecret(null);
                initializingRef.current = false;
              }}
              className="tw-mt-4"
            >
              Try Again
            </Button>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
