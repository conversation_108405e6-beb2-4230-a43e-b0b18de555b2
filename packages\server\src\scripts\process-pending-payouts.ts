import { connect, disconnect } from "mongoose";
import { processPendingPayouts } from "../services/athletePayoutService";

import "dotenv/config";

async function main() {
  try {
    console.log("[Payout Processor] Starting payout processing job...");
    await connect(process.env.DB_URL!);
    
    await processPendingPayouts();
    
    console.log("[Payout Processor] Payout processing job completed successfully");
  } catch (error) {
    console.error("[Payout Processor] Error processing payouts:", error);
    process.exit(1);
  } finally {
    await disconnect();
  }
}

main();
