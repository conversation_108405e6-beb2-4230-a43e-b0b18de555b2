// Re-export types from server for frontend use
export {
  DeliverableSubmissionStatus,
  type DeliverableSubmissionFile,
  type DeliverableSubmission,
  type SerializedDeliverableSubmission,
  type DeliverableSubmissionInput,
  type DeliverableSubmissionReviewInput,
} from "@repo/server/src/types/deliverableSubmission";

import { SerializedDeliverableSubmission, DeliverableSubmissionStatus } from "@repo/server/src/types/deliverableSubmission";

// Frontend-specific interfaces
export interface DeliverableSubmissionFormData {
  description: string;
  files: File[];
}

export interface DeliverableSubmissionModalProps {
  isOpen: boolean;
  onClose: () => void;
  campaignId: string;
  deliverableId: string;
  deliverableName: string;
  onSubmissionComplete?: (submission: SerializedDeliverableSubmission) => void;
  existingSubmission?: SerializedDeliverableSubmission; // For resubmissions
}

export interface SubmissionStatusBadgeProps {
  status: DeliverableSubmissionStatus;
  className?: string;
}

export interface DeliverableWithSubmission {
  id: string;
  name: string;
  description: string;
  type: string;
  minimumPayment: number;
  daysToComplete: number;
  location?: string;
  date?: string;
  time?: string;
  content?: string[];
  productName?: string;
  productPrice?: number;
  createdAt: string;
  updatedAt: string;
  submission?: SerializedDeliverableSubmission;
}
