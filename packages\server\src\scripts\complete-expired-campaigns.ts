import { connect, disconnect } from "mongoose";

import { CampaignModel } from "../models/campaign";
import { CampaignStatus } from "../types/campaign";

import "dotenv/config";

async function completeExpiredCampaigns() {
  await connect(process.env.DB_URL!);
  const now = new Date();
  const result = await CampaignModel.updateMany(
    {
      endDate: { $lt: now },
      status: { $nin: [CampaignStatus.COMPLETED, CampaignStatus.CANCELLED] },
    },
    { $set: { status: CampaignStatus.COMPLETED } },
  );
  console.log(`Marked ${result.modifiedCount} campaigns as COMPLETED.`);
  await disconnect();
}

completeExpiredCampaigns()
  .then(() => process.exit(0))
  .catch((err) => {
    console.error(err);
    process.exit(1);
  });
