export default function DashboardStats({ stats }: { stats: { label: string, value: string | number, icon: React.ReactNode }[] }) {
    return (
        <div className="tw-flex tw-flex-wrap tw-gap-6 tw-mb-8">
        {stats.map((stat) => (
          <div
            key={stat.label}
            className="tw-bg-aims-dark-2 tw-flex-1 tw-rounded-lg tw-p-6 tw-flex tw-items-center tw-gap-4 tw-shadow-sm"
          >
            <div className="tw-bg-aims-dark-3 tw-p-3 tw-rounded-md">
              {stat.icon}
            </div>
            <div>
              <div className="tw-text-xs tw-text-aims-text-secondary tw-font-medium">
                {stat.label}
              </div>
              <div className="tw-text-2xl tw-font-bold tw-text-aims-text-primary">
                {stat.value}
              </div>
            </div>
          </div>
        ))}
      </div>
    );
}
