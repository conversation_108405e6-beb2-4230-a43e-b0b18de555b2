"use client";

import { Toaster } from "@/components/ui/toast/toaster";
import { GoogleAnalyticsProvider } from "@/providers/GoogleAnalyticsProvider";
import { GoogleAuthProvider } from "@/providers/GoogleOAuthProvider";
import TRPCProvider from "@/providers/trpc-provider";
import { store } from "@/store";
import { Analytics } from "@vercel/analytics/react";
import { Provider } from "react-redux";
import { PostHogProvider } from "@/providers/posthog";

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <GoogleAuthProvider>
      <Provider store={store}>
        <TRPCProvider>
          <PostHogProvider>
            {children}
          </PostHogProvider>
          <Toaster />
          <GoogleAnalyticsProvider />
          <Analytics />
        </TRPCProvider>
      </Provider>
    </GoogleAuthProvider>
  );
}
