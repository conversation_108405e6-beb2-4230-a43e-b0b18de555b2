import { Schema, model } from "mongoose";
import { BrandProfile } from "../types/brand";

const brandSchema = new Schema<BrandProfile>({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'Users',
    required: true,
    unique: true
  },
  companyName: {
    type: String,
    default: null
  },
  industry: {
    type: String,
    default: null
  },
  website: {
    type: String,
    default: null
  },
  location: {
    type: String,
    default: null
  },
  description: {
    type: String,
    default: null
  },
  logo: {
    url: {
      type: String,
      default: null
    },
    key: {
      type: String,
      default: null
    },
    uploadedAt: {
      type: Date,
      default: null
    }
  },
  subscriptionActive: {
    type: Boolean,
    default: false
  },
  stripeSubscriptionId: {
    type: String,
    default: null
  }
}, { timestamps: true });

const BrandModel = model("Brands", brandSchema);
export default BrandModel;

