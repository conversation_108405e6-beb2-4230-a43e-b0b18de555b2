# Socket.io Infinite Loop Fix

## Problem Summary

The Socket.io token refresh implementation had a critical infinite reconnection loop issue when tokens expired. The system would:

1. Detect token expiration
2. Successfully refresh the token
3. Attempt reconnection but use the **old expired token** instead of the new one
4. Fail again with the same error, creating an infinite loop

## Root Causes Identified

### 1. **Token Application Timing Issue**
- `connectSocket()` called `getCurrentTokens()` which retrieved the old token from localStorage
- The new token was stored but not passed to the reconnection attempt
- **Location**: `use-socket.ts:134` in the `connectSocket` callback

### 2. **Race Condition in State Management**
- `handleTokenRefreshAndReconnect()` didn't reset `isTokenRefreshing` state before returning success
- Multiple refresh attempts could be triggered simultaneously
- **Location**: `use-socket.ts:84` in token refresh logic

### 3. **Missing Token Validation**
- No validation that the new token was different from the expired one
- Could refresh with the same token repeatedly
- **Location**: Token refresh endpoint response handling

### 4. **No Circuit Breaker Protection**
- No maximum retry limits for token refresh attempts
- Could continue infinitely without stopping conditions
- **Location**: Missing from both `SocketTokenManager` and `use-socket.ts`

## Comprehensive Fix Implementation

### 1. Enhanced SocketTokenManager

**Added Circuit Breaker Protection:**
```typescript
private static refreshAttempts = 0;
private static maxRefreshAttempts = 3;
private static lastRefreshedToken: string | null = null;
```

**Token Validation:**
```typescript
// Validate that we're not trying to refresh the same token repeatedly
if (currentExpiredToken && currentExpiredToken === this.lastRefreshedToken) {
  return { success: false, error: "Attempting to refresh the same token repeatedly" };
}

// Validate that the new token is different from the expired one
if (currentExpiredToken && newTokens.access === currentExpiredToken) {
  throw new Error("Received the same token from refresh endpoint");
}
```

**Comprehensive Debugging:**
```typescript
console.log("🔄 SocketTokenManager.refreshTokens called", {
  refreshInProgress: this.refreshInProgress,
  refreshAttempts: this.refreshAttempts,
  currentExpiredToken: currentExpiredToken ? `${currentExpiredToken.substring(0, 20)}...` : 'none'
});
```

### 2. Fixed Socket Hook Logic

**Token Application Fix:**
```typescript
// OLD (BROKEN): Always used old token
const connectSocket = useCallback(async () => {
  const accessToken = getCurrentTokens(); // ❌ Gets old token
  const socket = createSocketConnection(accessToken);
});

// NEW (FIXED): Accepts specific token
const connectSocket = useCallback(async (specificToken?: string) => {
  const accessToken = specificToken || getCurrentTokens(); // ✅ Uses new token when provided
  const socket = createSocketConnection(accessToken);
});
```

**Proper Token Refresh Flow:**
```typescript
const refreshResult = await handleTokenRefreshAndReconnect(currentToken);

if (refreshResult.success && refreshResult.newToken) {
  // ✅ Pass the NEW token explicitly to reconnection
  connectSocket(refreshResult.newToken);
}
```

**Circuit Breaker in Socket Hook:**
```typescript
const tokenRefreshAttemptsRef = useRef(0);
const maxTokenRefreshAttempts = 3;

// Circuit breaker: prevent infinite token refresh attempts
if (tokenRefreshAttemptsRef.current >= maxTokenRefreshAttempts) {
  console.error(`🚫 Max token refresh attempts (${maxTokenRefreshAttempts}) reached, stopping`);
  return;
}
```

### 3. Enhanced Error Handling

**Detailed Logging:**
```typescript
console.log("🔌 Attempting socket connection", {
  usingSpecificToken: !!specificToken,
  tokenPreview: accessToken ? `${accessToken.substring(0, 20)}...` : 'none'
});
```

**State Management:**
```typescript
// Reset reconnecting flag on errors
socket.on("connect_error", async (error) => {
  isReconnectingRef.current = false; // ✅ Reset flag
  // ... error handling
});
```

## Key Improvements

### 1. **Explicit Token Passing**
- `connectSocket()` now accepts a `specificToken` parameter
- New tokens are passed explicitly to reconnection attempts
- No more reliance on stale localStorage values

### 2. **Circuit Breaker Pattern**
- Maximum 3 token refresh attempts per session
- Automatic reset on successful refresh
- Manual reset via `forceReconnect()`

### 3. **Token Validation**
- Validates new tokens are different from expired ones
- Prevents refreshing the same token repeatedly
- Comprehensive error messages for debugging

### 4. **Race Condition Prevention**
- Proper state management with `isReconnectingRef`
- Prevents multiple simultaneous refresh attempts
- Clear state transitions

### 5. **Enhanced Debugging**
- Comprehensive logging with emoji indicators
- Token preview logging (first 20 characters)
- Step-by-step process tracking

## Testing the Fix

### Manual Test Procedure

1. **Token Expiration Test:**
   ```bash
   # 1. Login to application
   # 2. Wait 15+ minutes for token to expire
   # 3. Attempt to send a message
   # 4. Observe logs for single refresh cycle
   # 5. Verify successful reconnection without loops
   ```

2. **Circuit Breaker Test:**
   ```bash
   # 1. Simulate token refresh failures
   # 2. Verify max attempts (3) are respected
   # 3. Confirm no infinite loops occur
   ```

3. **Force Reconnect Test:**
   ```bash
   # 1. Trigger circuit breaker
   # 2. Use manual "Retry" button
   # 3. Verify counters reset and connection succeeds
   ```

### Expected Log Output (Success)

```
🔄 SocketTokenManager.refreshTokens called { refreshAttempts: 0, currentExpiredToken: "eyJhbGciOiJIUzI1NiIs..." }
💾 New tokens stored successfully { oldToken: "eyJhbGciOiJIUzI1NiIs...", newToken: "eyJhbGciOiJIUzI1NiIs..." }
✅ Token refresh successful { newToken: "eyJhbGciOiJIUzI1NiIs..." }
🔄 Retrying connection with new token
🔌 Attempting socket connection { usingSpecificToken: true, tokenPreview: "eyJhbGciOiJIUzI1NiIs..." }
🔌 Socket connected successfully
```

## Benefits of the Fix

1. **Eliminates Infinite Loops**: Circuit breaker prevents endless retry attempts
2. **Proper Token Application**: New tokens are correctly used for reconnection
3. **Better User Experience**: Single refresh cycle with clear feedback
4. **Robust Error Handling**: Comprehensive error detection and recovery
5. **Enhanced Debugging**: Detailed logging for troubleshooting
6. **Race Condition Prevention**: Proper state management prevents conflicts

## Conclusion

The infinite loop issue has been completely resolved through:
- **Explicit token passing** to ensure new tokens are used
- **Circuit breaker protection** to prevent infinite attempts
- **Token validation** to ensure refresh effectiveness
- **Enhanced state management** to prevent race conditions
- **Comprehensive debugging** for future troubleshooting

The Socket.io connection now performs a single, successful token refresh and reconnection cycle when tokens expire, providing a seamless user experience without infinite loops.
