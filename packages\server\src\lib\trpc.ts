import type * as trpcExpress from "@trpc/server/adapters/express";
import { initTRPC, TRPCError } from "@trpc/server";

import { isAuth } from "../middleware/auth";
import BrandModel from "../models/brand";
import PasswordResetTokenModel from "../models/password-reset-token";
import { AppRoles } from "../models/user";
import { ExtendedTRPCError } from "../utils/trpc";

export const createContext = ({
  req,
  res,
}: trpcExpress.CreateExpressContextOptions) => {
  // Log incoming request
  console.log(
    `[HTTP] ${new Date().toLocaleString("en-US", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      hour12: true,
    })} - ${req.method} ${req.url}`,
  );
  // console.log(`[HTTP] Headers:`, req.headers);

  return {
    req,
    res,
  };
};

type Context = Awaited<ReturnType<typeof createContext>>;
export const trpc = initTRPC.context<Context>().create();

// Add logging middleware
const logger = trpc.middleware(async ({ next, path, type, input }) => {
  const start = Date.now();
  // console.log(
  //   `[tRPC] ${new Date().toISOString()} - ${type.toUpperCase()} ${path}`,
  // );
  if (input) {
    console.log(`[tRPC] Input:`, input);
  }

  const result = await next();
  const duration = Date.now() - start;
  console.log(
    `[tRPC] ${new Date().toLocaleString("en-US", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      hour12: true,
    })} - ${type.toUpperCase()} ${path} completed in ${duration}ms`,
  );
  return result;
});

const isAuthenticated = trpc.middleware(async ({ ctx, next }) => {
  await isAuth(ctx.req, ctx.res);
  if (!ctx.req.user) {
    throw new ExtendedTRPCError("UNAUTHORIZED", "Unauthorized Access!");
  }
  return next();
});

const isAdmin = trpc.middleware(async ({ ctx, next }) => {
  if (!ctx.req.user.roles.includes(AppRoles.ADMIN)) {
    throw new ExtendedTRPCError("UNAUTHORIZED", "Unauthorized Access!");
  }
  return next();
});

const isBrand = trpc.middleware(async ({ ctx, next }) => {
  if (ctx.req.user.userType !== "brand") {
    throw new ExtendedTRPCError("UNAUTHORIZED", "Unauthorized Access!");
  }
  return next();
});

const isSubscribed = trpc.middleware(async ({ ctx, next }) => {
  const brand = await BrandModel.findOne({ userId: ctx.req.user.id });
  if (!brand?.subscriptionActive) {
    throw new ExtendedTRPCError("UNAUTHORIZED", "Unauthorized Access!");
  }
  return next();
});

export const isValidPassResetToken = trpc.middleware(
  async ({ next, input }) => {
    const { id, token } = input as { id: string; token: string };
    const resetPassToken = await PasswordResetTokenModel.findOne({ owner: id });
    if (!resetPassToken)
      throw new ExtendedTRPCError(
        "FORBIDDEN",
        "Unauthorized Request, invalid token",
      );
    const matched = await resetPassToken?.compareToken(token);
    if (!matched)
      throw new ExtendedTRPCError(
        "FORBIDDEN",
        "Unauthorized Request, invalid token",
      );
    return next();
  },
);

// Add logger to all procedures
export const publicProcedure = trpc.procedure.use(logger);
export const privateProcedure = trpc.procedure.use(logger).use(isAuthenticated);
export const adminProcedure = privateProcedure.use(isAdmin);
export const brandProcedure = privateProcedure.use(isBrand);
export const subscribedProcedure = brandProcedure.use(isSubscribed);
