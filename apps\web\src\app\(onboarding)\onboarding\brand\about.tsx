"use client";

import { useEffect, useState } from "react";
import Image from "next/image";
import { LoadingSpinner } from "@/components/ui/LoadingSpinner";
import { client } from "@/lib/trpc/client";
import {
  selectBrandBasicInfo,
  selectCurrentStep,
  updateBrandBasicInfo,
} from "@/store/slices/onboarding";
import { PlusIcon } from "@heroicons/react/24/outline";
import { useDispatch, useSelector } from "react-redux";

const INDUSTRY_OPTIONS = [
  "Apparel",
  "Sports Equipment",
  "Nutrition",
  "Technology",
  "Media",
  "Retail",
  "Other",
];

export default function BrandAbout() {
  const dispatch = useDispatch();
  const currentStep = useSelector(selectCurrentStep);
  const basicInfo = useSelector(selectBrandBasicInfo);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState("");
  const [customIndustry, setCustomIndustry] = useState("");
  const [formData, setFormData] = useState({
    companyName: basicInfo.companyName || "",
    industry: basicInfo.industry || "",
    website: basicInfo.website || "",
    location: basicInfo.location || "",
    description: basicInfo.description || "",
    logoUrl: basicInfo.logoUrl || "",
  });

  useEffect(() => {
    // Only update the store with custom industry if it exists
    const industryValue =
      formData.industry === "Other" && customIndustry
        ? customIndustry
        : formData.industry;

    dispatch(
      updateBrandBasicInfo({
        ...formData,
        industry: industryValue,
      }),
    );
  }, [dispatch, formData, customIndustry]);

  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validate file size (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
      setUploadError("File size must be less than 10MB");
      return;
    }

    try {
      setIsUploading(true);
      setUploadError("");

      // Get presigned URL
      const { url, key, publicUrl } = await client.brand.getUploadUrl.mutate({
        fileType: file.type,
      });

      // Upload to S3
      const uploadResponse = await fetch(url, {
        method: "PUT",
        body: file,
        headers: {
          "Content-Type": file.type,
        },
      });

      if (!uploadResponse.ok) throw new Error("Failed to upload image");

      // Update profile with new image
      await client.brand.updateLogo.mutate({
        key,
        url: publicUrl,
      });

      setFormData((prev) => ({
        ...prev,
        logoUrl: publicUrl,
      }));
    } catch (error) {
      setUploadError(
        error instanceof Error ? error.message : "Failed to upload image",
      );
    } finally {
      setIsUploading(false);
    }
  };

  if (currentStep !== 1) {
    return null;
  }

  return (
    <div className="tw-space-y-6 sm:tw-space-y-8 tw-text-aims-text-primary">
      <div className="tw-text-center tw-px-4 sm:tw-px-0">
        <h1 className="tw-text-xl sm:tw-text-2xl tw-font-semibold tw-mb-2">
          Tell us about your brand
        </h1>
      </div>
      <div className="tw-grid tw-grid-cols-1 lg:tw-grid-cols-2 tw-gap-6 lg:tw-gap-8">
        {/* Form Fields */}
        <div className="tw-space-y-4 sm:tw-space-y-6 tw-order-2 lg:tw-order-1">
          <div>
            <label className="tw-block tw-text-sm sm:tw-text-base tw-font-medium tw-mb-2">
              Company Name<span className="tw-text-red-500">*</span>
            </label>
            <input
              type="text"
              className="tw-w-full tw-bg-aims-dark-3 tw-border tw-border-gray-600 tw-rounded-md tw-px-3 tw-py-3 sm:tw-py-2 tw-h-12 sm:tw-h-auto tw-text-base sm:tw-text-sm tw-touch-manipulation"
              value={formData.companyName}
              onChange={(e) =>
                setFormData({ ...formData, companyName: e.target.value })
              }
              required
            />
          </div>
          <div>
            <label className="tw-block tw-text-sm sm:tw-text-base tw-font-medium tw-mb-2">
              Industry<span className="tw-text-red-500">*</span>
            </label>
            <select
              className="tw-w-full tw-bg-aims-dark-3 tw-border tw-border-gray-600 tw-rounded-md tw-px-3 tw-py-3 sm:tw-py-2 tw-h-12 sm:tw-h-auto tw-text-base sm:tw-text-sm tw-touch-manipulation"
              value={formData.industry}
              onChange={(e) => {
                const value = e.target.value;
                setFormData({ ...formData, industry: value });
                if (value === "Other") {
                  setCustomIndustry("");
                }
              }}
              required
            >
              <option value="">Select Industry</option>
              {INDUSTRY_OPTIONS.map((opt) => (
                <option key={opt} value={opt}>
                  {opt}
                </option>
              ))}
            </select>
            {formData.industry === "Other" && (
              <input
                type="text"
                className="tw-w-full tw-bg-aims-dark-3 tw-border tw-border-gray-600 tw-rounded-md tw-px-3 tw-py-3 sm:tw-py-2 tw-mt-2 tw-h-12 sm:tw-h-auto tw-text-base sm:tw-text-sm tw-touch-manipulation"
                placeholder="Enter your industry"
                value={customIndustry}
                onChange={(e) => {
                  setCustomIndustry(e.target.value);
                }}
              />
            )}
          </div>
          <div>
            <label className="tw-block tw-text-sm sm:tw-text-base tw-font-medium tw-mb-2">
              Website
            </label>
            <input
              type="url"
              className="tw-w-full tw-bg-aims-dark-3 tw-border tw-border-gray-600 tw-rounded-md tw-px-3 tw-py-3 sm:tw-py-2 tw-h-12 sm:tw-h-auto tw-text-base sm:tw-text-sm tw-touch-manipulation"
              value={formData.website}
              onChange={(e) =>
                setFormData({ ...formData, website: e.target.value })
              }
              placeholder="https://yourbrand.com"
            />
          </div>
          <div>
            <label className="tw-block tw-text-sm sm:tw-text-base tw-font-medium tw-mb-2">
              Location<span className="tw-text-red-500">*</span>
            </label>
            <input
              type="text"
              className="tw-w-full tw-bg-aims-dark-3 tw-border tw-border-gray-600 tw-rounded-md tw-px-3 tw-py-3 sm:tw-py-2 tw-h-12 sm:tw-h-auto tw-text-base sm:tw-text-sm tw-touch-manipulation"
              value={formData.location}
              onChange={(e) =>
                setFormData({ ...formData, location: e.target.value })
              }
              placeholder="City, State or Country"
            />
          </div>
          <div>
            <label className="tw-block tw-text-sm sm:tw-text-base tw-font-medium tw-mb-2">
              Description<span className="tw-text-red-500">*</span>
            </label>
            <textarea
              className="tw-w-full tw-bg-aims-dark-3 tw-border tw-border-gray-600 tw-rounded-md tw-px-3 tw-py-3 sm:tw-py-2 tw-text-base sm:tw-text-sm tw-touch-manipulation"
              value={formData.description}
              onChange={(e) =>
                setFormData({ ...formData, description: e.target.value })
              }
              rows={4}
              placeholder="Tell us about your brand, mission, and what you do."
            />
          </div>
        </div>

        {/* Logo Upload */}
        <div className="tw-flex tw-justify-center tw-order-1 lg:tw-order-2">
          <div
            className="tw-cursor-pointer tw-h-[200px] tw-w-[200px] sm:tw-h-[250px] sm:tw-w-[250px] lg:tw-h-[300px] lg:tw-w-[300px] tw-bg-aims-dark-3 tw-rounded-lg tw-flex tw-flex-col tw-items-center tw-justify-center tw-border-2 tw-border-dashed tw-border-gray-600 tw-relative tw-overflow-hidden tw-touch-manipulation"
            onClick={() =>
              !isUploading && document.getElementById("logo-upload")?.click()
            }
          >
            {isUploading ? (
              <div className="tw-flex tw-items-center tw-justify-center tw-w-full tw-h-full">
                <LoadingSpinner />
              </div>
            ) : formData.logoUrl ? (
              <div className="tw-w-full tw-h-full tw-relative">
                <Image
                  src={formData.logoUrl}
                  alt="Brand Logo"
                  fill
                  sizes="(max-width: 640px) 200px, (max-width: 1024px) 250px, 300px"
                  className="tw-object-contain tw-p-4"
                />
                <div className="tw-absolute tw-inset-0 tw-bg-black tw-bg-opacity-50 tw-flex tw-items-center tw-justify-center tw-opacity-0 hover:tw-opacity-100 tw-transition-opacity">
                  <p className="tw-text-aims-text-primary tw-text-xs sm:tw-text-sm">
                    Change logo
                  </p>
                </div>
              </div>
            ) : (
              <div className="tw-text-center tw-p-4 sm:tw-p-6">
                <PlusIcon className="tw-mx-auto tw-h-8 tw-w-8 sm:tw-h-12 sm:tw-w-12 tw-text-gray-400" />
                <p className="tw-mt-2 tw-text-xs sm:tw-text-sm tw-text-gray-400">
                  Click to upload logo
                </p>
                <p className="tw-text-xs tw-text-gray-500">
                  PNG, JPG, GIF up to 10MB
                </p>
              </div>
            )}
            <input
              id="logo-upload"
              type="file"
              className="tw-hidden"
              accept="image/jpeg,image/png,image/gif"
              onChange={handleFileUpload}
              disabled={isUploading}
            />
          </div>
        </div>
      </div>

      {uploadError && (
        <div className="tw-p-3 sm:tw-p-4 tw-bg-red-500/10 tw-border tw-border-red-500 tw-rounded-lg tw-text-red-500 tw-text-sm tw-text-center">
          {uploadError}
        </div>
      )}
    </div>
  );
}
