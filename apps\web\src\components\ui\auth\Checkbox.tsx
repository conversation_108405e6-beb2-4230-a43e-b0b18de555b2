interface CheckboxProps {
  id: string;
  label: React.ReactNode;
  checked: boolean;
  onChange: (checked: boolean) => void;
}

export function Checkbox({ id, label, checked, onChange }: CheckboxProps) {
  return (
    <div className="tw-flex tw-items-center">
      <input
        id={id}
        type="checkbox"
        checked={checked}
        onChange={(e) => onChange(e.target.checked)}
        className="tw-h-4 tw-w-4 tw-rounded tw-border-aims-dark-3 tw-bg-aims-dark-1 tw-text-aims-primary focus:tw-ring-aims-primary"
      />
      <label
        htmlFor={id}
        className="tw-ml-2 tw-block tw-text-sm tw-text-aims-text-secondary"
      >
        {label}
      </label>
    </div>
  );
}
