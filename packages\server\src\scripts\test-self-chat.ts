import { connectDB } from "../db";
import { createChat, sendMessage } from "../controllers/chat";
import { ChatType, MessageType } from "../types/chat";
import UserModel from "../models/user";
import AthleteModel from "../models/athlete";

/**
 * Test script to verify self-chat functionality for system notifications
 */
async function testSelfChat() {
  try {
    console.log("🔗 Connecting to database...");
    await connectDB();

    // Find a test athlete user
    console.log("👤 Finding test athlete...");
    const athlete = await AthleteModel.findOne().populate("userId");
    if (!athlete || !athlete.userId) {
      console.error("❌ No athlete found in database");
      return;
    }

    const user = athlete.userId as any;
    console.log(`✅ Found athlete: ${user.name} (${user.email})`);

    // Test 1: Create a self-chat
    console.log("\n📱 Testing self-chat creation...");
    const selfChat = await createChat(
      user._id.toString(),
      [user._id.toString()], // Same user ID for self-chat
      ChatType.DIRECT
    );
    console.log(`✅ Self-chat created: ${selfChat.id}`);
    console.log(`   Participants: ${selfChat.participants.length} (should be 1)`);

    // Test 2: Send a system notification message
    console.log("\n💰 Testing earnings notification...");
    const earningsMessage = await sendMessage(
      selfChat.id,
      user._id.toString(),
      "Your earnings of $50.00 from contract 'Test Campaign Contract #12345' have been credited to your wallet.",
      MessageType.EARNINGS_CREDITED
    );
    console.log(`✅ Earnings message sent: ${earningsMessage.id}`);

    // Test 3: Send a payout completion message
    console.log("\n💸 Testing payout completion notification...");
    const payoutMessage = await sendMessage(
      selfChat.id,
      user._id.toString(),
      "Your payout of $45.00 has been successfully processed and sent to your bank account.",
      MessageType.PAYOUT_COMPLETED
    );
    console.log(`✅ Payout message sent: ${payoutMessage.id}`);

    // Test 4: Send a payout failed message
    console.log("\n❌ Testing payout failed notification...");
    const failedPayoutMessage = await sendMessage(
      selfChat.id,
      user._id.toString(),
      "Your payout of $45.00 failed to process. Please check your bank account details and try again.",
      MessageType.PAYOUT_FAILED
    );
    console.log(`✅ Failed payout message sent: ${failedPayoutMessage.id}`);

    // Test 5: Try to create another self-chat (should return existing one)
    console.log("\n🔄 Testing duplicate self-chat creation...");
    const duplicateSelfChat = await createChat(
      user._id.toString(),
      [user._id.toString()],
      ChatType.DIRECT
    );
    console.log(`✅ Duplicate self-chat request handled: ${duplicateSelfChat.id}`);
    console.log(`   Same chat ID: ${selfChat.id === duplicateSelfChat.id ? 'Yes' : 'No'}`);

    console.log("\n🎉 All self-chat tests passed!");
    console.log("\nNext steps:");
    console.log("1. Start the development server");
    console.log("2. Log in as the test athlete");
    console.log("3. Navigate to the chat page");
    console.log("4. You should see a 'System Notifications' chat with the test messages");

  } catch (error) {
    console.error("❌ Test failed:", error);
  } finally {
    process.exit(0);
  }
}

// Run the test
testSelfChat();
