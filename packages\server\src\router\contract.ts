import * as yup from "yup";

import {
  generate<PERSON>ontract,
  getContractById,
  updateContract,
  updateContractStatus,
  getBrandContracts,
  getAthleteContracts,
  generateContractPdf,
  signContract,
  cancelContract,
  getContractByApplication,
  getContractsByCampaign,
  createPaymentIntent,
  getPaymentStatus,
  fulfillContract,
  retryPayment,
  syncContractPaymentStatus,
} from "../controllers/contract";
import { privateProcedure, trpc } from "../lib/trpc";
import { ContractStatus } from "../types/contract";
import {
  contractGenerationSchema,
  contractUpdateSchema,
  contractStatusUpdateSchema,
  contractSignatureSchema,
  getContractSchema,
  getBrandContractsSchema,
  getAthleteContractsSchema,
  generateContractPdfSchema,
  cancelContractSchema,
} from "../validators/contract";

export const contractRouter = trpc.router({
  // Generate a new contract from an accepted application
  generate: privateProcedure
    .input(contractGenerationSchema)
    .mutation(async ({ ctx, input }) => {
      return generateContract(
        ctx.req.user.id,
        ctx.req.user.userType,
        input,
      );
    }),

  // Get a specific contract by ID
  getById: privateProcedure
    .input(getContractSchema)
    .query(async ({ ctx, input }) => {
      return getContractById(
        ctx.req.user.id,
        ctx.req.user.userType,
        input.contractId,
      );
    }),

  // Update contract terms and details
  update: privateProcedure
    .input(contractUpdateSchema)
    .mutation(async ({ ctx, input }) => {
      return updateContract(
        ctx.req.user.id,
        ctx.req.user.userType,
        input,
      );
    }),

  // Update contract status (for workflow transitions)
  updateStatus: privateProcedure
    .input(contractStatusUpdateSchema)
    .mutation(async ({ ctx, input }) => {
      return updateContractStatus(
        ctx.req.user.id,
        ctx.req.user.userType,
        input.contractId,
        input.status,
        input.reason,
      );
    }),

  // Get contracts for a brand (with pagination and filtering)
  getBrandContracts: privateProcedure
    .input(getBrandContractsSchema)
    .query(async ({ ctx, input }) => {
      return getBrandContracts(
        ctx.req.user.id,
        ctx.req.user.userType,
        input.status,
        input.search,
        input.page,
        input.limit,
      );
    }),

  // Get contracts for an athlete (with pagination and filtering)
  getAthleteContracts: privateProcedure
    .input(getAthleteContractsSchema)
    .query(async ({ ctx, input }) => {
      return getAthleteContracts(
        ctx.req.user.id,
        ctx.req.user.userType,
        input.status,
        input.search,
        input.page,
        input.limit,
      );
    }),

  // Generate or regenerate contract PDF
  generatePdf: privateProcedure
    .input(generateContractPdfSchema)
    .mutation(async ({ ctx, input }) => {
      return generateContractPdf(
        ctx.req.user.id,
        ctx.req.user.userType,
        input.contractId,
        input.regenerate,
      );
    }),

  // Sign a contract (athlete only)
  sign: privateProcedure
    .input(contractSignatureSchema)
    .mutation(async ({ ctx, input }) => {
      return signContract(
        ctx.req.user.id,
        ctx.req.user.userType,
        input.contractId,
        input.signatureData,
      );
    }),

  // Cancel a contract
  cancel: privateProcedure
    .input(cancelContractSchema)
    .mutation(async ({ ctx, input }) => {
      return cancelContract(
        ctx.req.user.id,
        ctx.req.user.userType,
        input.contractId,
        input.reason,
      );
    }),

  // Get contracts by campaign ID
  getByCampaign: privateProcedure
    .input(
      yup.object({
        campaignId: yup.string().required("Campaign ID is required"),
      }),
    )
    .query(async ({ ctx, input }) => {
      return getContractsByCampaign(
        ctx.req.user.id,
        ctx.req.user.userType,
        input.campaignId,
      );
    }),

  // Get contracts by application ID
  getByApplication: privateProcedure
    .input(
      yup.object({
        applicationId: yup.string().required("Application ID is required"),
      }),
    )
    .query(async ({ ctx, input }) => {
      return getContractByApplication(
        ctx.req.user.id,
        ctx.req.user.userType,
        input.applicationId,
      );
    }),

  // Payment-related routes
  createPaymentIntent: privateProcedure
    .input(
      yup.object({
        contractId: yup.string().required("Contract ID is required"),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return createPaymentIntent(
        ctx.req.user.id,
        ctx.req.user.userType,
        input.contractId,
      );
    }),

  getPaymentStatus: privateProcedure
    .input(
      yup.object({
        contractId: yup.string().required("Contract ID is required"),
      }),
    )
    .query(async ({ ctx, input }) => {
      return getPaymentStatus(
        ctx.req.user.id,
        ctx.req.user.userType,
        input.contractId,
      );
    }),

  fulfillContract: privateProcedure
    .input(
      yup.object({
        contractId: yup.string().required("Contract ID is required"),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return fulfillContract(
        ctx.req.user.id,
        ctx.req.user.userType,
        input.contractId,
      );
    }),

  retryPayment: privateProcedure
    .input(
      yup.object({
        contractId: yup.string().required("Contract ID is required"),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return retryPayment(
        ctx.req.user.id,
        ctx.req.user.userType,
        input.contractId,
      );
    }),

  syncPaymentStatus: privateProcedure
    .input(
      yup.object({
        contractId: yup.string().required("Contract ID is required"),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return syncContractPaymentStatus(
        ctx.req.user.id,
        ctx.req.user.userType,
        input.contractId,
      );
    }),

  // Debug route to clear payment intent (temporary)
  clearPaymentIntent: privateProcedure
    .input(
      yup.object({
        contractId: yup.string().required("Contract ID is required"),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      if (ctx.req.user.userType !== "brand") {
        throw new Error("Only brands can clear payment intents");
      }

      const ContractModel = (await import("../models/contract")).default;
      await ContractModel.findByIdAndUpdate(input.contractId, {
        paymentIntentId: undefined,
        paymentStatus: undefined,
        paymentInitiatedAt: undefined,
        paymentFailedAt: undefined,
        paymentFailureReason: undefined,
        paymentRetryCount: 0,
      });

      console.log(`[Debug] Cleared payment intent for contract ${input.contractId}`);
      return { success: true };
    }),

  // Extend contract expiration
  extendExpiration: privateProcedure
    .input(
      yup.object({
        contractId: yup.string().required("Contract ID is required"),
        additionalDays: yup
          .number()
          .min(1, "Additional days must be at least 1")
          .max(365, "Additional days cannot exceed 365")
          .required("Additional days is required"),
        reason: yup.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // This will be implemented in the controller
      // For now, throw not implemented error
      throw new Error("Not implemented yet");
    }),

  // Get contract statistics for dashboard
  getStats: privateProcedure.query(async ({ ctx }) => {
    // This will be implemented to return contract statistics
    // For now, return empty stats
    return {
      total: 0,
      signed: 0,
      pending: 0,
      cancelled: 0,
      expired: 0,
    };
  }),

  // Bulk operations for contracts
  bulkUpdateStatus: privateProcedure
    .input(
      yup.object({
        contractIds: yup
          .array()
          .of(yup.string().required())
          .min(1, "At least one contract ID is required")
          .required("Contract IDs are required"),
        status: yup
          .string()
          .oneOf(Object.values(ContractStatus), "Invalid contract status")
          .required("Status is required"),
        reason: yup.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // This will be implemented for bulk operations
      // For now, throw not implemented error
      throw new Error("Not implemented yet");
    }),

  // Search contracts
  search: privateProcedure
    .input(
      yup.object({
        query: yup.string().required("Search query is required"),
        filters: yup
          .object({
            status: yup
              .string()
              .oneOf(Object.values(ContractStatus), "Invalid contract status")
              .optional(),
            dateFrom: yup.date().optional(),
            dateTo: yup.date().optional(),
          })
          .optional(),
        page: yup.number().min(1, "Page must be at least 1").optional(),
        limit: yup
          .number()
          .min(1, "Limit must be at least 1")
          .max(100, "Limit cannot exceed 100")
          .optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      // This will be implemented for contract search
      // For now, return empty results
      return {
        contracts: [],
        total: 0,
        pages: 0,
      };
    }),
});
