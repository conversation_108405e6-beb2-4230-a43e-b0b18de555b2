"use client";

import { memo, useCallback, useEffect, useMemo, useState } from "react";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  AthletePaymentInfo,
  selectAthletePaymentInfo,
  selectCurrentStep,
  updateAthletePaymentInfo,
} from "@/store/slices/onboarding";
import { useDispatch, useSelector } from "react-redux";

// Memoized form field components
const FormField = memo(
  ({
    label,
    required,
    children,
  }: {
    label: string;
    required?: boolean;
    children: React.ReactNode;
  }) => (
    <div className="tw-space-y-2">
      <label className="tw-block tw-text-sm sm:tw-text-base tw-font-medium tw-text-aims-text-primary">
        {label}
        {required && <span className="tw-text-red-500">*</span>}
      </label>
      {children}
    </div>
  ),
);
FormField.displayName = "FormField";

const SelectField = memo(
  ({
    value,
    onChange,
    placeholder,
    options,
  }: {
    value: string | null;
    onChange: (value: string) => void;
    placeholder: string;
    options: string[];
  }) => (
    <Select value={value ?? ""} onValueChange={onChange}>
      <SelectTrigger className="!tw-bg-aims-dark-3 tw-border-aims-dark-3 tw-text-aims-text-primary">
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {options.map((option) => (
          <SelectItem key={option} value={option}>
            {option}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  ),
);
SelectField.displayName = "SelectField";

const InputField = memo(
  ({
    type = "text",
    value,
    onChange,
    placeholder,
  }: {
    type?: string;
    value: string | number | null;
    onChange: (value: string) => void;
    placeholder?: string;
  }) => (
    <div className="tw-relative">
      <span className="tw-absolute tw-left-3 tw-top-1/2 -tw-translate-y-1/2 tw-text-aims-text-primary tw-text-sm sm:tw-text-base">
        $
      </span>
      <Input
        type={type}
        value={value ?? ""}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder}
        className="tw-pl-7 tw-bg-aims-dark-3 tw-border-aims-dark-3 tw-text-aims-text-primary tw-h-12 sm:tw-h-10 tw-text-base sm:tw-text-sm tw-touch-manipulation"
      />
    </div>
  ),
);
InputField.displayName = "InputField";

export default function AthletePayment() {
  const dispatch = useDispatch();
  const currentStep = useSelector(selectCurrentStep);
  const paymentInfo = useSelector(selectAthletePaymentInfo);
  const [formData, setFormData] = useState<AthletePaymentInfo>(paymentInfo);

  // Memoize the form validation
  const isFormValid = useMemo(() => {
    const requiredFields = ["referralSource"];
    if (formData.referralSource === "Friend/Colleague") {
      requiredFields.push("referralName", "referralVenmo");
    }
    return requiredFields.every((field) =>
      Boolean(formData[field as keyof AthletePaymentInfo]),
    );
  }, [formData]);

  // Update Redux when moving to next step
  useEffect(() => {
    if (currentStep === 4 && isFormValid) {
      dispatch(updateAthletePaymentInfo(formData));
    }
  }, [currentStep, dispatch, formData, isFormValid]);

  // Optimize field change handler
  const handleFieldChange = useCallback(
    (field: keyof AthletePaymentInfo, value: string | number) => {
      setFormData((prev) => ({
        ...prev,
        [field]:
          field === "referralSource" ||
          field === "referralName" ||
          field === "referralVenmo"
            ? value
            : value === ""
              ? null
              : Number(value),
      }));
    },
    [],
  );

  // Memoize handlers for each field
  const handlePaymentChange = useCallback(
    (field: keyof Omit<AthletePaymentInfo, "referralSource">) =>
      (value: string) => {
        handleFieldChange(field, value);
      },
    [handleFieldChange],
  );

  const handleReferralSourceChange = useCallback(
    (value: string) => {
      handleFieldChange("referralSource", value);
    },
    [handleFieldChange],
  );

  if (currentStep !== 4) {
    return null;
  }

  return (
    <div className="tw-space-y-6 sm:tw-space-y-8 tw-text-aims-text-primary">
      <div className="tw-text-center tw-px-4 sm:tw-px-0">
        <h1 className="tw-text-xl sm:tw-text-2xl tw-font-semibold tw-mb-2">
          Payment & Referral Information
        </h1>
      </div>

      <div className="tw-space-y-6 sm:tw-space-y-8">
        <div className="tw-space-y-4 sm:tw-space-y-6">
          <h3 className="tw-text-base sm:tw-text-lg tw-font-medium">
            Minimum Payment Requirements
          </h3>
          <div className="tw-grid tw-grid-cols-1 sm:tw-grid-cols-2 tw-gap-4 sm:tw-gap-6">
            <FormField label="Photo or Video Shoot">
              <InputField
                type="number"
                value={formData.shoot}
                onChange={handlePaymentChange("shoot")}
                placeholder="Enter minimum amount"
              />
            </FormField>

            <FormField label="In-Person Appearance">
              <InputField
                type="number"
                value={formData.inPerson}
                onChange={handlePaymentChange("inPerson")}
                placeholder="Enter minimum amount"
              />
            </FormField>

            <FormField label="Content Share">
              <InputField
                type="number"
                value={formData.contentShare}
                onChange={handlePaymentChange("contentShare")}
                placeholder="Enter minimum amount"
              />
            </FormField>

            <FormField label="Content Creation">
              <InputField
                type="number"
                value={formData.contentCreation}
                onChange={handlePaymentChange("contentCreation")}
                placeholder="Enter minimum amount"
              />
            </FormField>

            <FormField label="Gifted Collaboration">
              <InputField
                type="number"
                value={formData.giftedCollab}
                onChange={handlePaymentChange("giftedCollab")}
                placeholder="Enter minimum amount"
              />
            </FormField>

            <FormField label="Custom">
              <InputField
                type="number"
                value={formData.other}
                onChange={handlePaymentChange("other")}
                placeholder="Enter minimum amount"
              />
            </FormField>
          </div>
        </div>

        <FormField label="How did you hear about AIMS?" required>
          <SelectField
            value={formData.referralSource}
            onChange={handleReferralSourceChange}
            placeholder="Select an option"
            options={[
              "Social Media",
              "Friend/Colleague",
              "Search Engine",
              "Event/Conference",
              "Other",
            ]}
          />
        </FormField>

        {formData.referralSource === "Friend/Colleague" && (
          <div className="tw-space-y-4 sm:tw-space-y-6">
            <FormField label="Friend/Colleague Name" required>
              <Input
                value={formData.referralName || ""}
                onChange={(e) =>
                  handleFieldChange("referralName", e.target.value)
                }
                placeholder="Enter their name"
                className="tw-h-12 sm:tw-h-10 tw-text-base sm:tw-text-sm tw-touch-manipulation"
              />
            </FormField>
            <FormField label="Venmo Username" required>
              <Input
                value={formData.referralVenmo || ""}
                onChange={(e) =>
                  handleFieldChange("referralVenmo", e.target.value)
                }
                placeholder="Enter your Venmo username"
                className="tw-h-12 sm:tw-h-10 tw-text-base sm:tw-text-sm tw-touch-manipulation"
              />
            </FormField>
          </div>
        )}
      </div>
    </div>
  );
}
