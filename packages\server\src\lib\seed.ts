import UserModel from "../models/user";
import { AppRoles } from "../models/user";
import { hash } from "bcrypt";

const users = [
    {
        name: "<PERSON>",
        email: "<EMAIL>",
        password: "password123",
        verified: true,
        roles: ["Admin"],
    },
    {
        name: "<PERSON>",
        email: "<EMAIL>",
        password: "password123",
        verified: true,
        roles: ["Standard"],
    },
    {
        name: "<PERSON>",
        email: "<EMAIL>",
        password: "password123",
        verified: true,
        roles: ["Standard"],
    },
    {
        name: "<PERSON>",
        email: "<EMAIL>",
        password: "password123",
        verified: true,
        roles: ["Standard"],
    },
    {
        name: "<PERSON>",
        email: "<EMAIL>",
        password: "password123",
        verified: true,
        roles: ["Standard"],
    },
    {
        name: "<PERSON>",
        email: "<EMAIL>",
        password: "password123",
        verified: true,
        roles: ["Standard"],
    },
    {
        name: "<PERSON>",
        email: "<EMAIL>",
        password: "password123",
        verified: true,
        roles: ["Standard"],
    },
    {
        name: "<PERSON>",
        email: "<EMAIL>",
        password: "password123",
        verified: true,
        roles: ["Standard"],
    },
    {
        name: "<PERSON>",
        email: "<EMAIL>",
        password: "password123",
        verified: true,
        roles: ["Standard"],
    },
    {
        name: "Jack Thomas",
        email: "<EMAIL>",
        password: "password123",
        verified: true,
        roles: ["Standard"],
    },
];

export async function seedUsers() {
    try {
        let createdCount = 0;
        let skippedCount = 0;

        for (const user of users) {
            // Check if user already exists
            const existingUser = await UserModel.findOne({ email: user.email });

            if (!existingUser) {
                const hashedPassword = await hash(user.password, 10);
                await UserModel.create({
                    ...user,
                    password: hashedPassword,
                    roles: [AppRoles.STANDARD]
                });
                createdCount++;
            } else {
                skippedCount++;
            }
        }

        console.log(`Seeding completed: ${createdCount} users created, ${skippedCount} skipped (already exist)`);
    } catch (error) {
        console.error("Error seeding users:", error);
    }
} 