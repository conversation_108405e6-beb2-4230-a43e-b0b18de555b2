"use client";

import { useState } from "react";
import { trpc } from "@/lib/trpc/client";

import { ContractGenerationInput } from "@repo/server/src/types/contract";

import { Button } from "../ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "../ui/dialog";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Textarea } from "../ui/textarea";
import { useToast } from "../ui/toast/use-toast";

interface ContractGenerationModalProps {
  isOpen: boolean;
  onClose: () => void;
  campaignId: string;
  applicationId: string;
  campaignName: string;
  athleteName: string;
  onSuccess?: (contractId: string) => void;
}

export function ContractGenerationModal({
  isOpen,
  onClose,
  campaignId,
  applicationId,
  campaignName,
  athleteName,
  onSuccess,
}: ContractGenerationModalProps) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    expirationDays: 30 as number | string,
    additionalTerms: "",
    cancellationPolicy: "",
    intellectualPropertyRights: "",
    confidentialityClause: "",
  });

  const generateContractMutation = trpc.contract.generate.useMutation();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const input: ContractGenerationInput = {
        campaignId,
        applicationId,
        expirationDays: typeof formData.expirationDays === 'string'
          ? parseInt(formData.expirationDays) || 30
          : formData.expirationDays,
        customTerms: {
          additionalTerms: formData.additionalTerms
            ? formData.additionalTerms.split('\n').map(term => term.trim()).filter(term => term.length > 0)
            : undefined,
          cancellationPolicy: formData.cancellationPolicy || undefined,
          intellectualPropertyRights: formData.intellectualPropertyRights || undefined,
          confidentialityClause: formData.confidentialityClause || undefined,
        },
      };

      const contract = await generateContractMutation.mutateAsync(input);

      toast({
        title: "Contract Generated",
        description: `Contract ${contract.contractNumber} has been created successfully.`,
        variant: "success",
      });

      onSuccess?.(contract.id);
      onClose();
    } catch (error) {
      console.error("Failed to generate contract:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to generate contract. Please try again.";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: keyof typeof formData, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="tw-max-w-2xl tw-max-h-[90vh] tw-overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Generate Contract</DialogTitle>
          <DialogDescription>
            Create a formal contract for <strong>{athleteName}</strong> to participate in the campaign <strong>{campaignName}</strong>.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="tw-space-y-6">
          {/* Contract Expiration */}
          <div className="tw-space-y-1">
            <Label htmlFor="expirationDays" required>Contract Expiration (Days)</Label>
            <Input
              id="expirationDays"
              max="365"
              value={formData.expirationDays}
              onChange={(e) => handleInputChange("expirationDays", e.target.value === '' ? '' : parseInt(e.target.value) || 30)}
              placeholder="30"
              className="!tw-bg-aims-dark-2"
            />
            <p className="tw-text-sm tw-text-aims-dark-6">
              Number of days the athlete has to sign the contract before it expires.
            </p>
          </div>

          {/* Additional Terms */}
          <div className="tw-space-y-1">
            <Label htmlFor="additionalTerms">Additional Terms (Optional)</Label>
            <Textarea
              id="additionalTerms"
              value={formData.additionalTerms}
              onChange={(e) => handleInputChange("additionalTerms", e.target.value)}
              placeholder="Enter additional terms, one per line..."
              rows={4}
              className="!tw-bg-aims-dark-2"
            />
            <p className="tw-text-sm tw-text-aims-dark-6">
              Add any specific terms or requirements for this contract. Each line will be a separate term.
            </p>
          </div>

          {/* Cancellation Policy */}
          <div className="tw-space-y-1">
            <Label htmlFor="cancellationPolicy">Cancellation Policy (Optional)</Label>
            <Textarea
              id="cancellationPolicy"
              value={formData.cancellationPolicy}
              onChange={(e) => handleInputChange("cancellationPolicy", e.target.value)}
              placeholder="Describe the cancellation policy..."
              rows={3}
              className="!tw-bg-aims-dark-2"
            />
            <p className="tw-text-sm tw-text-aims-dark-6">
              Specify the terms under which either party can cancel the contract.
            </p>
          </div>

          {/* Intellectual Property Rights */}
          <div className="tw-space-y-1">
            <Label htmlFor="intellectualPropertyRights">Intellectual Property Rights (Optional)</Label>
            <Textarea
              id="intellectualPropertyRights"
              value={formData.intellectualPropertyRights}
              onChange={(e) => handleInputChange("intellectualPropertyRights", e.target.value)}
              placeholder="Describe intellectual property rights and usage..."
              rows={3}
              className="!tw-bg-aims-dark-2"
            />
            <p className="tw-text-sm tw-text-aims-dark-6">
              Define who owns the content created and how it can be used.
            </p>
          </div>

          {/* Confidentiality Clause */}
          <div className="tw-space-y-1">
            <Label htmlFor="confidentialityClause">Confidentiality Clause (Optional)</Label>
            <Textarea
              id="confidentialityClause"
              value={formData.confidentialityClause}
              onChange={(e) => handleInputChange("confidentialityClause", e.target.value)}
              placeholder="Describe confidentiality requirements..."
              rows={3}
              className="!tw-bg-aims-dark-2"
            />
            <p className="tw-text-sm tw-text-aims-dark-6">
              Specify any confidentiality or non-disclosure requirements.
            </p>
          </div>

          <DialogFooter className="tw-space-x-3">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
              className="tw-text-aims-text-primary"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
            >
              {isSubmitting ? "Generating..." : "Generate Contract"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
