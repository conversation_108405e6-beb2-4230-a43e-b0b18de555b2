import { OAuth2Client } from "google-auth-library";

import { ExtendedTRPCError } from "./trpc";

const client = new OAuth2Client(process.env.GOOGLE_CLIENT_ID);

export const verifyGoogleToken = async (token: string) => {
  try {
    const ticket = await client.verifyIdToken({
      idToken: token,
      audience: process.env.GOOGLE_CLIENT_ID,
    });

    const payload = ticket.getPayload();
    if (!payload) {
      throw new ExtendedTRPCError("UNAUTHORIZED", "Invalid Google token");
    }

    return {
      email: payload.email,
      name: payload.name,
      picture: payload.picture,
    };
  } catch (error) {
    throw new ExtendedTRPCError("UNAUTHORIZED", "Invalid Google token");
  }
};
