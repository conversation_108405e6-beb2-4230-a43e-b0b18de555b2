import { model, Schema } from "mongoose";

import { AthleteProfile } from "./user";

type AthleteSchemaType = {
  [K in keyof AthleteProfile]: any;
};

const athleteSchema = new Schema<AthleteProfile, {}, AthleteSchemaType>(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: "Users",
      required: true,
      unique: true,
    },
    name: {
      type: String,
      required: true,
    },
    university: {
      type: String,
      default: null,
    },
    sport: {
      type: String,
      default: null,
    },
    yearInSchool: {
      type: String,
      default: null,
    },
    position: {
      type: String,
      default: null,
    },
    birthDate: {
      type: Date,
      default: null,
    },
    gender: {
      type: String,
      enum: ["male", "female", null],
      default: null,
    },
    bio: {
      type: String,
      default: null,
    },
    profilePicture: {
      url: {
        type: String,
        default: null,
      },
      key: {
        type: String,
        default: null,
      },
      uploadedAt: {
        type: Date,
        default: null,
      },
    },
    businessInterests: {
      type: [String],
      default: [],
    },
    // Optional fields
    socialMedia: {
      instagram: String,
      twitter: String,
      tiktok: String,
    },
    minPayment: {
      shoot: Number,
      inPerson: Number,
      contentShare: Number,
      contentCreation: Number,
      giftedCollab: Number,
      other: Number,
    },
    referralSource: {
      type: String,
      default: null,
    },
    referralName: {
      type: String,
      default: null,
    },
    referralVenmo: {
      type: String,
      default: null,
    },
    hometown: {
      type: String,
      default: null,
    },
  },
  {
    timestamps: true,
    toJSON: { getters: true }, // This will allow us to format dates if needed
  },
);

const AthleteModel = model("Athletes", athleteSchema);
export default AthleteModel;
