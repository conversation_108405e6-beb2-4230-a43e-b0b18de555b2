"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { FormInput } from "@/components/ui/auth/FormInput";
import { Button } from "@/components/ui/button";
import { client } from "@/lib/trpc/client";
import { ArrowPathIcon } from "@heroicons/react/24/outline";

export default function ResetPassword() {
  const [status, setStatus] = useState<"loading" | "form" | "error">("loading");
  const [message, setMessage] = useState<string | null>(
    "Please wait, we are verifying your reset link",
  );
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    const verifyResetToken = async () => {
      try {
        const id = searchParams.get("id");
        const token = searchParams.get("token");

        if (!id || !token) {
          setStatus("error");
          setMessage("Invalid reset link");
          return;
        }

        await client.auth.verifyPasswordResetToken.mutate({ id, token });
        setStatus("form");
        setMessage(null); // Clear the verification message
      } catch (error) {
        setStatus("error");
        setMessage((error as Error).message);
      }
    };

    verifyResetToken();
  }, [searchParams]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!password.trim()) {
      setMessage("Password is required");
      return;
    }

    if (password !== confirmPassword) {
      setMessage("Passwords do not match");
      return;
    }

    setIsSubmitting(true);
    try {
      const id = searchParams.get("id");
      const token = searchParams.get("token");

      await client.auth.resetPassword.mutate({
        id: id!,
        token: token!,
        password,
      });

      setStatus("error"); // Using error state for success message display
      setMessage("Password reset successful! Redirecting to login...");

      // Redirect to login after 2 seconds
      setTimeout(() => {
        router.push("/auth/login");
      }, 2000);
    } catch (error) {
      setMessage((error as Error).message);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (status === "loading") {
    return (
      <div className="tw-w-full">
        <div className="tw-text-center">
          <h1 className="tw-text-2xl tw-font-bold tw-tracking-tight">
            Verifying Reset Link
          </h1>
          <p className="tw-mt-2 tw-text-aims-text-primary">{message}</p>
        </div>
      </div>
    );
  }

  if (status === "error") {
    return (
      <div className="tw-w-full">
        <div className="tw-text-center">
          <h1 className="tw-text-2xl tw-font-bold tw-tracking-tight">
            {message?.includes("successful") ? "Success!" : "Reset Link Error"}
          </h1>
          <p
            className={`tw-mt-2 ${message?.includes("successful") ? "tw-text-green-600" : "tw-text-red-600"}`}
          >
            {message}
          </p>
          {!message?.includes("successful") && (
            <Button
              onClick={() => router.push("/auth/login")}
              className="tw-mt-4"
            >
              Back to Sign In
            </Button>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="tw-w-full">
      <div className="tw-max-w-md tw-mx-auto">
        <h1 className="tw-text-2xl tw-font-bold tw-tracking-tight tw-text-center">
          Reset Password
        </h1>
        <form onSubmit={handleSubmit} className="tw-mt-8 tw-space-y-6">
          <FormInput
            id="password"
            label="New Password"
            type="password"
            value={password}
            onChange={setPassword}
            required
            disabled={isSubmitting}
            isPassword
            placeholder="Enter new password"
          />

          <FormInput
            id="confirmPassword"
            label="Confirm New Password"
            type="password"
            value={confirmPassword}
            onChange={setConfirmPassword}
            required
            disabled={isSubmitting}
            isPassword
            placeholder="Re-enter new password"
          />

          {message && <p className="tw-text-red-600 tw-text-sm">{message}</p>}

          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <ArrowPathIcon className="tw-h-4 tw-w-4 tw-mr-2 tw-animate-spin" />
                Resetting...
              </>
            ) : (
              "Reset Password"
            )}
          </Button>
        </form>
      </div>
    </div>
  );
}
