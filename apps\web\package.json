{"name": "@repo/web", "version": "0.1.0", "private": true, "engines": {"node": ">=20.12.0"}, "scripts": {"dev": "next dev --turbopack --port 3010", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.2", "@heroicons/react": "^2.2.0", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-toast": "^1.2.11", "@react-oauth/google": "^0.12.2", "@reduxjs/toolkit": "^2.5.1", "@repo/server": "workspace:*", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.4.0", "@tanstack/react-query": "^5.74.7", "@trpc/client": "11.0.0-rc.730", "@trpc/react-query": "11.0.0-rc.730", "@trpc/server": "11.0.0-rc.730", "@vercel/analytics": "^1.5.0", "class-variance-authority": "^0.7.1", "framer-motion": "^12.16.0", "next": "15.2.0", "posthog-js": "^1.256.1", "react": "18.3.1", "react-dom": "18.3.1", "react-google-recaptcha": "^3.1.0", "react-redux": "^9.2.0", "redux-persist": "^6.0.0", "socket.io-client": "^4.8.1", "swiper": "^11.2.8"}, "devDependencies": {"@eslint/eslintrc": "3.1.0", "@tailwindcss/forms": "0.5.10", "@types/lodash": "^4.17.17", "@types/node": "20.17.6", "@types/react": "18.2.12", "@types/react-dom": "19.0.4", "@types/react-google-recaptcha": "2.1.9", "autoprefixer": "10.4.20", "eslint": "9.14.0", "eslint-config-next": "15.2.0", "postcss": "8.4.49", "tailwindcss": "3.4.17", "typescript": "5.7.3"}}