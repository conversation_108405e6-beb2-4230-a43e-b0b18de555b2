"use client";

import { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogOverlay,
  DialogPortal,
  DialogTitle,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/toast/use-toast";
import { trpc } from "@/lib/trpc/client";
import { CloudArrowUpIcon, XMarkIcon, ExclamationTriangleIcon } from "@heroicons/react/24/outline";
import { format } from "date-fns";

import {
  DeliverableSubmissionFormData,
  DeliverableSubmissionModalProps,
  DeliverableSubmissionFile,
  DeliverableSubmissionStatus,
} from "@/types/deliverableSubmission";
import { Label } from "@/components/ui/label";

export function DeliverableSubmissionModal({
  isOpen,
  onClose,
  campaignId,
  deliverableId,
  deliverableName,
  onSubmissionComplete,
  existingSubmission,
}: DeliverableSubmissionModalProps) {
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [formData, setFormData] = useState<DeliverableSubmissionFormData>({
    description: "",
    files: [],
  });
  const [isUploading, setIsUploading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const getUploadUrl = trpc.campaign.getUploadUrl.useMutation();
  const submitDeliverable = trpc.deliverableSubmission.submit.useMutation();
  const getSubmissionForResubmission = trpc.deliverableSubmission.getSubmissionForResubmission.useQuery(
    { campaignId, deliverableId },
    { enabled: isOpen && !existingSubmission }
  );

  const isResubmission = existingSubmission || getSubmissionForResubmission.data;
  const submissionData = existingSubmission || getSubmissionForResubmission.data;

  // Initialize form data when modal opens or when submission data is loaded
  useEffect(() => {
    if (isOpen) {
      if (submissionData) {
        // Pre-fill form with existing submission data for resubmission
        setFormData({
          description: submissionData.description,
          files: [],
        });
      } else {
        // Reset form for new submission
        setFormData({
          description: "",
          files: [],
        });
      }
    }
  }, [isOpen, submissionData]);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    if (files.length === 0) return;

    // Validate file types and sizes
    const validFiles = files.filter(file => {
      const maxSize = 50 * 1024 * 1024; // 50MB
      const allowedTypes = [
        'image/jpeg', 'image/png', 'image/gif', 'image/webp',
        'video/mp4', 'video/quicktime', 'video/x-msvideo',
        'application/pdf', 'text/plain',
        'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      ];

      if (file.size > maxSize) {
        toast({
          variant: "destructive",
          title: "File too large",
          description: `${file.name} is larger than 50MB`,
        });
        return false;
      }

      if (!allowedTypes.includes(file.type)) {
        toast({
          variant: "destructive",
          title: "Invalid file type",
          description: `${file.name} is not a supported file type`,
        });
        return false;
      }

      return true;
    });

    if (validFiles.length > 0) {
      setFormData(prev => ({
        ...prev,
        files: [...prev.files, ...validFiles],
      }));
    }

    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const removeFile = (index: number) => {
    setFormData(prev => ({
      ...prev,
      files: prev.files.filter((_, i) => i !== index),
    }));
  };

  const uploadFiles = async (): Promise<DeliverableSubmissionFile[]> => {
    const uploadedFiles: DeliverableSubmissionFile[] = [];
    
    for (const file of formData.files) {
      try {
        // Get signed URL for upload
        const { url, publicUrl } = await getUploadUrl.mutateAsync({
          fileType: file.type,
          uploadType: "deliverable-submission",
        });

        // Upload file to S3
        await fetch(url, {
          method: "PUT",
          body: file,
          headers: {
            "Content-Type": file.type,
          },
        });

        uploadedFiles.push({
          url: publicUrl,
          originalName: file.name,
          fileType: file.type,
          fileSize: file.size,
        });
      } catch (error) {
        console.error("Error uploading file:", error);
        throw new Error(`Failed to upload ${file.name}`);
      }
    }

    return uploadedFiles;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.description.trim()) {
      toast({
        variant: "destructive",
        title: "Description required",
        description: "Please provide a description for your submission",
      });
      return;
    }

    setIsSubmitting(true);
    setIsUploading(true);

    try {
      // Upload files first
      const uploadedFiles = await uploadFiles();
      setIsUploading(false);

      // Submit deliverable
      const submission = await submitDeliverable.mutateAsync({
        campaignId,
        deliverableId,
        description: formData.description,
        files: uploadedFiles,
      });

      toast({
        title: "Submission successful",
        description: "Your deliverable has been submitted for review",
      });

      // Reset form
      setFormData({ description: "", files: [] });
      
      // Call completion callback
      if (onSubmissionComplete) {
        onSubmissionComplete(submission);
      }

      onClose();
    } catch (error: any) {
      console.error("Error submitting deliverable:", error);
      toast({
        variant: "destructive",
        title: "Submission failed",
        description: error.message || "Failed to submit deliverable",
      });
    } finally {
      setIsSubmitting(false);
      setIsUploading(false);
    }
  };

  const handleClose = () => {
    if (isSubmitting) return; // Prevent closing during submission
    setFormData({ description: "", files: [] });
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogPortal>
        <DialogOverlay className="tw-fixed tw-inset-0 tw-bg-black/80 tw-backdrop-blur-sm tw-z-[100] tw-transition-opacity tw-duration-200" />
        <DialogContent className="tw-fixed tw-left-[50%] tw-top-[50%] tw-z-[101] tw-w-full tw-max-w-2xl tw-mx-4 sm:tw-mx-0 tw-translate-x-[-50%] tw-translate-y-[-50%] tw-bg-aims-dark-2 tw-border tw-border-aims-dark-4 tw-rounded-2xl tw-shadow-2xl tw-p-4 sm:tw-p-6 tw-max-h-[90vh] tw-overflow-y-auto">
          <DialogHeader className="tw-space-y-2 tw-mb-4">
            <DialogTitle className="tw-text-xl sm:tw-text-2xl tw-font-bold tw-text-aims-text-primary tw-tracking-tight">
              {isResubmission ? "Resubmit Deliverable" : "Submit Deliverable"}
            </DialogTitle>
            <DialogDescription className="tw-text-sm sm:tw-text-base tw-text-aims-text-secondary tw-leading-relaxed">
              {isResubmission
                ? `Update your submission for "${deliverableName}"`
                : `Submit your work for "${deliverableName}"`
              }
            </DialogDescription>
          </DialogHeader>

          {/* Previous Submission Details for Resubmissions */}
          {submissionData && (
            <div className="tw-bg-aims-dark-3 tw-rounded-lg tw-p-4 tw-mb-6">
              <div className="tw-flex tw-items-center tw-gap-2 tw-mb-3">
                <ExclamationTriangleIcon className="tw-w-5 tw-h-5 tw-text-orange-500" />
                <h3 className="tw-text-lg tw-font-semibold tw-text-aims-text-primary">
                  Previous Submission
                </h3>
              </div>

              <div className="tw-space-y-3">
                <div>
                  <span className="tw-text-sm tw-font-medium tw-text-aims-text-primary tw-block tw-mb-1">
                    Status: {submissionData.status === DeliverableSubmissionStatus.REJECTED ? "Rejected" : "Needs Revision"}
                  </span>
                  <span className="tw-text-xs tw-text-aims-text-secondary">
                    Submitted on {format(new Date(submissionData.submittedAt), "MMM d, yyyy 'at' h:mm a")}
                  </span>
                </div>

                {submissionData.feedback && (
                  <div>
                    <span className="tw-text-sm tw-font-medium tw-text-aims-text-primary tw-block tw-mb-1">
                      Feedback from Brand
                    </span>
                    <div className="tw-text-sm tw-text-aims-text-secondary tw-bg-aims-dark-2 tw-rounded tw-p-3">
                      {submissionData.feedback}
                    </div>
                  </div>
                )}

                <div>
                  <span className="tw-text-sm tw-font-medium tw-text-aims-text-primary tw-block tw-mb-1">
                    Previous Description
                  </span>
                  <div className="tw-text-sm tw-text-aims-text-secondary tw-bg-aims-dark-2 tw-rounded tw-p-3">
                    {submissionData.description}
                  </div>
                </div>
              </div>
            </div>
          )}

          <form onSubmit={handleSubmit} className="tw-space-y-4 sm:tw-space-y-6">
            {/* Description */}
            <div>
              <Label required htmlFor="description">
                {isResubmission ? "Updated Description" : "Description"}
              </Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder={isResubmission
                  ? "Describe your updated submission and how you've addressed the feedback..."
                  : "Describe your submission and any relevant details..."
                }
                className="tw-min-h-[100px] tw-resize-none !tw-bg-aims-dark-2"
                maxLength={2000}
                disabled={isSubmitting}
              />
              <div className="tw-text-xs tw-text-aims-text-secondary tw-mt-1">
                {formData.description.length}/2000 characters
              </div>
            </div>

            {/* File Upload */}
            <div>
              <Label htmlFor="files">Files</Label>
              
              {/* Upload Button */}
              <div className="tw-border-2 tw-border-dashed tw-border-aims-dark-4 tw-rounded-lg tw-p-4 sm:tw-p-6 tw-text-center">
                <input
                  id="files"
                  ref={fileInputRef}
                  type="file"
                  multiple
                  onChange={handleFileSelect}
                  className="tw-hidden"
                  accept="image/*,video/*,.pdf,.doc,.docx,.txt"
                  disabled={isSubmitting}
                />
                <CloudArrowUpIcon className="tw-w-8 tw-h-8 sm:tw-w-12 sm:tw-h-12 tw-mx-auto tw-text-aims-text-secondary tw-mb-2" />
                <p className="tw-text-sm tw-text-aims-text-secondary tw-mb-2">
                  Click to upload files or drag and drop
                </p>
                <p className="tw-text-xs tw-text-aims-text-secondary tw-mb-4">
                  Images, videos, PDFs, documents (max 50MB each)
                </p>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => fileInputRef.current?.click()}
                  disabled={isSubmitting}
                  className="tw-h-10 sm:tw-h-12 tw-text-aims-text-primary"
                >
                  Choose Files
                </Button>
              </div>

              {/* Selected Files */}
              {formData.files.length > 0 && (
                <div className="tw-mt-4 tw-space-y-2">
                  <p className="tw-text-sm tw-font-medium tw-text-aims-text-primary">
                    Selected Files ({formData.files.length})
                  </p>
                  <div className="tw-space-y-2">
                    {formData.files.map((file, index) => (
                      <div
                        key={index}
                        className="tw-flex tw-items-center tw-justify-between tw-p-3 tw-bg-aims-dark-3 tw-rounded-lg"
                      >
                        <div className="tw-flex-1 tw-min-w-0">
                          <p className="tw-text-sm tw-font-medium tw-text-aims-text-primary tw-truncate">
                            {file.name}
                          </p>
                          <p className="tw-text-xs tw-text-aims-text-secondary">
                            {(file.size / 1024 / 1024).toFixed(2)} MB
                          </p>
                        </div>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeFile(index)}
                          disabled={isSubmitting}
                          className="tw-ml-2 tw-p-1 tw-h-8 tw-w-8"
                        >
                          <XMarkIcon className="tw-w-4 tw-h-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            <DialogFooter className="tw-flex tw-flex-col sm:tw-flex-row tw-gap-2 sm:tw-gap-3 tw-pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={isSubmitting}
                className="tw-h-12 sm:tw-h-10 tw-order-2 sm:tw-order-1 tw-text-aims-text-primary"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting || isUploading || !formData.description.trim()}
                className="tw-h-12 sm:tw-h-10 tw-order-1 sm:tw-order-2"
              >
                {isUploading
                  ? "Uploading..."
                  : isSubmitting
                    ? (isResubmission ? "Resubmitting..." : "Submitting...")
                    : (isResubmission ? "Resubmit Deliverable" : "Submit Deliverable")
                }
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </DialogPortal>
    </Dialog>
  );
}
