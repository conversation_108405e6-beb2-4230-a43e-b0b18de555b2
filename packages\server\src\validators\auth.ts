import { isValidObjectId } from "mongoose";
import * as yup from "yup";

import { email } from "./shared";

const password = {
  password: yup.string().required("Password is missing"),
};

const tokenAndId = {
  id: yup
    .string()
    .test({
      name: "valid-id",
      message: "Invalid user id",
      test: (value) => {
        return isValidObjectId(value);
      },
    })
    .required("Id is missing"),
  token: yup.string().required("Token is missing"),
};

export const newUserSchema = yup.object({
  name: yup.string().required("Name is missing"),
  ...email,
  ...password,
  userType: yup
    .string()
    .oneOf(["brand", "athlete"], "User type must be either brand or athlete")
    .required("User type is missing"),
  captchaToken: yup.string().required("CAPTCHA verification is required"),
});

export const signInSchema = yup.object({
  ...email,
  ...password,
});

export const signInWithOauthSchema = yup.object({
  oauthIdToken: yup.string().required("OAuth token is required"),
  userType: yup
    .string()
    .oneOf(["brand", "athlete"])
    .required("User type is required"),
});

export const emailSchema = yup.object({
  ...email,
});

export const verifyEmailCodeSchema = yup.object({
  ...email,
  code: yup.string().required("Code is missing"),
});

export const verifyTokenSchema = yup.object({
  ...tokenAndId,
});

export const resetPasswordSchema = yup.object({
  ...tokenAndId,
  ...password,
});

export const refreshTokenSchema = yup.object({
  refreshToken: yup.string().required("Refresh token is missing"),
});

export const tokenSchema = yup.object({
  token: yup.string().required("Token is missing"),
});

export const forgotPasswordSchema = yup.object({
  ...email,
  captchaToken: yup.string().required("CAPTCHA verification is required"),
});

export const updatePasswordSchema = yup.object({
  newPassword: yup
    .string()
    .required("New password is required")
    .min(8, "Password must be at least 8 characters")
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
      "Password must contain at least one uppercase letter, one lowercase letter, one number and one special character",
    ),
});

export const updatePasswordWhenLoggedInSchema = yup.object({
  currentPassword: yup.string().required("Current password is required"),
  newPassword: yup
    .string()
    .required("New password is required")
    .min(8, "Password must be at least 8 characters")
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
      "Password must contain at least one uppercase letter, one lowercase letter, one number and one special character",
    ),
});
