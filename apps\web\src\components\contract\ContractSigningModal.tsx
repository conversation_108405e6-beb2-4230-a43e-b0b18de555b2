"use client";

import { useState } from "react";
import { trpc } from "@/lib/trpc/client";

import { SerializedContract } from "@repo/server/src/types/contract";

import { Button } from "../ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "../ui/dialog";
import { Checkbox } from "../ui/auth/Checkbox";
import { useToast } from "../ui/toast/use-toast";

interface ContractSigningModalProps {
  isOpen: boolean;
  onClose: () => void;
  contract: SerializedContract;
  onSigningComplete?: () => void;
}

export function ContractSigningModal({
  isOpen,
  onClose,
  contract,
  onSigningComplete,
}: ContractSigningModalProps) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [agreements, setAgreements] = useState({
    termsAccepted: false,
    deliverableCommitment: false,
    paymentTerms: false,
    legalBinding: false,
  });

  const signContractMutation = trpc.contract.sign.useMutation();

  const handleAgreementChange = (field: keyof typeof agreements, checked: boolean) => {
    setAgreements(prev => ({
      ...prev,
      [field]: checked,
    }));
  };

  const allAgreementsChecked = Object.values(agreements).every(Boolean);

  const handleSign = async () => {
    if (!allAgreementsChecked) {
      toast({
        title: "Agreement Required",
        description: "Please check all agreement boxes before signing.",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Get user's IP address and user agent for signature data
      const signatureData = {
        ipAddress: await getUserIpAddress(),
        userAgent: navigator.userAgent,
      };

      await signContractMutation.mutateAsync({
        contractId: contract.id,
        signatureData,
      });

      onSigningComplete?.();
    } catch (error) {
      console.error("Failed to sign contract:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to sign contract. Please try again.";
      toast({
        title: "Signing Failed",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      setAgreements({
        termsAccepted: false,
        deliverableCommitment: false,
        paymentTerms: false,
        legalBinding: false,
      });
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="tw-max-w-[calc(100vw-2rem)] sm:tw-max-w-[calc(100vw-3rem)] lg:tw-max-w-5xl">
        <DialogHeader>
          <DialogTitle>Sign Contract</DialogTitle>
          <DialogDescription>
            Please review and confirm your agreement to the contract terms before signing.
          </DialogDescription>
        </DialogHeader>

        <div className="tw-space-y-6 tw-py-4">
          {/* Contract Summary */}
          <div className="tw-bg-gray-50 tw-p-4 tw-rounded-lg">
            <h4 className="tw-font-medium tw-mb-2 tw-text-black">Contract Summary</h4>
            <div className="tw-grid tw-grid-cols-2 tw-gap-4 tw-text-sm">
              <div>
                <span className="tw-text-gray-600">Contract:</span>
                <p className="tw-font-medium tw-text-black">{contract.contractNumber}</p>
              </div>
              <div>
                <span className="tw-text-gray-600">Total Compensation:</span>
                <p className="tw-font-medium tw-text-green-600">
                  {new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: 'USD',
                  }).format(contract.terms.totalCompensation)}
                </p>
              </div>
              <div>
                <span className="tw-text-gray-600">Deliverables:</span>
                <p className="tw-font-medium tw-text-black">{contract.terms.deliverables.length} items</p>
              </div>
              <div>
                <span className="tw-text-gray-600">Campaign Duration:</span>
                <p className="tw-font-medium tw-text-black">
                  {new Date(contract.terms.campaignDuration.startDate).toLocaleDateString()} - {new Date(contract.terms.campaignDuration.endDate).toLocaleDateString()}
                </p>
              </div>
            </div>
          </div>

          {/* Agreement Checkboxes */}
          <div className="tw-space-y-4">
            <h4 className="tw-font-medium">Please confirm your agreement:</h4>
            
            <div className="tw-space-y-3">
                <Checkbox
                  id="termsAccepted"
                  label="I have read and agree to all terms and conditions outlined in this contract."
                  checked={agreements.termsAccepted}
                  onChange={(checked) => handleAgreementChange('termsAccepted', checked)}
                />
                <Checkbox
                  id="deliverableCommitment"
                  label="I commit to delivering all specified deliverables according to the requirements and deadlines outlined in this contract."
                  checked={agreements.deliverableCommitment}
                  onChange={(checked) => handleAgreementChange('deliverableCommitment', checked)}
                />
                <Checkbox
                  id="paymentTerms"
                  label="I understand and agree to the payment schedule and terms specified in this contract."
                  checked={agreements.paymentTerms}
                  onChange={(checked) => handleAgreementChange('paymentTerms', checked)}
                />
                <Checkbox
                  id="legalBinding"
                  label="I understand that this is a legally binding agreement and that my electronic signature has the same legal effect as a handwritten signature."
                  checked={agreements.legalBinding}
                  onChange={(checked) => handleAgreementChange('legalBinding', checked)}
                />
            </div>
          </div>

          {/* Digital Signature Notice */}
          <div className="tw-bg-blue-50 tw-p-4 tw-rounded-lg tw-border tw-border-blue-200">
            <h4 className="tw-font-medium tw-text-blue-900 tw-mb-2">Digital Signature</h4>
            <p className="tw-text-sm tw-text-blue-800">
              By clicking &quot;Sign Contract&quot; below, you are providing your electronic signature to this contract.
              Your signature will be recorded along with your IP address and timestamp for legal verification.
            </p>
          </div>

          {/* Warning for expired contracts */}
          {contract.expiresAt && new Date(contract.expiresAt) < new Date() && (
            <div className="tw-bg-red-50 tw-p-4 tw-rounded-lg tw-border tw-border-red-200">
              <h4 className="tw-font-medium tw-text-red-900 tw-mb-2">Contract Expired</h4>
              <p className="tw-text-sm tw-text-red-800">
                This contract expired on {new Date(contract.expiresAt).toLocaleDateString()}. 
                Please contact the brand to request a contract extension.
              </p>
            </div>
          )}
        </div>

        <DialogFooter className="tw-space-x-3">
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSign}
            disabled={!allAgreementsChecked || isSubmitting || (!!contract.expiresAt && new Date(contract.expiresAt) < new Date())}
          >
            {isSubmitting ? "Signing..." : "Sign Contract"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

// Helper function to get user's IP address
async function getUserIpAddress(): Promise<string> {
  try {
    const response = await fetch('https://api.ipify.org?format=json');
    const data = await response.json();
    return data.ip || 'Unknown';
  } catch (error) {
    console.warn('Failed to get IP address:', error);
    return 'Unknown';
  }
}
