import { Document, model, Schema } from "mongoose";
import { AthletePayoutRequest, PayoutRequestStatus } from "../types/athlete";

// Document interface for MongoDB
export interface AthletePayoutRequestDocument extends Document {
  _id: string;
  athleteId: Schema.Types.ObjectId;
  amount: number;
  status: PayoutRequestStatus;
  stripePayoutId?: string;
  requestedAt: Date;
  processedAt?: Date;
  failureReason?: string;
  metadata?: {
    stripeConnectAccountId?: string;
    processingFee?: number;
  };
  createdAt: Date;
  updatedAt: Date;
  toClient(): AthletePayoutRequest;
}

// Mongoose schema
const athletePayoutRequestSchema = new Schema<AthletePayoutRequestDocument>(
  {
    athleteId: {
      type: Schema.Types.ObjectId,
      ref: "Athletes",
      required: true,
    },
    amount: {
      type: Number,
      required: true,
      min: 0.01, // Minimum $0.01
    },
    status: {
      type: String,
      enum: Object.values(PayoutRequestStatus),
      default: PayoutRequestStatus.PENDING,
      required: true,
    },
    stripePayoutId: {
      type: String,
    },
    requestedAt: {
      type: Date,
      default: Date.now,
      required: true,
    },
    processedAt: {
      type: Date,
    },
    failureReason: {
      type: String,
    },
    metadata: {
      stripeConnectAccountId: String,
      processingFee: Number,
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Indexes for performance
athletePayoutRequestSchema.index({ athleteId: 1, createdAt: -1 });
athletePayoutRequestSchema.index({ status: 1 });
athletePayoutRequestSchema.index({ stripePayoutId: 1 }, { sparse: true });

// Instance method to convert to client format
athletePayoutRequestSchema.methods.toClient = function(): AthletePayoutRequest {
  return {
    id: this._id.toString(),
    athleteId: this.athleteId,
    amount: this.amount,
    status: this.status,
    stripePayoutId: this.stripePayoutId,
    requestedAt: this.requestedAt,
    processedAt: this.processedAt,
    failureReason: this.failureReason,
    metadata: this.metadata,
  };
};

const AthletePayoutRequestModel = model<AthletePayoutRequestDocument>("AthletePayoutRequest", athletePayoutRequestSchema);
export default AthletePayoutRequestModel;
