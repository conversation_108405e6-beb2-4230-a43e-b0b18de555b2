"use client";

import { format } from "date-fns";
import Link from "next/link";

import { SerializedContract } from "@repo/server/src/types/contract";
import { ContractStatus } from "@repo/server/src/types/contract";

import { Button } from "../ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "../ui/card";
import { Badge } from "../ui/badge";

interface ContractListProps {
  contracts: SerializedContract[];
  isLoading?: boolean;
}

export function ContractList({
  contracts,
  isLoading = false,
}: ContractListProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case "DRAFT":
        return "tw-bg-gray-100 tw-text-gray-800";
      case "PENDING_BRAND_REVIEW":
        return "tw-bg-yellow-100 tw-text-yellow-800";
      case "PENDING_BRAND_APPROVAL":
        return "tw-bg-blue-100 tw-text-blue-800";
      case "PENDING_ATHLETE_SIGNATURE":
        return "tw-bg-purple-100 tw-text-purple-800";
      case "AWAITING_DELIVERABLES":
        return "tw-bg-purple-100 tw-text-purple-800";
      case "AWAITING_BRAND_APPROVAL":
        return "tw-bg-orange-100 tw-text-orange-800";
      case "SIGNED":
        return "tw-bg-green-100 tw-text-green-800";
      case "FULFILLED":
        return "tw-bg-green-100 tw-text-green-800";
      case "CANCELLED":
        return "tw-bg-red-100 tw-text-red-800";
      case "EXPIRED":
        return "tw-bg-gray-100 tw-text-gray-600";
      default:
        return "tw-bg-gray-100 tw-text-gray-800";
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const getStatusLabel = (status: string) => {
    return status.replace(/_/g, ' ');
  };

  if (isLoading) {
    return (
      <div className="tw-space-y-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="tw-animate-pulse">
            <CardHeader>
              <div className="tw-h-4 tw-bg-gray-200 tw-rounded tw-w-3/4"></div>
              <div className="tw-h-3 tw-bg-gray-200 tw-rounded tw-w-1/2"></div>
            </CardHeader>
            <CardContent>
              <div className="tw-space-y-2">
                <div className="tw-h-3 tw-bg-gray-200 tw-rounded"></div>
                <div className="tw-h-3 tw-bg-gray-200 tw-rounded tw-w-2/3"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="tw-space-y-6">
      {/* Contract List */}
      <div className="tw-space-y-4">
        {contracts.length === 0 ? (
          <Card>
            <CardContent className="tw-py-12 tw-text-center">
              <p className="tw-text-gray-500 tw-text-lg">No contracts found</p>
              <p className="tw-text-gray-400 tw-text-sm tw-mt-2">
                Contracts will appear here once you generate them from accepted campaign applications.
              </p>
            </CardContent>
          </Card>
        ) : (
          contracts.map((contract) => (
            <Card key={contract.id} className="tw-hover:shadow-md tw-transition-shadow">
              <CardHeader>
                <div className="tw-flex tw-justify-between tw-items-start">
                  <div className="tw-flex-1">
                    <CardTitle className="tw-text-lg tw-font-semibold">
                      {contract.title.split(' - ')[0]} - <Link href={`/app/campaign/${contract.campaignId}`} className="tw-underline hover:tw-text-aims-primary">{contract.title.split(' - ')[1]}</Link>
                    </CardTitle>
                    <p className="tw-text-sm tw-text-gray-600 tw-mt-1">
                      Contract #{contract.contractNumber} • Version {contract.version}
                    </p>
                  </div>
                  <Badge className={getStatusColor(contract.status)}>
                    {getStatusLabel(contract.status)}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="tw-grid tw-grid-cols-1 md:tw-grid-cols-4 tw-gap-4 tw-mb-4">
                  <div>
                    <p className="tw-text-sm tw-font-medium tw-text-gray-500">Created</p>
                    <p className="tw-text-sm">{format(new Date(contract.createdAt), 'MMM dd, yyyy')}</p>
                  </div>
                  <div>
                    <p className="tw-text-sm tw-font-medium tw-text-gray-500">Campaign Period</p>
                    <p className="tw-text-sm">
                      {format(new Date(contract.terms.campaignDuration.startDate), 'MMM dd')} - {format(new Date(contract.terms.campaignDuration.endDate), 'MMM dd, yyyy')}
                    </p>
                  </div>
                  <div>
                    <p className="tw-text-sm tw-font-medium tw-text-gray-500">Total Value</p>
                    <p className="tw-text-sm tw-font-semibold tw-text-green-600">
                      {formatCurrency(contract.terms.totalCompensation)}
                    </p>
                  </div>
                  <div>
                    <p className="tw-text-sm tw-font-medium tw-text-gray-500">Deliverables</p>
                    <p className="tw-text-sm">{contract.terms.deliverables.length} items</p>
                  </div>
                </div>

                {/* Quick Actions */}
                <div className="tw-flex tw-flex-wrap tw-gap-2 tw-pt-4 tw-border-t">
                  <Link href={`/app/brand/contracts/${contract.id}`}>
                    <Button variant="outline" size="sm">
                      View Details
                    </Button>
                  </Link>
                  
                  {contract.status === ContractStatus.PENDING_BRAND_REVIEW && (
                    <Link href={`/app/brand/contracts/${contract.id}/#review`}>
                      <Button size="sm" className="tw-bg-blue-600 hover:tw-bg-blue-700">
                        Review Contract
                      </Button>
                    </Link>
                  )}
                  
                  {contract.status === ContractStatus.PENDING_BRAND_APPROVAL && (
                    <Link href={`/app/brand/contracts/${contract.id}/#review`}>
                      <Button size="sm" className="tw-bg-green-600 hover:tw-bg-green-700">
                        Approve & Send
                      </Button>
                    </Link>
                  )}
                  
                  {contract.pdfUrl && (
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => window.open(contract.pdfUrl, '_blank')}
                    >
                      View PDF
                    </Button>
                  )}
                  
                  {(contract.status === ContractStatus.PENDING_BRAND_REVIEW || 
                    contract.status === ContractStatus.PENDING_BRAND_APPROVAL) && (
                    <Link href={`/app/brand/contracts/${contract.id}/#review`}>
                      <Button variant="outline" size="sm">
                        Edit
                      </Button>
                    </Link>
                  )}
                </div>

                {/* Expiration Warning */}
                {contract.expiresAt && contract.status === ContractStatus.PENDING_ATHLETE_SIGNATURE && (
                  <div className="tw-mt-4 tw-p-3 tw-bg-yellow-50 tw-border tw-border-yellow-200 tw-rounded-lg">
                    <p className="tw-text-sm tw-text-yellow-800">
                      <span className="tw-font-medium">Expires:</span> {format(new Date(contract.expiresAt), 'MMM dd, yyyy')}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
}
