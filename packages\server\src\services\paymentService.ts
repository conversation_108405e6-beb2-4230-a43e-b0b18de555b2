import Stripe from "stripe";
import { Types } from "mongoose";

import { stripe } from "../lib/stripe";
import ContractModel from "../models/contract";
import BrandModel from "../models/brand";
import AthleteModel from "../models/athlete";
import { CampaignModel } from "../models/campaign";
import { DeliverableSubmissionModel } from "../models/deliverable";
import { PaymentStatus, ContractStatus } from "../types/contract";
import { MessageType, ChatType } from "../types/chat";
import { DeliverableSubmissionStatus } from "../types/deliverableSubmission";
import { ExtendedTRPCError } from "../utils/trpc";
import { createChat, sendMessage } from "../controllers/chat";
import { creditAthleteEarnings, updatePendingEarnings } from "./athletePayoutService";
import { getSocketManager } from "../lib/socket";

export interface CreatePaymentIntentInput {
  contractId: string;
  brandUserId: string;
}

export interface PaymentIntentResult {
  clientSecret: string;
  paymentIntentId: string;
  amount: number;
}

export interface ProcessPaymentResult {
  success: boolean;
  paymentIntentId: string;
  error?: string;
}

// Helper function to send payment notifications
const sendPaymentNotification = async (
  contract: any,
  messageType: MessageType,
  content: string,
) => {
  try {
    console.log(`Starting payment notification for contract ${contract._id}, messageType: ${messageType}`);

    // Get brand and athlete documents
    const brand = await BrandModel.findById(contract.brandId);
    const athlete = await AthleteModel.findById(contract.athleteId);

    if (!brand || !athlete) {
      console.error("Failed to find brand or athlete for payment notification", {
        brandId: contract.brandId,
        athleteId: contract.athleteId,
        foundBrand: !!brand,
        foundAthlete: !!athlete
      });
      return;
    }

    // Get the brand and athlete user IDs
    const brandUserId = brand.userId.toString();
    const athleteUserId = athlete.userId.toString();

    console.log(`Brand userId: ${brandUserId}, Athlete userId: ${athleteUserId}`);

    // Create or get the chat between brand and athlete
    const chat = await createChat(
      brandUserId,
      [athleteUserId],
      ChatType.DIRECT,
      contract.campaignId?.toString()
    );

    console.log(`Created/found chat: ${chat.id}`);

    // Send notification to both brand and athlete
    await sendMessage(
      chat.id,
      brandUserId, // Brand sends the notification
      content,
      messageType,
      contract.campaignId?.toString(),
      contract._id.toString()
    );

    console.log(`Payment notification sent successfully - messageType: ${messageType}, content: ${content}`);
  } catch (error) {
    // Log the error but don't fail the payment operation
    console.error("Failed to send payment notification:", error);
  }
};

/**
 * Creates a Stripe payment intent for a contract
 */
export const createContractPaymentIntent = async (
  input: CreatePaymentIntentInput
): Promise<PaymentIntentResult> => {
  console.log(`[Payment Service] Creating payment intent for contract ${input.contractId}`);

  // Fetch the contract
  const contract = await ContractModel.findById(input.contractId);
  if (!contract) {
    throw new ExtendedTRPCError("NOT_FOUND", "Contract not found");
  }

  // Verify the brand owns this contract
  const brand = await BrandModel.findOne({ userId: input.brandUserId });
  if (!brand || contract.brandId.toString() !== brand._id.toString()) {
    throw new ExtendedTRPCError("FORBIDDEN", "Access denied to this contract");
  }

  // Check if contract is in the correct status for payment
  if (contract.status !== ContractStatus.BRAND_SIGNED && contract.status !== ContractStatus.PENDING_PAYMENT) {
    throw new ExtendedTRPCError(
      "BAD_REQUEST",
      "Contract must be signed by brand before payment can be processed"
    );
  }

  // Check if payment intent already exists
  if (contract.paymentIntentId) {
    try {
      const existingIntent = await stripe.paymentIntents.retrieve(contract.paymentIntentId);

      // Only reuse payment intent if it's in a state that allows payment
      if (existingIntent.status === "requires_payment_method" ||
          existingIntent.status === "requires_confirmation") {
        console.log(`[Payment Service] Returning existing payment intent ${existingIntent.id} with status ${existingIntent.status}`);
        return {
          clientSecret: existingIntent.client_secret!,
          paymentIntentId: existingIntent.id,
          amount: existingIntent.amount,
        };
      } else {
        console.log(`[Payment Service] Existing payment intent ${existingIntent.id} has status ${existingIntent.status}, creating new one`);
        // Cancel the old payment intent if it's in a cancelable state
        if (existingIntent.status === "requires_action") {
          try {
            await stripe.paymentIntents.cancel(existingIntent.id);
            console.log(`[Payment Service] Cancelled old payment intent ${existingIntent.id}`);
          } catch (cancelError) {
            console.warn(`[Payment Service] Failed to cancel old payment intent: ${cancelError}`);
          }
        }
      }
    } catch (error) {
      console.warn(`[Payment Service] Existing payment intent ${contract.paymentIntentId} not found or invalid: ${error}`);
      // Clear the invalid payment intent ID from the contract
      await ContractModel.findByIdAndUpdate(contract._id, {
        paymentIntentId: undefined,
        paymentStatus: PaymentStatus.PENDING,
      });
      console.log(`[Payment Service] Cleared invalid payment intent ID from contract ${contract._id}`);
    }
  }

  // Fetch the campaign to get the total price (including 10% AIMS fee + processing fee)
  const campaign = await CampaignModel.findById(contract.campaignId);
  if (!campaign) {
    throw new ExtendedTRPCError("NOT_FOUND", "Campaign not found for contract");
  }

  // Calculate the total amount (in cents) - use campaign price which includes 10% AIMS fee + processing fee
  const amountInCents = Math.round(campaign.price * 100);

  try {
    // Create new payment intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount: amountInCents,
      currency: "usd",
      metadata: {
        contractId: contract._id.toString(),
        brandId: brand._id.toString(),
        contractNumber: contract.contractNumber,
      },
      description: `Payment for contract ${contract.contractNumber} - ${contract.title}`,
    });

    // Update contract with payment intent details
    // Note: Don't change status here as it should already be PENDING_PAYMENT from signing
    await ContractModel.findByIdAndUpdate(contract._id, {
      paymentIntentId: paymentIntent.id,
      paymentStatus: PaymentStatus.PENDING,
      paymentInitiatedAt: new Date(),
    });

    console.log(`[Payment Service] Created payment intent ${paymentIntent.id} for contract ${contract._id}`);

    return {
      clientSecret: paymentIntent.client_secret!,
      paymentIntentId: paymentIntent.id,
      amount: amountInCents,
    };
  } catch (error) {
    console.error(`[Payment Service] Failed to create payment intent:`, error);
    throw new ExtendedTRPCError(
      "INTERNAL_SERVER_ERROR",
      "Failed to create payment intent"
    );
  }
};

/**
 * Processes a successful payment confirmation
 */
export const processPaymentSuccess = async (
  paymentIntentId: string
): Promise<ProcessPaymentResult> => {
  console.log(`[Payment Service] Processing successful payment for intent ${paymentIntentId}`);

  try {
    // Find the contract by payment intent ID
    const contract = await ContractModel.findOne({ paymentIntentId });
    if (!contract) {
      console.error(`[Payment Service] Contract not found for payment intent ${paymentIntentId}`);
      return {
        success: false,
        paymentIntentId,
        error: "Contract not found",
      };
    }

    // Update contract status to awaiting deliverables (automatic transition from paid)
    await ContractModel.findByIdAndUpdate(contract._id, {
      status: ContractStatus.AWAITING_DELIVERABLES,
      paymentStatus: PaymentStatus.SUCCEEDED,
      paymentCompletedAt: new Date(),
      paymentFailedAt: undefined,
      paymentFailureReason: undefined,
    });

    console.log(`[Payment Service] Contract ${contract._id} payment completed, now awaiting deliverables`);

    // Initialize deliverable submissions with AWAITING_SUBMISSION status
    try {
      await initializeDeliverableSubmissions(contract._id.toString());
    } catch (error) {
      console.error(`[Payment Service] Failed to initialize deliverable submissions for contract ${contract._id}:`, error);
      // Don't fail the payment process if submission initialization fails
    }

    // Send payment success notification
    await sendPaymentNotification(
      contract,
      MessageType.CONTRACT_PAYMENT_COMPLETED,
      `🎉 Payment completed successfully for contract: ${contract.title}! You can now start working on the deliverables. Check the contract details for requirements and deadlines. You will receive payment after deliverables are submitted and approved according to the contract payment schedule.`
    );

    // Emit socket event for real-time contract status update
    const socketManager = getSocketManager();
    if (socketManager) {
      const updatedContract = await ContractModel.findById(contract._id)
        .populate('brandId', 'userId')
        .populate('athleteId', 'userId');

      if (updatedContract) {
        const contractData = updatedContract.toClient();

        // Emit to brand user
        if (updatedContract.brandId && (updatedContract.brandId as any).userId) {
          socketManager.emitToUser((updatedContract.brandId as any).userId.toString(), 'contractStatusUpdated', {
            contractId: contract._id.toString(),
            status: ContractStatus.AWAITING_DELIVERABLES,
            contract: contractData
          });
        }

        // Emit to athlete user
        if (updatedContract.athleteId && (updatedContract.athleteId as any).userId) {
          socketManager.emitToUser((updatedContract.athleteId as any).userId.toString(), 'contractStatusUpdated', {
            contractId: contract._id.toString(),
            status: ContractStatus.AWAITING_DELIVERABLES,
            contract: contractData
          });
        }
      }
    }

    return {
      success: true,
      paymentIntentId,
    };
  } catch (error) {
    console.error(`[Payment Service] Failed to process payment success:`, error);
    return {
      success: false,
      paymentIntentId,
      error: "Failed to update contract status",
    };
  }
};

/**
 * Processes a failed payment
 */
export const processPaymentFailure = async (
  paymentIntentId: string,
  failureReason: string
): Promise<ProcessPaymentResult> => {
  console.log(`[Payment Service] Processing failed payment for intent ${paymentIntentId}`);

  try {
    // Find the contract by payment intent ID
    const contract = await ContractModel.findOne({ paymentIntentId });
    if (!contract) {
      console.error(`[Payment Service] Contract not found for payment intent ${paymentIntentId}`);
      return {
        success: false,
        paymentIntentId,
        error: "Contract not found",
      };
    }

    // Update contract with failure details
    const retryCount = (contract.paymentRetryCount || 0) + 1;
    await ContractModel.findByIdAndUpdate(contract._id, {
      paymentStatus: PaymentStatus.FAILED,
      paymentFailedAt: new Date(),
      paymentFailureReason: failureReason,
      paymentRetryCount: retryCount,
    });

    console.log(`[Payment Service] Contract ${contract._id} payment failed (attempt ${retryCount})`);

    // Send payment failure notification
    await sendPaymentNotification(
      contract,
      MessageType.CONTRACT_PAYMENT_FAILED,
      `Payment failed for contract: ${contract.title}. Reason: ${failureReason}`
    );

    return {
      success: true,
      paymentIntentId,
    };
  } catch (error) {
    console.error(`[Payment Service] Failed to process payment failure:`, error);
    return {
      success: false,
      paymentIntentId,
      error: "Failed to update contract status",
    };
  }
};

/**
 * Retrieves payment status for a contract
 */
export const getContractPaymentStatus = async (
  contractId: string,
  userId: string
): Promise<{
  paymentStatus?: PaymentStatus;
  paymentIntentId?: string;
  amount?: number;
  canRetryPayment: boolean;
}> => {
  const contract = await ContractModel.findById(contractId);
  if (!contract) {
    throw new ExtendedTRPCError("NOT_FOUND", "Contract not found");
  }

  // Verify user has access to this contract
  const brand = await BrandModel.findOne({ userId });
  if (!brand || contract.brandId.toString() !== brand._id.toString()) {
    throw new ExtendedTRPCError("FORBIDDEN", "Access denied to this contract");
  }

  const canRetryPayment = contract.paymentStatus === PaymentStatus.FAILED &&
                         (contract.paymentRetryCount || 0) < 3;

  // Fetch the campaign to get the total price (including 10% AIMS fee + processing fee)
  const campaign = await CampaignModel.findById(contract.campaignId);
  if (!campaign) {
    throw new ExtendedTRPCError("NOT_FOUND", "Campaign not found for contract");
  }

  return {
    paymentStatus: contract.paymentStatus,
    paymentIntentId: contract.paymentIntentId,
    amount: campaign.price, // Use campaign price which includes 10% AIMS fee + processing fee
    canRetryPayment,
  };
};

/**
 * Initialize deliverable submissions with AWAITING_SUBMISSION status
 * when contract transitions to AWAITING_DELIVERABLES
 */
export const initializeDeliverableSubmissions = async (
  contractId: string
): Promise<void> => {
  const contract = await ContractModel.findById(contractId);
  if (!contract) {
    throw new ExtendedTRPCError("NOT_FOUND", "Contract not found");
  }

  // Get athlete ID
  const athleteId = contract.athleteId;
  const campaignId = contract.campaignId;

  // Create placeholder submissions for each deliverable in the contract
  const submissionPromises = contract.terms.deliverables.map(async (deliverable) => {
    // Check if submission already exists
    const existingSubmission = await DeliverableSubmissionModel.findOne({
      campaignId,
      deliverableId: deliverable.deliverableId,
      athleteId,
    });

    // Only create if no submission exists
    if (!existingSubmission) {
      await DeliverableSubmissionModel.create({
        campaignId,
        deliverableId: deliverable.deliverableId,
        athleteId,
        description: "", // Empty description for placeholder
        files: [],
        status: DeliverableSubmissionStatus.AWAITING_SUBMISSION,
        submittedAt: new Date(), // Set to current time for sorting purposes
      });
    }
  });

  await Promise.all(submissionPromises);
  console.log(`[Payment Service] Initialized deliverable submissions for contract ${contractId}`);
};

/**
 * Marks a contract as fulfilled after deliverables are completed
 */
export const markContractFulfilled = async (
  contractId: string,
  userId: string
): Promise<void> => {
  const contract = await ContractModel.findById(contractId);
  if (!contract) {
    throw new ExtendedTRPCError("NOT_FOUND", "Contract not found");
  }

  // Verify contract is in a status that can be fulfilled
  if (contract.status !== ContractStatus.AWAITING_DELIVERABLES && contract.status !== ContractStatus.AWAITING_BRAND_APPROVAL) {
    throw new ExtendedTRPCError(
      "BAD_REQUEST",
      "Contract must be in awaiting deliverables or awaiting brand approval status before it can be marked as fulfilled"
    );
  }

  // Update contract status
  await ContractModel.findByIdAndUpdate(contractId, {
    status: ContractStatus.FULFILLED,
    fulfilledAt: new Date(),
  });

  // Credit athlete earnings and update pending earnings
  try {
    await updatePendingEarnings(contractId, false); // Remove from pending
    await creditAthleteEarnings(contractId); // Credit available balance
    console.log(`[Payment Service] Athlete earnings processed for contract ${contractId}`);
  } catch (error) {
    console.error(`[Payment Service] Failed to process athlete earnings for contract ${contractId}:`, error);
    // Don't throw error here - contract fulfillment should still succeed even if payout fails
  }

  console.log(`[Payment Service] Contract ${contractId} marked as fulfilled`);
};

/**
 * Retries a failed payment by creating a new payment intent
 */
export const retryContractPayment = async (
  input: CreatePaymentIntentInput
): Promise<PaymentIntentResult> => {
  console.log(`[Payment Service] Retrying payment for contract ${input.contractId}`);

  // Fetch the contract
  const contract = await ContractModel.findById(input.contractId);
  if (!contract) {
    throw new ExtendedTRPCError("NOT_FOUND", "Contract not found");
  }

  // Verify the brand owns this contract
  const brand = await BrandModel.findOne({ userId: input.brandUserId });
  if (!brand || contract.brandId.toString() !== brand._id.toString()) {
    throw new ExtendedTRPCError("FORBIDDEN", "Access denied to this contract");
  }

  // Check if payment can be retried
  if (contract.paymentStatus !== PaymentStatus.FAILED) {
    throw new ExtendedTRPCError(
      "BAD_REQUEST",
      "Payment can only be retried for failed payments"
    );
  }

  const retryCount = (contract.paymentRetryCount || 0);
  if (retryCount >= 3) {
    throw new ExtendedTRPCError(
      "BAD_REQUEST",
      "Maximum retry attempts exceeded. Please contact support."
    );
  }

  // Cancel the old payment intent if it exists
  if (contract.paymentIntentId) {
    try {
      await stripe.paymentIntents.cancel(contract.paymentIntentId);
      console.log(`[Payment Service] Cancelled old payment intent ${contract.paymentIntentId}`);
    } catch (error) {
      console.warn(`[Payment Service] Failed to cancel old payment intent: ${error}`);
    }
  }

  // Fetch the campaign to get the total price (including 10% AIMS fee + processing fee)
  const campaign = await CampaignModel.findById(contract.campaignId);
  if (!campaign) {
    throw new ExtendedTRPCError("NOT_FOUND", "Campaign not found for contract");
  }

  // Calculate the total amount (in cents) - use campaign price which includes 10% AIMS fee + processing fee
  const amountInCents = Math.round(campaign.price * 100);

  try {
    // Create new payment intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount: amountInCents,
      currency: "usd",
      metadata: {
        contractId: contract._id.toString(),
        brandId: brand._id.toString(),
        contractNumber: contract.contractNumber,
        retryAttempt: (retryCount + 1).toString(),
      },
      description: `Payment retry ${retryCount + 1} for contract ${contract.contractNumber} - ${contract.title}`,
    });

    // Update contract with new payment intent details
    await ContractModel.findByIdAndUpdate(contract._id, {
      paymentIntentId: paymentIntent.id,
      paymentStatus: PaymentStatus.PENDING,
      paymentInitiatedAt: new Date(),
      paymentFailedAt: undefined,
      paymentFailureReason: undefined,
    });

    console.log(`[Payment Service] Created retry payment intent ${paymentIntent.id} for contract ${contract._id} (attempt ${retryCount + 1})`);

    return {
      clientSecret: paymentIntent.client_secret!,
      paymentIntentId: paymentIntent.id,
      amount: amountInCents,
    };
  } catch (error) {
    console.error(`[Payment Service] Failed to create retry payment intent:`, error);
    throw new ExtendedTRPCError(
      "INTERNAL_SERVER_ERROR",
      "Failed to create retry payment intent"
    );
  }
};

/**
 * Synchronizes payment status with Stripe
 */
export const syncPaymentStatus = async (
  contractId: string,
  userId: string
): Promise<{
  paymentStatus: PaymentStatus;
  paymentIntentStatus?: string;
  lastSyncAt: string;
}> => {
  console.log(`[Payment Service] Syncing payment status for contract ${contractId}`);

  const contract = await ContractModel.findById(contractId);
  if (!contract) {
    throw new ExtendedTRPCError("NOT_FOUND", "Contract not found");
  }

  // Verify user has access to this contract
  const brand = await BrandModel.findOne({ userId });
  if (!brand || contract.brandId.toString() !== brand._id.toString()) {
    throw new ExtendedTRPCError("FORBIDDEN", "Access denied to this contract");
  }

  if (!contract.paymentIntentId) {
    return {
      paymentStatus: contract.paymentStatus || PaymentStatus.PENDING,
      lastSyncAt: new Date().toISOString(),
    };
  }

  try {
    // Fetch payment intent from Stripe
    const paymentIntent = await stripe.paymentIntents.retrieve(contract.paymentIntentId);

    let newPaymentStatus: PaymentStatus;
    switch (paymentIntent.status) {
      case "succeeded":
        newPaymentStatus = PaymentStatus.SUCCEEDED;
        break;
      case "processing":
        newPaymentStatus = PaymentStatus.PROCESSING;
        break;
      case "requires_payment_method":
      case "requires_confirmation":
        newPaymentStatus = PaymentStatus.PENDING;
        break;
      case "requires_action":
        newPaymentStatus = PaymentStatus.REQUIRES_ACTION;
        break;
      case "canceled":
        newPaymentStatus = PaymentStatus.CANCELLED;
        break;
      default:
        newPaymentStatus = PaymentStatus.FAILED;
    }

    // Update contract if status has changed
    if (contract.paymentStatus !== newPaymentStatus) {
      const updateData: any = {
        paymentStatus: newPaymentStatus,
      };

      if (newPaymentStatus === PaymentStatus.SUCCEEDED && contract.status !== ContractStatus.AWAITING_DELIVERABLES) {
        updateData.status = ContractStatus.AWAITING_DELIVERABLES;
        updateData.paymentCompletedAt = new Date();
      }

      await ContractModel.findByIdAndUpdate(contractId, updateData);
      console.log(`[Payment Service] Updated contract ${contractId} payment status to ${newPaymentStatus}`);

      // Initialize deliverable submissions if contract just transitioned to AWAITING_DELIVERABLES
      if (newPaymentStatus === PaymentStatus.SUCCEEDED && contract.status !== ContractStatus.AWAITING_DELIVERABLES) {
        try {
          await initializeDeliverableSubmissions(contractId);
        } catch (error) {
          console.error(`[Payment Service] Failed to initialize deliverable submissions for contract ${contractId}:`, error);
          // Don't fail the sync process if submission initialization fails
        }
      }

      // Send payment completion notification if payment just succeeded
      if (newPaymentStatus === PaymentStatus.SUCCEEDED && contract.paymentStatus !== PaymentStatus.SUCCEEDED) {
        await sendPaymentNotification(
          contract,
          MessageType.CONTRACT_PAYMENT_COMPLETED,
          `🎉 Payment completed! You can now start working on the deliverables for: ${contract.title}. Check the contract details for requirements and deadlines.`
        );

        // Emit socket event for real-time contract status update
        const socketManager = getSocketManager();
        if (socketManager) {
          const updatedContract = await ContractModel.findById(contractId)
            .populate('brandId', 'userId')
            .populate('athleteId', 'userId');

          if (updatedContract) {
            const contractData = updatedContract.toClient();

            // Emit to brand user
            if (updatedContract.brandId && (updatedContract.brandId as any).userId) {
              socketManager.emitToUser((updatedContract.brandId as any).userId.toString(), 'contractStatusUpdated', {
                contractId: contractId,
                status: ContractStatus.AWAITING_DELIVERABLES,
                contract: contractData
              });
            }

            // Emit to athlete user
            if (updatedContract.athleteId && (updatedContract.athleteId as any).userId) {
              socketManager.emitToUser((updatedContract.athleteId as any).userId.toString(), 'contractStatusUpdated', {
                contractId: contractId,
                status: ContractStatus.AWAITING_DELIVERABLES,
                contract: contractData
              });
            }
          }
        }
      }
    }

    return {
      paymentStatus: newPaymentStatus,
      paymentIntentStatus: paymentIntent.status,
      lastSyncAt: new Date().toISOString(),
    };
  } catch (error) {
    console.error(`[Payment Service] Failed to sync payment status:`, error);
    throw new ExtendedTRPCError(
      "INTERNAL_SERVER_ERROR",
      "Failed to sync payment status"
    );
  }
};
