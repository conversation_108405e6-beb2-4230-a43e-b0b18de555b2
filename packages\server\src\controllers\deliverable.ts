import { ExtendedTRPCError } from "../utils/trpc";
import DeliverableModel, { Deliverable } from "../models/deliverable";

export const getDeliverablesByIds = async (deliverableIds: string[]): Promise<Deliverable[]> => {
  if (!deliverableIds || deliverableIds.length === 0) {
    return [];
  }

  try {
    const deliverables = await DeliverableModel.find({
      _id: { $in: deliverableIds }
    });

    // Return deliverables in the same order as requested IDs
    const deliverableMap = new Map(deliverables.map(d => [d._id.toString(), d.toClient()]));
    return deliverableIds.map(id => deliverableMap.get(id)).filter(Boolean) as Deliverable[];
  } catch (error) {
    console.error("Error fetching deliverables:", error);
    throw new ExtendedTRPCError("INTERNAL_SERVER_ERROR", "Failed to fetch deliverables");
  }
};
