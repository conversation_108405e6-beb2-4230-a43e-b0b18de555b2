import * as React from "react";
import { cn } from "@/lib/utils";

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {}

const baseInputClasses =
  "tw-bg-aims-dark-3 tw-text-aims-text-primary tw-border-aims-dark-3 tw-flex tw-h-10 tw-w-full tw-rounded-md tw-border tw-border-input tw-px-3 tw-py-2 tw-text-sm tw-ring-offset-background file:tw-border-0 file:tw-bg-transparent file:tw-text-sm file:tw-font-medium placeholder:tw-text-muted-foreground focus-visible:tw-outline-none focus-visible:tw-ring-2 focus-visible:tw-ring-ring focus-visible:tw-ring-offset-2 disabled:tw-cursor-not-allowed disabled:tw-opacity-50";

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, ...props }, ref) => {
    const inputClassName = React.useMemo(
      () => cn(baseInputClasses, className),
      [className],
    );

    return (
      <input type={type} className={inputClassName} ref={ref} {...props} />
    );
  },
);
Input.displayName = "Input";

export { Input };
