import React from "react";

import { EmailTemplate } from "./EmailTemplate";

interface PasswordResetSuccessfulProps {
  email: string;
}

export const PasswordResetSuccessful: React.FC<
  PasswordResetSuccessfulProps
> = ({ email }) => {
  return (
    <EmailTemplate email={email}>
      <div style={{ width: "auto", borderRadius: "10px" }}>
        <div style={{ width: "100%" }}>
          <h3>Your Password has Been Reset</h3>
          <p style={{ color: "#64758b" }}>
            The password for the account associated with the following email has
            been reset:
          </p>
        </div>

        <div
          style={{
            width: "100%",
            textAlign: "center",
            borderRadius: "10px",
            borderWidth: "1px",
            borderStyle: "solid",
          }}
        >
          <p>
            <a rel="nofollow" style={{ color: "white" }}>{email}</a>
          </p>
        </div>

        <div style={{ width: "100%" }}>
          <p style={{ color: "#64758b" }}>
            <strong>
              If you did not reset your password, please contact us using the
              link below.
            </strong>
          </p>
          <br />
          <p style={{ width: "100%", textAlign: "center" }}>
            <a
              href="https://aimsmarketing.ai/"
              style={{ color: "#6366f1", textDecoration: "none" }}
            >
              <strong>Contact Us</strong>
            </a>
          </p>
        </div>
      </div>
    </EmailTemplate>
  );
};
