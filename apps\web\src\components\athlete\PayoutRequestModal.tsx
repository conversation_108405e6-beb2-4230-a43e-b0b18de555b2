"use client";

import { useState } from "react";
import { trpc } from "@/lib/trpc/client";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { useToast } from "../ui/toast/use-toast";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "../ui/dialog";

import { InformationCircleIcon } from "@heroicons/react/24/outline";

interface PayoutRequestModalProps {
  availableBalance: number;
  onClose: () => void;
  onSuccess: () => void;
}

const MINIMUM_PAYOUT = 5;

export default function PayoutRequestModal({
  availableBalance,
  onClose,
  onSuccess,
}: PayoutRequestModalProps) {
  const { toast } = useToast();
  const [amount, setAmount] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const createPayoutMutation = trpc.athlete.createPayoutRequest.useMutation();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const payoutAmount = parseFloat(amount);
    
    // Validation
    if (isNaN(payoutAmount) || payoutAmount <= 0) {
      toast({
        title: "Invalid Amount",
        description: "Please enter a valid amount.",
        variant: "destructive",
      });
      return;
    }

    if (payoutAmount < MINIMUM_PAYOUT) {
      toast({
        title: "Amount Too Low",
        description: `Minimum payout amount is $${MINIMUM_PAYOUT}.`,
        variant: "destructive",
      });
      return;
    }

    if (payoutAmount > availableBalance) {
      toast({
        title: "Insufficient Balance",
        description: `You can only request up to $${availableBalance.toFixed(2)}.`,
        variant: "destructive",
      });
      return;
    }

    try {
      setIsSubmitting(true);
      await createPayoutMutation.mutateAsync({ amount: payoutAmount });
      onSuccess();
    } catch (error: any) {
      console.error("Payout request failed:", error);
      toast({
        title: "Payout Request Failed",
        description: error.message || "An error occurred while requesting your payout.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    // Allow only numbers and decimal point
    if (value === "" || /^\d*\.?\d*$/.test(value)) {
      setAmount(value);
    }
  };

  const setMaxAmount = () => {
    setAmount(availableBalance.toFixed(2));
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="tw-w-full tw-max-w-md tw-mx-4 sm:tw-mx-auto">
        <DialogHeader>
          <DialogTitle>Request Payout</DialogTitle>
          <DialogDescription>
            Request a payout from your available balance. Payouts are typically processed within 1-2 business days.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="tw-space-y-4">
          <div className="tw-p-3 tw-rounded-lg tw-bg-blue-50 tw-border tw-border-blue-200 tw-text-sm tw-text-blue-800 tw-flex tw-items-start tw-gap-2">
            <InformationCircleIcon className="tw-h-4 tw-w-4 tw-mt-0.5 tw-flex-shrink-0" />
            <div>
              Available balance: <strong>${availableBalance.toFixed(2)}</strong>
              <br />
              Minimum payout: <strong>${MINIMUM_PAYOUT}</strong>
            </div>
          </div>

          <div className="tw-space-y-2">
            <Label htmlFor="amount">Payout Amount</Label>
            <div className="tw-relative">
              <div className="tw-absolute tw-inset-y-0 tw-left-0 tw-pl-3 tw-flex tw-items-center tw-pointer-events-none">
                <span className="tw-text-aims-text-secondary tw-text-sm">$</span>
              </div>
              <Input
                id="amount"
                type="text"
                value={amount}
                onChange={handleAmountChange}
                placeholder="0.00"
                className="tw-pl-8 tw-pr-16 !tw-bg-aims-dark-2"
                required
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={setMaxAmount}
                className="tw-absolute tw-inset-y-0 tw-right-0 tw-px-3 tw-text-xs tw-text-aims-primary hover:tw-text-aims-primary-dark"
              >
                Max
              </Button>
            </div>
          </div>

          <div className="tw-text-xs tw-text-aims-text-secondary tw-space-y-1">
            <p>• Payouts are processed to your connected bank account</p>
            <p>• Processing time: 1-2 business days</p>
            <p>• You&apos;ll receive an email confirmation when processed</p>
          </div>

          <DialogFooter className="tw-flex sm:tw-flex-row tw-flex-justify-between tw-gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
              className="tw-h-10 sm:tw-h-9 tw-text-aims-text-primary"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting || !amount || parseFloat(amount) < MINIMUM_PAYOUT}
              className="tw-h-10 sm:tw-h-9"
            >
              {isSubmitting ? "Requesting..." : "Request Payout"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
