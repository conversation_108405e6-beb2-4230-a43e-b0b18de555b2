"use client";

import { useRef, useState } from "react";
import Link from "next/link";
import { FormInput } from "@/components/ui/auth/FormInput";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/toast/use-toast";
import { useAuth } from "@/hooks/use-auth";
import { ArrowPathIcon } from "@heroicons/react/24/outline";
import ReCA<PERSON><PERSON><PERSON> from "react-google-recaptcha";

const RECAPTCHA_SITE_KEY = process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY;
if (!RECAPTCHA_SITE_KEY) {
  console.error("NEXT_PUBLIC_RECAPTCHA_SITE_KEY is not defined!");
}

export default function ForgotPassword() {
  const [email, setEmail] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const recaptchaRef = useRef<ReCAPTCHA>(null);
  const { forgotPassword } = useAuth();
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email.trim()) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Email is required",
      });
      return;
    }

    const captchaToken = await recaptchaRef.current?.executeAsync();
    if (!captchaToken) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Please complete the CAPTCHA",
      });
      return;
    }

    setIsLoading(true);

    try {
      await forgotPassword(email, captchaToken);
      toast({
        variant: "success",
        title: "Success",
        description: "Password reset link has been sent to your email",
      });
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: (error as Error).message,
      });
      recaptchaRef.current?.reset();
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="tw-w-full">
      <div className="tw-max-w-md tw-mx-auto">
        <h3 className="tw-text-h4 tw-text-center">Forgot Password</h3>
        <p className="tw-mt-2 tw-text-center tw-text-aims-text-secondary">
          Enter your email address and we&apos;ll send you a link to reset your
          password.
        </p>

        <form onSubmit={handleSubmit} className="tw-mt-8 tw-space-y-6">
          <FormInput
            id="email"
            label="Email Address"
            type="email"
            value={email}
            onChange={setEmail}
            required
            disabled={isLoading}
            placeholder="<EMAIL>"
          />

          <ReCAPTCHA
            ref={recaptchaRef}
            size="invisible"
            sitekey={RECAPTCHA_SITE_KEY || ""}
          />

          <Button type="submit" disabled={isLoading} className="tw-w-full">
            {isLoading ? (
              <>
                <ArrowPathIcon className="tw-h-4 tw-w-4 tw-mr-2 tw-animate-spin" />
                Sending...
              </>
            ) : (
              "Send Reset Link"
            )}
          </Button>

          <div className="tw-text-right">
            <Link
              href="/auth/login"
              className="tw-text-sm tw-text-aims-primary hover:tw-text-aims-primary/90"
            >
              Back to Sign In
            </Link>
          </div>
        </form>
      </div>
    </div>
  );
}
