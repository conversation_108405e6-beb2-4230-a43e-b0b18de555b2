export enum DeliverableSubmissionStatus {
  AWAITING_SUBMISSION = "AWAITING_SUBMISSION",
  PENDING = "PENDING",
  APPROVED = "APPROVED", 
  REJECTED = "REJECTED",
  NEEDS_REVISION = "NEEDS_REVISION",
}

export interface DeliverableSubmissionFile {
  url: string;
  originalName: string;
  fileType: string;
  fileSize: number;
}

export interface DeliverableSubmission {
  id: string;
  campaignId: string;
  deliverableId: string;
  athleteId: string;
  description: string;
  files: DeliverableSubmissionFile[];
  status: DeliverableSubmissionStatus;
  submittedAt: string;
  reviewedAt?: string;
  reviewedBy?: string; // Brand user ID
  feedback?: string;
  createdAt: string;
  updatedAt: string;
}

export interface SerializedDeliverableSubmission extends DeliverableSubmission {
  campaign?: {
    id: string;
    name: string;
  };
  deliverable?: {
    id: string;
    name: string;
    type: string;
  };
  athlete?: {
    id: string;
    name: string;
    profilePicture?: string;
  };
  reviewer?: {
    id: string;
    name: string;
    companyName?: string;
  };
}

// Input types for API
export interface DeliverableSubmissionInput {
  campaignId: string;
  deliverableId: string;
  description: string;
  files?: DeliverableSubmissionFile[];
}

export interface DeliverableSubmissionReviewInput {
  submissionId: string;
  status: DeliverableSubmissionStatus;
  feedback?: string;
}
