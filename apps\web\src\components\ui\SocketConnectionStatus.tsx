"use client";

import { useEffect, useState } from "react";
import {
  ExclamationCircleIcon,
  CheckCircleIcon,
  ArrowPathIcon,
  SignalSlashIcon
} from "@heroicons/react/24/outline";
import { SocketConnectionStatus, SocketConnectionState } from "@/types/socket";
import { Button } from "./button";

interface SocketConnectionStatusProps {
  connectionState: SocketConnectionState;
  onReconnect?: () => void;
  onClearError?: () => void;
  className?: string;
  showDetails?: boolean;
}

export function SocketConnectionStatusIndicator({
  connectionState,
  onReconnect,
  onClearError,
  className = "",
  showDetails = false,
}: SocketConnectionStatusProps) {
  const [showAlert, setShowAlert] = useState(false);

  // Show alert for important status changes
  useEffect(() => {
    if (
      connectionState.status === SocketConnectionStatus.AUTH_ERROR ||
      connectionState.status === SocketConnectionStatus.CONNECTION_ERROR ||
      (connectionState.status === SocketConnectionStatus.RECONNECTING && connectionState.reconnectAttempts > 2)
    ) {
      setShowAlert(true);
    } else if (connectionState.status === SocketConnectionStatus.CONNECTED) {
      setShowAlert(false);
    }
  }, [connectionState.status, connectionState.reconnectAttempts]);

  const getStatusIcon = () => {
    switch (connectionState.status) {
      case SocketConnectionStatus.CONNECTED:
        return <CheckCircleIcon className="tw-w-4 tw-h-4 tw-text-green-500" />;
      case SocketConnectionStatus.CONNECTING:
      case SocketConnectionStatus.TOKEN_REFRESHING:
        return <ArrowPathIcon className="tw-w-4 tw-h-4 tw-text-blue-500 tw-animate-spin" />;
      case SocketConnectionStatus.RECONNECTING:
        return <ArrowPathIcon className="tw-w-4 tw-h-4 tw-text-yellow-500 tw-animate-spin" />;
      case SocketConnectionStatus.AUTH_ERROR:
      case SocketConnectionStatus.CONNECTION_ERROR:
        return <ExclamationCircleIcon className="tw-w-4 tw-h-4 tw-text-red-500" />;
      case SocketConnectionStatus.DISCONNECTED:
      default:
        return <SignalSlashIcon className="tw-w-4 tw-h-4 tw-text-gray-500" />;
    }
  };

  const getStatusText = () => {
    switch (connectionState.status) {
      case SocketConnectionStatus.CONNECTED:
        return "Connected";
      case SocketConnectionStatus.CONNECTING:
        return "Connecting...";
      case SocketConnectionStatus.RECONNECTING:
        return `Reconnecting... (${connectionState.reconnectAttempts}/${connectionState.maxReconnectAttempts})`;
      case SocketConnectionStatus.TOKEN_REFRESHING:
        return "Refreshing authentication...";
      case SocketConnectionStatus.AUTH_ERROR:
        return "Authentication error";
      case SocketConnectionStatus.CONNECTION_ERROR:
        return "Connection error";
      case SocketConnectionStatus.DISCONNECTED:
      default:
        return "Disconnected";
    }
  };

  const getStatusColor = () => {
    switch (connectionState.status) {
      case SocketConnectionStatus.CONNECTED:
        return "tw-text-green-600";
      case SocketConnectionStatus.CONNECTING:
      case SocketConnectionStatus.TOKEN_REFRESHING:
        return "tw-text-blue-600";
      case SocketConnectionStatus.RECONNECTING:
        return "tw-text-yellow-600";
      case SocketConnectionStatus.AUTH_ERROR:
      case SocketConnectionStatus.CONNECTION_ERROR:
        return "tw-text-red-600";
      case SocketConnectionStatus.DISCONNECTED:
      default:
        return "tw-text-gray-600";
    }
  };

  const shouldShowReconnectButton = () => {
    return (
      connectionState.status === SocketConnectionStatus.AUTH_ERROR ||
      connectionState.status === SocketConnectionStatus.CONNECTION_ERROR ||
      connectionState.status === SocketConnectionStatus.DISCONNECTED
    );
  };

  if (!showDetails && connectionState.status === SocketConnectionStatus.CONNECTED) {
    return null; // Don't show anything when connected and not showing details
  }

  return (
    <div className={`tw-space-y-2 ${className}`}>
      {/* Status indicator */}
      <div className="tw-flex tw-items-center tw-gap-2 tw-text-sm">
        {getStatusIcon()}
        <span className={getStatusColor()}>{getStatusText()}</span>
        {shouldShowReconnectButton() && onReconnect && (
          <Button
            variant="outline"
            size="sm"
            onClick={onReconnect}
            className="tw-h-6 tw-px-2 tw-text-xs tw-text-aims-text-primary"
          >
            <ArrowPathIcon className="tw-w-3 tw-h-3 tw-mr-1" />
            Retry
          </Button>
        )}
      </div>

      {/* Error alert */}
      {showAlert && connectionState.lastError && (
        <div className="tw-bg-red-50 tw-border tw-border-red-200 tw-rounded-md tw-p-3 tw-flex tw-items-start tw-gap-2">
          <ExclamationCircleIcon className="tw-h-4 tw-w-4 tw-text-red-500 tw-flex-shrink-0 tw-mt-0.5" />
          <div className="tw-flex-1">
            <div className="tw-flex tw-items-center tw-justify-between">
              <span className="tw-text-sm tw-text-red-700">{connectionState.lastError}</span>
              <div className="tw-flex tw-gap-2">
                {onReconnect && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={onReconnect}
                    className="tw-h-6 tw-px-2 tw-text-xs tw-border-red-300 tw-text-red-700 hover:tw-bg-red-100"
                  >
                    Retry
                  </Button>
                )}
                {onClearError && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      onClearError();
                      setShowAlert(false);
                    }}
                    className="tw-h-6 tw-px-2 tw-text-xs tw-text-red-700 hover:tw-bg-red-100"
                  >
                    Dismiss
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Detailed status (optional) */}
      {showDetails && (
        <div className="tw-text-xs tw-text-gray-500 tw-space-y-1">
          <div>Status: {connectionState.status}</div>
          <div>Connected: {connectionState.isConnected ? "Yes" : "No"}</div>
          {connectionState.isReconnecting && (
            <div>Reconnect attempts: {connectionState.reconnectAttempts}/{connectionState.maxReconnectAttempts}</div>
          )}
          {connectionState.isTokenRefreshing && (
            <div>Token refresh in progress...</div>
          )}
        </div>
      )}
    </div>
  );
}

// Compact version for use in headers/status bars
export function SocketConnectionDot({
  connectionState,
  className = "",
}: {
  connectionState: SocketConnectionState;
  className?: string;
}) {
  const getDotColor = () => {
    switch (connectionState.status) {
      case SocketConnectionStatus.CONNECTED:
        return "tw-bg-green-500";
      case SocketConnectionStatus.CONNECTING:
      case SocketConnectionStatus.TOKEN_REFRESHING:
        return "tw-bg-blue-500";
      case SocketConnectionStatus.RECONNECTING:
        return "tw-bg-yellow-500";
      case SocketConnectionStatus.AUTH_ERROR:
      case SocketConnectionStatus.CONNECTION_ERROR:
        return "tw-bg-red-500";
      case SocketConnectionStatus.DISCONNECTED:
      default:
        return "tw-bg-gray-400";
    }
  };

  const shouldPulse = () => {
    return (
      connectionState.status === SocketConnectionStatus.CONNECTING ||
      connectionState.status === SocketConnectionStatus.RECONNECTING ||
      connectionState.status === SocketConnectionStatus.TOKEN_REFRESHING
    );
  };

  return (
    <div
      className={`tw-w-2 tw-h-2 tw-rounded-full ${getDotColor()} ${
        shouldPulse() ? "tw-animate-pulse" : ""
      } ${className}`}
      title={getStatusText()}
    />
  );

  function getStatusText() {
    switch (connectionState.status) {
      case SocketConnectionStatus.CONNECTED:
        return "Connected to real-time messaging";
      case SocketConnectionStatus.CONNECTING:
        return "Connecting to real-time messaging...";
      case SocketConnectionStatus.RECONNECTING:
        return "Reconnecting to real-time messaging...";
      case SocketConnectionStatus.TOKEN_REFRESHING:
        return "Refreshing authentication...";
      case SocketConnectionStatus.AUTH_ERROR:
        return "Authentication error - real-time messaging unavailable";
      case SocketConnectionStatus.CONNECTION_ERROR:
        return "Connection error - real-time messaging unavailable";
      case SocketConnectionStatus.DISCONNECTED:
      default:
        return "Disconnected from real-time messaging";
    }
  }
}
