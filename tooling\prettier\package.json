{"name": "@repo/prettier-config", "private": true, "version": "0.1.0", "type": "module", "exports": {".": "./index.js"}, "scripts": {"clean": "rm -rf .turbo node_modules", "format": "prettier --check . --ignore-path ../../.gitignore", "typecheck": "tsc --noEmit"}, "dependencies": {"@ianvs/prettier-plugin-sort-imports": "^4.2.1", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.5.14"}, "devDependencies": {"@repo/tsconfig": "workspace:*", "typescript": "5.7.3"}, "prettier": "@repo/prettier-config"}