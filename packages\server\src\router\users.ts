import { trpc } from "../lib/trpc";
import { privateProcedure, publicProcedure } from "../lib/trpc";
import { getAllUsers, getUserById } from "../controllers/users";
import { idSchema, athleteProfileSchema, brandProfileSchema } from "../validators";
import BrandModel from "../models/brand";
import AthleteModel from "../models/athlete";
import { ExtendedTRPCError } from "../utils/trpc";

export const usersRouter = trpc.router({
    getAll: publicProcedure.query(async () => {
        return getAllUsers();
    }),
    getById: publicProcedure
        .input(idSchema)
        .query(async ({ input }) => {
            return getUserById(input.id);
        }),
    updateBrandProfile: privateProcedure
        .input(brandProfileSchema)
        .mutation(async ({ ctx, input }) => {
            const profile = await BrandModel.findOne({ userId: ctx.req.user.id });
            if (!profile) {
                throw new ExtendedTRPCError("NOT_FOUND", "Brand profile not found");
            }
            if (ctx.req.user.userType !== "brand") {
                throw new ExtendedTRPCError("FORBIDDEN", "Only brand users can update brand profiles");
            }
            
            Object.assign(profile, input);
            await profile.save();
            return profile;
        }),
});

export type UsersRouter = typeof usersRouter; 