import React from "react";

import { EmailButton } from "./EmailButton";
import { EmailTemplate } from "./EmailTemplate";

interface VerificationEmailProps {
  vLink: string;
  email: string;
}

export const NewEmailVerification: React.FC<VerificationEmailProps> = ({
  vLink,
  email,
}) => {
  return (
    <EmailTemplate email={email}>
      <div style={{ width: "auto", borderRadius: "10px" }}>
        <div style={{ width: "100%" }}>
          <h3>Verify New Email</h3>
          <p style={{ color: "#64758b" }}>
            Confirm that this is the updated email for your AIMS account by
            clicking the button below.
          </p>
        </div>

        <EmailButton href={vLink}>Verify</EmailButton>

        <br />
      </div>
    </EmailTemplate>
  );
};
