import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { format } from "date-fns";
import { useDispatch, useSelector } from "react-redux";
import type { PayloadAction } from "@reduxjs/toolkit";

import { CampaignVisibility } from "@repo/server/src/types/campaign";
import type { RootState } from "@/store";
import type { CampaignInfo } from "@/store/slices/campaign";

// Helper function to convert ISO string or date to yyyy-MM-dd format for date inputs
const formatDateForInput = (dateValue: string): string => {
  if (!dateValue) return "";

  try {
    // If it's already in yyyy-MM-dd format, return as is
    if (/^\d{4}-\d{2}-\d{2}$/.test(dateValue)) {
      return dateValue;
    }

    // Otherwise, parse as Date and format to yyyy-MM-dd
    return format(new Date(dateValue), "yyyy-MM-dd");
  } catch {
    return "";
  }
};

export default function CampaignBasicInfo({
  basicInfoSelector,
  setBasicInfoAction,
  isEditMode,
}: {
  basicInfoSelector: (state: RootState) => CampaignInfo;
  setBasicInfoAction: (payload: Partial<CampaignInfo>) => PayloadAction<Partial<CampaignInfo>>;
  isEditMode: boolean;
}) {
  const dispatch = useDispatch();
  const basicInfo = useSelector(basicInfoSelector);

  const handleChange = (
    field: keyof typeof basicInfo,
    value: string | number | undefined,
  ) => {
    dispatch(setBasicInfoAction({ [field]: value }));
  };

  return (
    <div className="tw-space-y-6">
      <h4>{isEditMode ? "Edit Campaign" : "Create New Campaign"}</h4>
      <div>
        <div className="tw-space-y-4">
          <div>
            <Label htmlFor="name" required>
              Campaign Name
            </Label>
            <Input
              id="name"
              value={basicInfo.name}
              onChange={(e) => handleChange("name", e.target.value)}
              placeholder="Enter campaign name"
            />
          </div>

          <div>
            <Label htmlFor="visibility">Visibility</Label>
            <Select
              value={basicInfo.visibility}
              onValueChange={(value) => handleChange("visibility", value)}
            >
              <SelectTrigger className="!tw-bg-aims-dark-3 tw-border-aims-dark-3 tw-text-aims-text-primary">
                <SelectValue
                  placeholder="Select visibility"
                  className="tw-text-aims-text-secondary"
                />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={CampaignVisibility.PUBLIC}>
                  Public
                </SelectItem>
                <SelectItem value={CampaignVisibility.PRIVATE}>
                  Private
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="tw-grid tw-grid-cols-2 tw-gap-4">
            <div>
              <Label htmlFor="startDate" required>
                Start Date
              </Label>
              <Input
                id="startDate"
                type="date"
                value={formatDateForInput(basicInfo.startDate)}
                onChange={(e) => handleChange("startDate", e.target.value)}
              />
            </div>

            <div>
              <Label htmlFor="endDate" required>
                End Date
              </Label>
              <Input
                id="endDate"
                type="date"
                value={formatDateForInput(basicInfo.endDate)}
                onChange={(e) => handleChange("endDate", e.target.value)}
                min={format(new Date(), "yyyy-MM-dd")}
              />
            </div>
          </div>
          <div>
            <Label htmlFor="description" required>
              Description
            </Label>
            <Textarea
              id="description"
              value={basicInfo.description}
              onChange={(e) => handleChange("description", e.target.value)}
              className="tw-bg-aims-dark-3 tw-border-aims-dark-3 tw-text-aims-text-primary tw-min-h-[100px]"
              placeholder="Describe your campaign"
            />
          </div>

          {/* <div>
            <Label htmlFor="price" className="tw-text-aims-text-primary">
              Price (USD)*
            </Label>
            <Input
              id="price"
              type="number"
              value={basicInfo.price || ""}
              onChange={(e) =>
                handleChange("price", parseFloat(e.target.value) || 0)
              }
              placeholder="10"
              min="10"
              step="1"
            />
          </div> */}
        </div>
      </div>
    </div>
  );
}
