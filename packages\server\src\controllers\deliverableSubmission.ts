

import AthleteModel from "../models/athlete";
import BrandModel from "../models/brand";
import { CampaignModel } from "../models/campaign";
import ContractModel from "../models/contract";
import DeliverableModel, { DeliverableSubmissionModel } from "../models/deliverable";
import UserModel, { UserType } from "../models/user";
import { ContractStatus } from "../types/contract";
import {
  DeliverableSubmissionInput,
  DeliverableSubmissionReviewInput,
  DeliverableSubmissionStatus,
  SerializedDeliverableSubmission,
} from "../types/deliverableSubmission";
import { ExtendedTRPCError } from "../utils/trpc";
import { createChat, sendMessage } from "./chat";
import { ChatType, MessageType } from "../types/chat";

/**
 * Check if all deliverables for a contract have been submitted (none in AWAITING_SUBMISSION)
 */
export const areAllDeliverablesSubmitted = async (
  contractId: string
): Promise<boolean> => {
  const contract = await ContractModel.findById(contractId);
  if (!contract) {
    throw new ExtendedTRPCError("NOT_FOUND", "Contract not found");
  }

  // Get all deliverable IDs from the contract
  const deliverableIds = contract.terms.deliverables.map(d => d.deliverableId);

  // Check if any deliverable submissions are still in AWAITING_SUBMISSION status
  const awaitingSubmissions = await DeliverableSubmissionModel.countDocuments({
    campaignId: contract.campaignId,
    deliverableId: { $in: deliverableIds },
    athleteId: contract.athleteId,
    status: DeliverableSubmissionStatus.AWAITING_SUBMISSION,
  });

  return awaitingSubmissions === 0;
};

/**
 * Check if all deliverables for a contract have been approved
 */
export const areAllDeliverablesApproved = async (
  contractId: string
): Promise<boolean> => {
  const contract = await ContractModel.findById(contractId);
  if (!contract) {
    throw new ExtendedTRPCError("NOT_FOUND", "Contract not found");
  }

  // Get all deliverable IDs from the contract
  const deliverableIds = contract.terms.deliverables.map(d => d.deliverableId);

  // Count total deliverables
  const totalDeliverables = deliverableIds.length;

  // Count approved deliverable submissions
  const approvedSubmissions = await DeliverableSubmissionModel.countDocuments({
    campaignId: contract.campaignId,
    deliverableId: { $in: deliverableIds },
    athleteId: contract.athleteId,
    status: DeliverableSubmissionStatus.APPROVED,
  });

  return approvedSubmissions === totalDeliverables;
};

/**
 * Submit a deliverable for review
 */
export const submitDeliverable = async (
  userId: string,
  userType: UserType,
  input: DeliverableSubmissionInput
): Promise<SerializedDeliverableSubmission> => {
  // Verify user is an athlete
  if (userType !== "athlete") {
    throw new ExtendedTRPCError("FORBIDDEN", "Only athletes can submit deliverables");
  }

  // Get athlete profile
  const athlete = await AthleteModel.findOne({ userId });
  if (!athlete) {
    throw new ExtendedTRPCError("NOT_FOUND", "Athlete profile not found");
  }

  // Verify campaign exists
  const campaign = await CampaignModel.findById(input.campaignId);
  if (!campaign) {
    throw new ExtendedTRPCError("NOT_FOUND", "Campaign not found");
  }

  // Verify deliverable exists and belongs to campaign
  const deliverable = await DeliverableModel.findOne({
    _id: input.deliverableId,
    campaignId: input.campaignId,
  });
  if (!deliverable) {
    throw new ExtendedTRPCError("NOT_FOUND", "Deliverable not found");
  }

  // Verify athlete has an active contract for this campaign
  const activeContract = await ContractModel.findOne({
    campaignId: input.campaignId,
    athleteId: athlete._id,
    status: { $in: [ContractStatus.AWAITING_DELIVERABLES, ContractStatus.AWAITING_BRAND_APPROVAL, ContractStatus.FULFILLED] },
  });

  if (!activeContract) {
    throw new ExtendedTRPCError(
      "FORBIDDEN", 
      "You must have an active contract to submit deliverables"
    );
  }

  // Check if submission already exists for this deliverable
  const existingSubmission = await DeliverableSubmissionModel.findOne({
    campaignId: input.campaignId,
    deliverableId: input.deliverableId,
    athleteId: athlete._id,
  });

  // If submission exists, check if submission/resubmission is allowed
  if (existingSubmission) {
    const canSubmit = existingSubmission.status === DeliverableSubmissionStatus.AWAITING_SUBMISSION ||
                     existingSubmission.status === DeliverableSubmissionStatus.REJECTED ||
                     existingSubmission.status === DeliverableSubmissionStatus.NEEDS_REVISION;

    if (!canSubmit) {
      throw new ExtendedTRPCError(
        "BAD_REQUEST",
        "You have already submitted this deliverable and it cannot be resubmitted"
      );
    }
  }

  // Validate input
  if (!input.description.trim()) {
    throw new ExtendedTRPCError("BAD_REQUEST", "Description is required");
  }

  let submission;

  if (existingSubmission) {
    // Update existing submission for resubmission
    existingSubmission.description = input.description;
    existingSubmission.files = input.files || [];
    existingSubmission.status = DeliverableSubmissionStatus.PENDING;
    existingSubmission.submittedAt = new Date();
    existingSubmission.reviewedAt = undefined;
    existingSubmission.reviewedBy = undefined;
    existingSubmission.feedback = undefined;

    await existingSubmission.save();
    submission = existingSubmission;
  } else {
    // Create new submission
    submission = new DeliverableSubmissionModel({
      campaignId: input.campaignId,
      deliverableId: input.deliverableId,
      athleteId: athlete._id,
      description: input.description,
      files: input.files || [],
      status: DeliverableSubmissionStatus.PENDING,
    });

    await submission.save();
  }

  // Send notification to brand
  try {
    console.log("Starting notification process for submission...");
    console.log("Campaign brandId:", campaign.brandId);

    const brand = await BrandModel.findById(campaign.brandId).populate('userId');
    console.log("Found brand:", brand ? "Yes" : "No");
    console.log("Brand userId:", brand?.userId);
    console.log("Brand details:", {
      brandId: brand?._id,
      companyName: brand?.companyName,
      userId: brand?.userId,
      userIdType: typeof brand?.userId
    });

    if (brand && brand.userId) {
      const brandUserId = brand.userId._id.toString();
      const athleteUserId = userId;

      console.log("Creating chat between:", { athleteUserId, brandUserId });

      // Create or get chat between brand and athlete
      const chat = await createChat(
        athleteUserId,
        [brandUserId],
        ChatType.DIRECT,
        campaign._id.toString()
      );

      console.log("Chat created/found:", chat.id);

      // Send notification message
      const isResubmission = existingSubmission !== null;
      const content = isResubmission
        ? `Deliverable resubmitted for "${deliverable.name}" in campaign "${campaign.name}". Click to review the updated submission.`
        : `New deliverable submission for "${deliverable.name}" in campaign "${campaign.name}". Click to review the submission.`;

      console.log("Sending message with content:", content);

      const message = await sendMessage(
        chat.id,
        athleteUserId,
        content,
        MessageType.DELIVERABLE_SUBMISSION,
        campaign._id.toString(),
        activeContract._id.toString()
      );

      console.log("Message sent successfully:", message.id);
      console.log("Message details:", {
        chatId: chat.id,
        senderId: athleteUserId,
        content,
        type: MessageType.DELIVERABLE_SUBMISSION,
        campaignId: campaign._id.toString(),
        contractId: activeContract._id.toString()
      });
    } else {
      console.log("Brand or brand userId not found - skipping notification");
    }
  } catch (error) {
    console.error("Error sending submission notification:", error);
    // Don't fail the submission if notification fails
  }

  // Check if all deliverables are now submitted and transition contract status if needed
  try {
    if (activeContract.status === ContractStatus.AWAITING_DELIVERABLES) {
      const allSubmitted = await areAllDeliverablesSubmitted(activeContract._id.toString());

      if (allSubmitted) {
        // Update contract status to AWAITING_BRAND_APPROVAL
        await ContractModel.findByIdAndUpdate(activeContract._id, {
          status: ContractStatus.AWAITING_BRAND_APPROVAL,
        });

        console.log(`Contract ${activeContract._id} transitioned to AWAITING_BRAND_APPROVAL - all deliverables submitted`);

        // Send notification to brand about all deliverables being submitted
        try {
          const contractBrand = await BrandModel.findById(campaign.brandId).populate('userId');

          if (contractBrand && contractBrand.userId) {
            const brandUserId = contractBrand.userId._id.toString();
            const athleteUserId = userId;

            // Create or get chat between brand and athlete
            const chat = await createChat(
              athleteUserId,
              [brandUserId],
              ChatType.DIRECT,
              campaign._id.toString()
            );

            // Send notification message
            const content = `🎉 All deliverables have been submitted for contract "${activeContract.title}" in campaign "${campaign.name}". Please review and approve the submissions to complete the contract.`;

            await sendMessage(
              chat.id,
              athleteUserId,
              content,
              MessageType.CONTRACT_UPDATED,
              campaign._id.toString(),
              activeContract._id.toString()
            );

            console.log("Brand notification sent for all deliverables submitted");
          }
        } catch (notificationError) {
          console.error("Error sending brand notification for all deliverables submitted:", notificationError);
          // Don't fail the submission if notification fails
        }
      }
    }
  } catch (error) {
    console.error("Error checking/updating contract status after submission:", error);
    // Don't fail the submission if status update fails
  }

  return await getSerializedSubmission(submission._id.toString());
};

/**
 * Get submissions for an athlete
 */
export const getAthleteSubmissions = async (
  userId: string,
  userType: UserType,
  campaignId?: string
): Promise<SerializedDeliverableSubmission[]> => {
  if (userType !== "athlete") {
    throw new ExtendedTRPCError("FORBIDDEN", "Only athletes can view their submissions");
  }

  const athlete = await AthleteModel.findOne({ userId });
  if (!athlete) {
    throw new ExtendedTRPCError("NOT_FOUND", "Athlete profile not found");
  }

  const query: any = { athleteId: athlete._id };
  if (campaignId) {
    query.campaignId = campaignId;
  }

  const submissions = await DeliverableSubmissionModel.find(query)
    .sort({ submittedAt: -1 })
    .populate('campaignId', 'name')
    .populate('deliverableId', 'name type');

  return Promise.all(
    submissions.map(submission => getSerializedSubmission(submission._id.toString()))
  );
};

/**
 * Get submissions for a campaign (brand view)
 */
export const getCampaignSubmissions = async (
  userId: string,
  userType: UserType,
  campaignId: string
): Promise<SerializedDeliverableSubmission[]> => {
  if (userType !== "brand") {
    throw new ExtendedTRPCError("FORBIDDEN", "Only brands can view campaign submissions");
  }

  // Verify brand owns the campaign
  const brand = await BrandModel.findOne({ userId });
  if (!brand) {
    throw new ExtendedTRPCError("NOT_FOUND", "Brand profile not found");
  }

  const campaign = await CampaignModel.findOne({
    _id: campaignId,
    brandId: brand._id,
  });

  if (!campaign) {
    throw new ExtendedTRPCError("NOT_FOUND", "Campaign not found");
  }

  const submissions = await DeliverableSubmissionModel.find({ campaignId })
    .sort({ submittedAt: -1 });

  return Promise.all(
    submissions.map(submission => getSerializedSubmission(submission._id.toString()))
  );
};

/**
 * Get a serialized submission with populated data
 */
const getSerializedSubmission = async (
  submissionId: string
): Promise<SerializedDeliverableSubmission> => {
  const submission = await DeliverableSubmissionModel.findById(submissionId)
    .populate('campaignId', 'name')
    .populate('deliverableId', 'name type')
    .populate('athleteId', 'userId')
    .populate('reviewedBy', 'name userType');

  if (!submission) {
    throw new ExtendedTRPCError("NOT_FOUND", "Submission not found");
  }

  const baseSubmission = submission.toClient();

  // Add populated data
  const result: SerializedDeliverableSubmission = {
    ...baseSubmission,
    campaign: submission.campaignId ? {
      id: (submission.campaignId as any)._id.toString(),
      name: (submission.campaignId as any).name,
    } : undefined,
    deliverable: submission.deliverableId ? {
      id: (submission.deliverableId as any)._id.toString(),
      name: (submission.deliverableId as any).name,
      type: (submission.deliverableId as any).type,
    } : undefined,
  };

  // Get athlete info
  if (submission.athleteId) {
    const athleteUser = await UserModel.findById((submission.athleteId as any).userId);
    const athlete = await AthleteModel.findById(submission.athleteId);
    if (athleteUser && athlete) {
      result.athlete = {
        id: athlete._id.toString(),
        name: athleteUser.name,
        profilePicture: athlete.profilePicture,
      };
    }
  }

  // Get reviewer info
  if (submission.reviewedBy) {
    const reviewer = submission.reviewedBy as any;
    if (reviewer.userType === 'brand') {
      const brand = await BrandModel.findOne({ userId: reviewer._id });
      result.reviewer = {
        id: reviewer._id.toString(),
        name: reviewer.name,
        companyName: brand?.companyName,
      };
    }
  }

  return result;
};

/**
 * Review a deliverable submission (brand only)
 */
export const reviewSubmission = async (
  userId: string,
  userType: UserType,
  input: DeliverableSubmissionReviewInput
): Promise<SerializedDeliverableSubmission> => {
  // Verify user is a brand
  if (userType !== "brand") {
    throw new ExtendedTRPCError("FORBIDDEN", "Only brands can review submissions");
  }

  // Get brand profile
  const brand = await BrandModel.findOne({ userId });
  if (!brand) {
    throw new ExtendedTRPCError("NOT_FOUND", "Brand profile not found");
  }

  // Get the submission
  const submission = await DeliverableSubmissionModel.findById(input.submissionId)
    .populate('campaignId')
    .populate('deliverableId')
    .populate('athleteId');

  if (!submission) {
    throw new ExtendedTRPCError("NOT_FOUND", "Submission not found");
  }

  // Verify brand owns the campaign
  const campaign = submission.campaignId as any;
  if (campaign.brandId.toString() !== brand._id.toString()) {
    throw new ExtendedTRPCError("FORBIDDEN", "You can only review submissions for your own campaigns");
  }

  // Update submission status
  submission.status = input.status;
  submission.reviewedAt = new Date();
  submission.reviewedBy = userId as any;
  if (input.feedback) {
    submission.feedback = input.feedback;
  }

  await submission.save();

  // Send notification to athlete
  try {
    const athlete = submission.athleteId as any;
    const athleteUserId = athlete.userId.toString();
    const brandUserId = userId;

    // Create or get chat between brand and athlete
    const chat = await createChat(
      brandUserId,
      [athleteUserId],
      ChatType.DIRECT,
      campaign._id.toString()
    );

    // Determine message content based on status
    let content = "";
    let messageType = MessageType.TEXT;

    switch (input.status) {
      case DeliverableSubmissionStatus.APPROVED:
        content = `Great work! Your submission for "${(submission.deliverableId as any).name}" has been approved.`;
        messageType = MessageType.TEXT;
        break;
      case DeliverableSubmissionStatus.REJECTED:
        content = `Your submission for "${(submission.deliverableId as any).name}" has been rejected.`;
        if (input.feedback) {
          content += ` Feedback: ${input.feedback}`;
        }
        messageType = MessageType.TEXT;
        break;
      case DeliverableSubmissionStatus.NEEDS_REVISION:
        content = `Your submission for "${(submission.deliverableId as any).name}" needs revision.`;
        if (input.feedback) {
          content += ` Feedback: ${input.feedback}`;
        }
        messageType = MessageType.TEXT;
        break;
    }

    await sendMessage(
      chat.id,
      brandUserId,
      content,
      messageType,
      campaign._id.toString()
    );

    console.log("Review notification sent to athlete");
  } catch (error) {
    console.error("Error sending review notification:", error);
    // Don't fail the review if notification fails
  }

  // Check if all deliverables are now approved and ready for contract completion
  try {
    // Find the contract for this submission
    const contract = await ContractModel.findOne({
      campaignId: campaign._id,
      athleteId: submission.athleteId,
      status: ContractStatus.AWAITING_BRAND_APPROVAL,
    });

    if (contract && input.status === DeliverableSubmissionStatus.APPROVED) {
      const allApproved = await areAllDeliverablesApproved(contract._id.toString());

      if (allApproved) {
        console.log(`All deliverables approved for contract ${contract._id} - ready for fulfillment`);

        // Send notification to brand about contract being ready for fulfillment
        try {
          const brandUserId = userId;
          const athlete = submission.athleteId as any;
          const athleteUserId = athlete.userId.toString();

          // Create or get chat between brand and athlete
          const chat = await createChat(
            brandUserId,
            [athleteUserId],
            ChatType.DIRECT,
            campaign._id.toString()
          );

          // Send notification message to brand
          const content = `🎉 All deliverables have been approved for contract "${contract.title}" in campaign "${campaign.name}". You can now mark the contract as fulfilled to complete the agreement.`;

          await sendMessage(
            chat.id,
            brandUserId,
            content,
            MessageType.CONTRACT_UPDATED,
            campaign._id.toString(),
            contract._id.toString()
          );

          console.log("Brand notification sent for contract ready for fulfillment");
        } catch (notificationError) {
          console.error("Error sending brand notification for contract ready for fulfillment:", notificationError);
          // Don't fail the review if notification fails
        }
      }
    }
  } catch (error) {
    console.error("Error checking contract completion status after review:", error);
    // Don't fail the review if status check fails
  }

  return await getSerializedSubmission(submission._id.toString());
};

// Get submission for resubmission (athlete only)
export const getSubmissionForResubmission = async (
  userId: string,
  userType: UserType,
  input: { campaignId: string; deliverableId: string }
): Promise<SerializedDeliverableSubmission | null> => {
  // Only athletes can get submissions for resubmission
  if (userType !== "athlete") {
    throw new ExtendedTRPCError("FORBIDDEN", "Only athletes can access this endpoint");
  }

  // Get athlete
  const athlete = await AthleteModel.findOne({ userId }).populate('userId');
  if (!athlete) {
    throw new ExtendedTRPCError("NOT_FOUND", "Athlete not found");
  }

  // Find the submission
  const submission = await DeliverableSubmissionModel.findOne({
    campaignId: input.campaignId,
    deliverableId: input.deliverableId,
    athleteId: athlete._id,
  });

  if (!submission) {
    return null;
  }

  // Only return submissions that can be submitted/resubmitted
  const canSubmit = submission.status === DeliverableSubmissionStatus.AWAITING_SUBMISSION ||
                   submission.status === DeliverableSubmissionStatus.REJECTED ||
                   submission.status === DeliverableSubmissionStatus.NEEDS_REVISION;

  if (!canSubmit) {
    return null;
  }

  return await getSerializedSubmission(submission._id.toString());
};

/**
 * Check if athlete has active contract for campaign
 */
export const hasActiveContract = async (
  userId: string,
  campaignId: string
): Promise<boolean> => {
  const athlete = await AthleteModel.findOne({ userId });
  if (!athlete) {
    return false;
  }

  const activeContract = await ContractModel.findOne({
    campaignId,
    athleteId: athlete._id,
    status: { $in: [ContractStatus.AWAITING_DELIVERABLES, ContractStatus.AWAITING_BRAND_APPROVAL, ContractStatus.FULFILLED] },
  });

  return !!activeContract;
};
