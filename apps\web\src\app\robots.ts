import { MetadataRoute } from "next";

export default function robots(): MetadataRoute.Robots {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || "https://aimsmarketing.ai";

  return {
    rules: {
      userAgent: "*",
      allow: "/",
      disallow: [
        "/api/",
        "/admin/",
        "/private/",
        "/(auth)/",
        "/(aims)/",
        "/(campaign)/",
        "/(onboarding)/",
      ],
    },
    sitemap: `${baseUrl}/sitemap.xml`,
  };
}
