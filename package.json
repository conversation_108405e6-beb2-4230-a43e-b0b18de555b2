{"name": "create-t3-turbo", "private": true, "engines": {"node": ">=20.12.0"}, "packageManager": "pnpm@9.14.2", "scripts": {"build": "turbo run build", "clean": "git clean -xdf node_modules", "clean:workspaces": "turbo run clean", "web": "cd ./apps/web && pnpm dev", "redis": "cd ./packages/redis && pnpm dev", "compose": "cd ./packages/worker && pnpm compose", "worker": "cd ./packages/worker && pnpm compose", "dev": "pnpm redis && pnpm worker && turbo watch dev --filter=@repo/web --filter=@repo/server --filter=@repo/worker", "server": "turbo watch dev -F @repo/server...", "start:server": "cd ./packages/server && pnpm start", "build:server": "turbo build --filter=@repo/server", "format": "turbo run format --continue -- --cache --cache-location node_modules/.cache/.prettiercache", "format:fix": "turbo run format --continue -- --write --cache --cache-location node_modules/.cache/.prettiercache", "lint": "turbo run lint --continue -- --cache --cache-location node_modules/.cache/.eslintcache", "lint:fix": "turbo run lint --continue -- --fix --cache --cache-location node_modules/.cache/.eslintcache", "lint:ws": "pnpm dlx sherif@latest", "postinstall": "pnpm lint:ws", "typecheck": "turbo run typecheck", "ui-add": "turbo run ui-add", "seed": "cd ./packages/server && pnpm seed", "db:dump": "bash ./scripts/db-scripts/dump.sh", "db:restore": "bash ./scripts/db-scripts/restore.sh"}, "devDependencies": {"@repo/prettier-config": "workspace:*", "@turbo/gen": "^2.5.2", "eslint": "9.14.0", "postcss": "8.4.49", "prettier": "^3.5.3", "turbo": "^2.5.2", "typescript": "5.7.3"}, "prettier": "@repo/prettier-config"}