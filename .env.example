
# JWT
JWT_SECRET="asdfasfasdf"
JWT_EXPIRES_IN="15m"

# Client
CLIENT_URL="http://localhost:3010"

# Server
SERVER_URL="http://localhost:3000"

# Next.js & Expo
NEXT_PUBLIC_SERVER_URL="http://localhost:3000"
NEXT_PUBLIC_APP_URL="http://localhost:3010"
EXPO_PUBLIC_SERVER_URL="http://***********:3000"

# Cloudinary
EXPO_PUBLIC_CLOUD_NAME=""
EXPO_PUBLIC_CLOUD_KEY=""
EXPO_PUBLIC_CLOUD_SECRET=""

# MongoDB
DB_URL="mongodb://localhost:27017/aims"

MAIL_TRAP_HOST=""
MAIL_TRAP_PORT=""
MAIL_TRAP_USER=""
MAIL_TRAP_PASSWORD=""
MAIL_TRAP_FROM_EMAIL=""

# Redis
REDIS_HOST="localhost"
REDIS_PASSWORD="default"
REDIS_PORT="6379"

# AI Keys
PERPLEXITY_API_KEY=""
OPENAI_API_KEY=""

# S3
AWS_ACCESS_KEY_ID=""
AWS_SECRET_ACCESS_KEY=""
AWS_REGION=""
AWS_BUCKET_NAME=""
