import { combineReducers, configureStore } from "@reduxjs/toolkit";

import { authReducer } from "./slices/auth";
import campaignReducer from "./slices/campaign";
import editCampaignReducer from "./slices/editCampaign";
import { onboardingReducer } from "./slices/onboarding";

// Combine your reducers
const rootReducer = combineReducers({
  auth: authReducer,
  onboarding: onboardingReducer,
  campaign: campaignReducer,
  editCampaign: editCampaignReducer,
});

// Create the store with the root reducer
export const store = configureStore({
  reducer: rootReducer,
  devTools: process.env.NODE_ENV !== "production",
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
    }),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
