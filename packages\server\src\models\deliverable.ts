import { Document, model, Schema } from "mongoose";

import { DeliverableType } from "../types/deliverable";
import { DeliverableSubmissionStatus } from "../types/deliverableSubmission";

export interface DeliverableDocument extends Document {
  _id: string;
  campaignId: Schema.Types.ObjectId;
  name: string;
  daysToComplete: number;
  minimumPayment: number;
  description: string;
  type: DeliverableType;
  // Type-specific fields
  location?: string;
  date?: string;
  time?: string;
  content?: string[];
  productName?: string;
  productPrice?: number;
  createdAt: Date;
  updatedAt: Date;
  toClient(): Deliverable;
}

export interface Deliverable {
  id: string;
  campaignId: string;
  name: string;
  daysToComplete: number;
  minimumPayment: number;
  description: string;
  type: DeliverableType;
  // Type-specific fields
  location?: string;
  date?: string;
  time?: string;
  content?: string[];
  productName?: string;
  productPrice?: number;
  createdAt: string;
  updatedAt: string;
}

export const deliverableSchema = new Schema<DeliverableDocument>(
  {
    name: {
      type: String,
      required: true,
    },
    daysToComplete: {
      type: Number,
      required: true,
      min: 1,
    },
    minimumPayment: {
      type: Number,
      required: true,
      min: 0,
    },
    description: {
      type: String,
      required: true,
    },
    type: {
      type: String,
      enum: Object.values(DeliverableType),
      required: true,
    },
    campaignId: {
      type: Schema.Types.ObjectId,
      ref: "Campaigns",
      required: true,
    },
    // Type-specific fields
    location: {
      type: String,
      required: function (this: DeliverableDocument) {
        return this.type === DeliverableType.IN_PERSON;
      },
    },
    time: {
      type: String,
    },
    content: [
      {
        type: String,
      },
    ],
    date: {
      type: String,
    },
    productName: {
      type: String,
    },
    productPrice: {
      type: Number,
      min: 0,
    },
  },
  { timestamps: true },
);

deliverableSchema.methods.toClient = function (): Deliverable {
  return {
    id: this._id.toString(),
    campaignId: this.campaignId.toString(),
    name: this.name,
    daysToComplete: this.daysToComplete,
    minimumPayment: this.minimumPayment,
    description: this.description,
    type: this.type,
    location: this.location,
    date: this.date,
    time: this.time,
    content: this.content,
    productName: this.productName,
    productPrice: this.productPrice,
    createdAt: this.createdAt.toISOString(),
    updatedAt: this.updatedAt.toISOString(),
  };
};

const DeliverableModel = model("Deliverables", deliverableSchema);
export default DeliverableModel;

// DeliverableSubmission interfaces and schema
export interface DeliverableSubmissionDocument extends Document {
  _id: string;
  campaignId: Schema.Types.ObjectId;
  deliverableId: Schema.Types.ObjectId;
  athleteId: Schema.Types.ObjectId;
  description: string;
  files: {
    url: string;
    originalName: string;
    fileType: string;
    fileSize: number;
  }[];
  status: DeliverableSubmissionStatus;
  submittedAt: Date;
  reviewedAt?: Date;
  reviewedBy?: Schema.Types.ObjectId; // Brand user ID
  feedback?: string;
  createdAt: Date;
  updatedAt: Date;
  toClient(): any;
}

const deliverableSubmissionFileSchema = new Schema(
  {
    url: { type: String, required: true },
    originalName: { type: String, required: true },
    fileType: { type: String, required: true },
    fileSize: { type: Number, required: true },
  },
  { _id: false }
);

const deliverableSubmissionSchema = new Schema<DeliverableSubmissionDocument>(
  {
    campaignId: {
      type: Schema.Types.ObjectId,
      ref: "Campaigns",
      required: true,
    },
    deliverableId: {
      type: Schema.Types.ObjectId,
      ref: "Deliverables",
      required: true,
    },
    athleteId: {
      type: Schema.Types.ObjectId,
      ref: "Athletes",
      required: true,
    },
    description: {
      type: String,
      required: true,
      maxlength: 2000,
    },
    files: [deliverableSubmissionFileSchema],
    status: {
      type: String,
      enum: Object.values(DeliverableSubmissionStatus),
      default: DeliverableSubmissionStatus.PENDING,
    },
    submittedAt: {
      type: Date,
      default: Date.now,
    },
    reviewedAt: {
      type: Date,
    },
    reviewedBy: {
      type: Schema.Types.ObjectId,
      ref: "Users",
    },
    feedback: {
      type: String,
      maxlength: 2000,
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Create indexes for common queries
deliverableSubmissionSchema.index({ campaignId: 1, athleteId: 1 });
deliverableSubmissionSchema.index({ deliverableId: 1, athleteId: 1 });
deliverableSubmissionSchema.index({ status: 1, submittedAt: -1 });

// Add toClient method
deliverableSubmissionSchema.methods.toClient = function () {
  const obj = this.toObject();
  return {
    id: obj._id.toString(),
    campaignId: obj.campaignId.toString(),
    deliverableId: obj.deliverableId.toString(),
    athleteId: obj.athleteId.toString(),
    description: obj.description,
    files: obj.files,
    status: obj.status,
    submittedAt: obj.submittedAt.toISOString(),
    reviewedAt: obj.reviewedAt?.toISOString(),
    reviewedBy: obj.reviewedBy?.toString(),
    feedback: obj.feedback,
    createdAt: obj.createdAt.toISOString(),
    updatedAt: obj.updatedAt.toISOString(),
  };
};

export const DeliverableSubmissionModel = model("DeliverableSubmissions", deliverableSubmissionSchema);
