import * as yup from "yup";

import {
  getAthleteById,
  getAthleteProfile,
  getAthletes,
  getAthletesByIds,
  getUploadUrl,
  updateAthleteProfile,
  updateProfilePicture,
} from "../controllers/athlete";
import {
  getAthleteWallet,
  createPayoutRequest,
  getPayoutRequests,
  cancelPayoutRequest,
  setupStripeConnect,
  createStripeOnboardingLink,
  getAthleteStripeStatus,
} from "../controllers/athleteWallet";
import { privateProcedure, trpc } from "../lib/trpc";
import {
  athleteProfileSchema,
  profilePictureSchema,
  uploadUrlSchema,
} from "../validators/profile";
import { urlValidator } from "../validators/shared";

export const athleteRouter = trpc.router({
  getProfile: privateProcedure.query(async ({ ctx }) => {
    return getAthleteProfile(ctx.req.user.id);
  }),

  getUploadUrl: privateProcedure
    .input(uploadUrlSchema)
    .mutation(async ({ input }) => {
      return getUploadUrl(input.fileType);
    }),

  updateProfilePicture: privateProcedure
    .input(profilePictureSchema)
    .mutation(async ({ ctx, input }) => {
      return updateProfilePicture(ctx.req.user.id, input.url, input.key);
    }),

  updateAthleteProfile: privateProcedure
    .input(athleteProfileSchema)
    .mutation(async ({ ctx, input }) => {
      return updateAthleteProfile(
        ctx.req.user.id,
        ctx.req.user.userType,
        input,
      );
    }),

  getAthleteById: privateProcedure
    .input(
      yup.object({
        athleteId: yup.string().required(),
      }),
    )
    .query(async ({ ctx, input }) => {
      return getAthleteById(ctx.req.user.id, input.athleteId);
    }),

  getAthletes: privateProcedure
    .input(
      yup.object({
        filters: yup
          .object({
            search: yup.string().optional(),
            university: yup.string().optional(),
            sport: yup.string().optional(),
            yearInSchool: yup.string().optional(),
          })
          .optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      return getAthletes(ctx.req.user.id, input.filters);
    }),

  getAthletesByIds: privateProcedure
    .input(
      yup.object({
        athleteIds: yup.array().of(yup.string().required()).required(),
      }),
    )
    .query(async ({ input }) => {
      return getAthletesByIds(input.athleteIds);
    }),

  // Wallet operations
  getWallet: privateProcedure.query(async ({ ctx }) => {
    return getAthleteWallet(ctx.req.user.id, ctx.req.user.userType);
  }),

  createPayoutRequest: privateProcedure
    .input(
      yup.object({
        amount: yup.number().min(0.01).required(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return createPayoutRequest(
        ctx.req.user.id,
        ctx.req.user.userType,
        input.amount
      );
    }),

  getPayoutRequests: privateProcedure
    .input(
      yup.object({
        limit: yup.number().min(1).max(100).optional().default(20),
      }),
    )
    .query(async ({ ctx, input }) => {
      return getPayoutRequests(
        ctx.req.user.id,
        ctx.req.user.userType,
        input.limit
      );
    }),

  cancelPayoutRequest: privateProcedure
    .input(
      yup.object({
        payoutRequestId: yup.string().required(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return cancelPayoutRequest(
        ctx.req.user.id,
        ctx.req.user.userType,
        input.payoutRequestId
      );
    }),

  // Stripe Connect operations
  setupStripeConnect: privateProcedure.mutation(async ({ ctx }) => {
    return setupStripeConnect(ctx.req.user.id, ctx.req.user.userType);
  }),

  createStripeOnboardingLink: privateProcedure
    .input(
      yup.object({
        returnUrl: urlValidator,
        refreshUrl: urlValidator,
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return createStripeOnboardingLink(
        ctx.req.user.id,
        ctx.req.user.userType,
        input.returnUrl,
        input.refreshUrl
      );
    }),

  getStripeStatus: privateProcedure.query(async ({ ctx }) => {
    return getAthleteStripeStatus(ctx.req.user.id, ctx.req.user.userType);
  }),
});
