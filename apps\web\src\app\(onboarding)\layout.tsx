"use client";

import { useCallback, useEffect, useState } from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { LoadingSpinner } from "@/components/ui/LoadingSpinner";
import { useAuth } from "@/hooks/use-auth";
import { client } from "@/lib/trpc/client";
import {
  resetOnboarding,
  selectAthleteBasicInfo,
  selectAthletePaymentInfo,
  selectBrandBasicInfo,
  selectCurrentStep,
  selectInterests,
  selectSocialMedia,
  selectUserType,
  setCurrentStep,
  setUserType,
} from "@/store/slices/onboarding";
import { useDispatch, useSelector } from "react-redux";

export default function OnboardingLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const router = useRouter();
  const dispatch = useDispatch();
  const { getProfile, user } = useAuth();
  const currentStep = useSelector(selectCurrentStep);
  const userType = useSelector(selectUserType);
  const athleteBasicInfo = useSelector(selectAthleteBasicInfo);
  const brandBasicInfo = useSelector(selectBrandBasicInfo);
  const interests = useSelector(selectInterests);
  const socialMedia = useSelector(selectSocialMedia);
  const athletePaymentInfo = useSelector(selectAthletePaymentInfo);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState("");

  const TOTAL_STEPS = userType === "athlete" ? 4 : 1;

  // Reset onboarding state when component mounts
  useEffect(() => {
    dispatch(resetOnboarding());
  }, [dispatch]);

  const checkAuth = useCallback(async () => {
    const profile = await getProfile();
    if (!profile) {
      router.push("/auth/login");
    }
  }, [getProfile, router]);

  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  useEffect(() => {
    if (userType === null) {
      const storedUserType = localStorage.getItem("userType");
      if (storedUserType) {
        dispatch(setUserType(storedUserType as "athlete" | "brand"));
      }
    }
  }, [dispatch, userType]);

  const handleNext = async () => {
    if (currentStep < TOTAL_STEPS) {
      dispatch(setCurrentStep(currentStep + 1));
    } else {
      if (userType === "athlete") {
        // Validate required fields for athlete
        if (
          !athleteBasicInfo.college ||
          !athleteBasicInfo.sport ||
          !athleteBasicInfo.yearInSchool ||
          !athleteBasicInfo.birthDate ||
          !athleteBasicInfo.gender
        ) {
          setError(
            "Please fill in all required fields in the basic information section.",
          );
          dispatch(setCurrentStep(1));
          return;
        }
        if (interests.businessInterests.length === 0) {
          setError("Please select at least one business interest.");
          dispatch(setCurrentStep(2));
          return;
        }
        if (!athletePaymentInfo.referralSource) {
          setError("Please select how you heard about AIMS.");
          dispatch(setCurrentStep(4));
          return;
        }
        try {
          setIsSubmitting(true);
          setError("");
          // Submit athlete profile
          await client.athlete.updateAthleteProfile.mutate({
            name: user?.name || "",
            university: athleteBasicInfo.college,
            sport: athleteBasicInfo.sport,
            yearInSchool: athleteBasicInfo.yearInSchool,
            position: athleteBasicInfo.position,
            birthDate: new Date(athleteBasicInfo.birthDate),
            gender: athleteBasicInfo.gender as "male" | "female",
            businessInterests: interests.businessInterests,
            bio: athleteBasicInfo.bio,
            hometown: athleteBasicInfo.hometown,
            socialMedia: {
              instagram: socialMedia.instagram,
              twitter: socialMedia.twitter,
              tiktok: socialMedia.tiktok,
            },
            minPayment: {
              shoot: athletePaymentInfo.shoot || null,
              inPerson: athletePaymentInfo.inPerson || null,
              contentShare: athletePaymentInfo.contentShare || null,
              contentCreation: athletePaymentInfo.contentCreation || null,
              giftedCollab: athletePaymentInfo.giftedCollab || null,
              other: athletePaymentInfo.other || null,
            },
            referralSource: athletePaymentInfo.referralSource || "",
            referralName: athletePaymentInfo.referralName || null,
            referralVenmo: athletePaymentInfo.referralVenmo || null,
          });
          await getProfile(true);
          router.push("/app/athlete");
        } catch (error) {
          setError(
            error instanceof Error ? error.message : "Failed to create profile",
          );
        } finally {
          setIsSubmitting(false);
        }
      } else if (userType === "brand") {
        // Validate required fields for brand
        if (!brandBasicInfo.companyName || !brandBasicInfo.industry) {
          setError("Please fill in all required fields for your brand.");
          dispatch(setCurrentStep(1));
          return;
        }
        try {
          setIsSubmitting(true);
          setError("");
          await client.brand.updateBrandProfile.mutate({
            companyName: brandBasicInfo.companyName,
            industry: brandBasicInfo.industry,
            website: brandBasicInfo.website,
            location: brandBasicInfo.location,
            description: brandBasicInfo.description,
          });
          const result = await client.brand.createCheckoutSession.mutate();
          if (result.url) {
            window.location.href = result.url;
          }
          await getProfile(true);
          router.push("/app/brand");
        } catch (error) {
          setError(
            error instanceof Error
              ? error.message
              : "Failed to create brand profile",
          );
        } finally {
          setIsSubmitting(false);
        }
      }
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      dispatch(setCurrentStep(currentStep - 1));
    }
  };

  const handleCancel = () => {
    if (
      confirm(
        "Are you sure you want to cancel onboarding? Your progress will be lost.",
      )
    ) {
      router.push("/auth/login");
    }
  };

  return (
    <div className="tw-min-h-screen tw-flex tw-flex-col tw-bg-aims-dark-2">
      {/* Header */}
      <header className="tw-px-4 sm:tw-px-6 tw-py-3 sm:tw-py-4 tw-border-b tw-border-gray-700">
        <div className="tw-max-w-[800px] tw-mx-auto tw-flex tw-items-center tw-justify-center">
          <div className="tw-flex tw-items-center tw-gap-2">
            <Image
              src="/aims-icon.jpg"
              alt="AIMS"
              width={32}
              height={32}
              className="tw-w-6 tw-h-6 sm:tw-w-8 sm:tw-h-8"
            />
            <span className="tw-text-lg sm:tw-text-xl tw-font-semibold tw-text-aims-primary">
              AIMS
            </span>
          </div>
        </div>
      </header>

      {/* Progress Steps */}
      {userType === "athlete" && (
        <div className="tw-bg-aims-dark-3 tw-py-4 sm:tw-py-6 tw-px-4 sm:tw-px-6">
          <div className="tw-max-w-7xl tw-mx-auto tw-overflow-x-auto">
            <div className="tw-flex tw-justify-center tw-items-center tw-min-w-max">
              {Array.from({ length: TOTAL_STEPS }, (_, i) => i + 1).map(
                (step, index) => (
                  <div
                    key={step}
                    className="tw-flex tw-flex-col tw-items-center"
                  >
                    <div className="tw-flex tw-items-center">
                      <div
                        className={`tw-w-10 tw-h-10 sm:tw-w-12 sm:tw-h-12 tw-rounded-full tw-flex tw-flex-col tw-items-center tw-justify-center tw-relative tw-touch-manipulation ${
                          step <= currentStep
                            ? "tw-bg-aims-primary"
                            : "tw-bg-gray-600"
                        }`}
                      >
                        <span className="tw-text-aims-text-primary tw-font-medium tw-text-sm sm:tw-text-base">
                          {step}
                        </span>
                      </div>
                      {index < TOTAL_STEPS - 1 && (
                        <div
                          className={`tw-w-16 sm:tw-w-32 tw-h-[2px] tw-mx-[-8px] sm:tw-mx-[-12px] ${
                            step < currentStep
                              ? "tw-bg-aims-primary"
                              : "tw-bg-gray-600"
                          }`}
                        ></div>
                      )}
                    </div>
                  </div>
                ),
              )}
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      <main className="tw-flex-1 tw-flex tw-flex-col tw-bg-aims-bg">
        <div className="tw-max-w-7xl tw-w-full tw-mx-auto tw-px-4 sm:tw-px-6 lg:tw-px-8 tw-py-6 sm:tw-py-8">
          {error && (
            <div className="tw-mb-4 sm:tw-mb-6 tw-p-3 sm:tw-p-4 tw-bg-red-500/10 tw-border tw-border-red-500 tw-rounded-lg tw-text-red-500 tw-text-sm sm:tw-text-base">
              {error}
            </div>
          )}
          {children}
        </div>
      </main>

      {/* Footer */}
      <footer className="tw-py-4 sm:tw-py-6 tw-px-4 sm:tw-px-6 tw-border-t tw-border-gray-700 tw-bg-aims-bg">
        <div className="tw-max-w-7xl tw-mx-auto tw-flex tw-flex-col sm:tw-flex-row tw-gap-4 sm:tw-gap-0 sm:tw-justify-between sm:tw-items-center">
          <div className="tw-flex tw-flex-col sm:tw-flex-row tw-gap-3 sm:tw-gap-4 tw-order-2 sm:tw-order-1">
            <Button
              variant="ghost"
              onClick={handleCancel}
              disabled={isSubmitting}
              className="tw-h-12 sm:tw-h-10 tw-text-base sm:tw-text-sm tw-touch-manipulation"
            >
              Cancel
            </Button>
            {currentStep > 1 && (
              <Button
                variant="ghost"
                onClick={handleBack}
                disabled={isSubmitting}
                className="tw-h-12 sm:tw-h-10 tw-text-base sm:tw-text-sm tw-touch-manipulation"
              >
                Back
              </Button>
            )}
          </div>
          <Button
            onClick={handleNext}
            disabled={isSubmitting}
            className="tw-flex tw-items-center tw-justify-center tw-gap-2 tw-h-12 sm:tw-h-10 tw-text-base sm:tw-text-sm tw-touch-manipulation tw-order-1 sm:tw-order-2"
          >
            {isSubmitting && <LoadingSpinner />}
            {userType === "brand"
              ? currentStep === TOTAL_STEPS
                ? "Subscribe"
                : "Next "
              : currentStep === TOTAL_STEPS
                ? "Finish"
                : "Next"}
          </Button>
        </div>
      </footer>
    </div>
  );
}
