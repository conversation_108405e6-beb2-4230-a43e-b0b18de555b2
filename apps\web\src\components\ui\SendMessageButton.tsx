"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { ChatBubbleLeftIcon } from "@heroicons/react/24/outline";
import { trpc } from "@/lib/trpc/client";
import { useToast } from "@/components/ui/toast/use-toast";
import { ChatType, MessageType } from "@repo/server/src/types/chat";

import { Button } from "./button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "./dialog";
import { Textarea } from "./textarea";

interface SendMessageButtonProps {
  targetUserId: string;
  targetName: string;
  variant?: "default" | "outline" | "ghost" | "secondary" | "destructive" | "link";
  size?: "default" | "sm" | "lg" | "icon";
  className?: string;
  showText?: boolean;
  // Legacy props for backward compatibility
  athleteUserId?: string;
  athleteName?: string;
}

export default function SendMessageButton({
  targetUserId,
  targetName,
  variant = "outline",
  size = "default",
  className = "",
  showText = true,
  // Legacy props for backward compatibility
  athleteUserId,
  athleteName,
}: SendMessageButtonProps) {
  // All hooks must be called at the top level, before any conditional logic
  const [isOpen, setIsOpen] = useState(false);
  const [message, setMessage] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const router = useRouter();
  const { toast } = useToast();

  // Use new props if provided, otherwise fall back to legacy props
  const userId = targetUserId || athleteUserId;
  const userName = targetName || athleteName;

  if (!userId || !userName) {
    console.error("SendMessageButton: Missing required user ID or name");
    return null;
  }

  const createChatMutation = trpc.chat.createChat.useMutation();
  const sendMessageMutation = trpc.chat.sendMessage.useMutation();

  const handleSendMessage = async () => {
    if (!message.trim()) {
      toast({
        title: "Error",
        description: "Please enter a message before sending.",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    try {
      // Create or get the chat between current user and target user
      const chat = await createChatMutation.mutateAsync({
        participantIds: [userId],
        type: ChatType.DIRECT,
      });

      // Send the message to the chat
      await sendMessageMutation.mutateAsync({
        chatId: chat.id,
        content: message.trim(),
        type: MessageType.TEXT,
      });

      toast({
        title: "Message sent",
        description: `Your message has been sent to ${userName}.`,
        variant: "success",
      });

      // Close dialog and reset form
      setIsOpen(false);
      setMessage("");

      // Navigate to the chat
      router.push(`/app/chat/${chat.id}`);
    } catch (error) {
      console.error("Failed to send message:", error);

      // Provide more specific error messages
      let errorMessage = "Failed to send message. Please try again.";
      if (error instanceof Error && (error.message?.includes("Token has expired") || error.message?.includes("Authentication"))) {
        errorMessage = "Your session has expired. Please refresh the page and try again.";
      } else if (error instanceof Error && (error.message?.includes("Network") || error.message?.includes("fetch"))) {
        errorMessage = "Network error. Please check your connection and try again.";
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant={variant} size={size} className={className}>
          <div className="tw-flex tw-flex-row tw-items-center tw-gap-2">
            <ChatBubbleLeftIcon className="tw-w-4 tw-h-4" />
            {showText && <span>Send Message</span>}
          </div>
        </Button>
      </DialogTrigger>
      <DialogContent className="tw-bg-aims-dark-2 tw-text-aims-text-primary tw-border-aims-dark-3 tw-max-w-md">
        <DialogHeader>
          <DialogTitle>Send Message to {userName}</DialogTitle>
          <DialogDescription className="tw-text-aims-text-secondary">
            Send a direct message to {userName}. They will be notified and can respond in the chat.
          </DialogDescription>
        </DialogHeader>
        <div className="tw-py-4">
          <Textarea
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder={`Type your message to ${userName}...`}
            className="tw-w-full !tw-bg-aims-dark-2 tw-border tw-px-3 tw-py-2 tw-text-sm tw-min-h-[100px] tw-resize-y placeholder:tw-text-aims-text-secondary focus-visible:tw-outline-none focus-visible:tw-ring-2 focus-visible:tw-ring-aims-primary focus-visible:tw-ring-offset-2"
            disabled={isSubmitting}
          />
        </div>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => setIsOpen(false)}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSendMessage}
            disabled={isSubmitting || !message.trim()}
            className="tw-text-black"
          >
            {isSubmitting ? "Sending..." : "Send Message"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
