import { useEffect, useMemo } from "react";
import { trpc } from "@/lib/trpc/client";

import { useAuth } from "./use-auth";
import { useSocket } from "./use-socket";

export function useUnreadMessages() {
  const { user } = useAuth();
  const { socket, connectionState } = useSocket();
  const { data: chats } = trpc.chat.getChats.useQuery();
  const utils = trpc.useUtils();

  // Listen for real-time message updates
  useEffect(() => {
    if (!socket || !user?.id || !connectionState.isConnected) {
      return;
    }

    const handleNewMessage = (message: any) => {
      console.log("📨 useUnreadMessages: New message received", {
        messageId: message.id,
        chatId: message.chatId,
        senderId: message.sender?.id,
        currentUserId: user?.id,
        isFromCurrentUser: message.sender?.id === user?.id
      });
      // Invalidate the chats query to force a refetch
      utils.chat.getChats.invalidate();
    };

    const handleMessagesRead = (data: { chatId: string; userId: string }) => {
      console.log("👁️ useUnreadMessages: Messages marked as read", {
        chatId: data.chatId,
        userId: data.userId,
        currentUserId: user?.id,
        isCurrentUser: data.userId === user?.id
      });
      // Invalidate the chats query to force a refetch
      utils.chat.getChats.invalidate();
    };

    socket.on("newMessage", handleNewMessage);
    socket.on("messagesRead", handleMessagesRead);

    return () => {
      socket.off("newMessage", handleNewMessage);
      socket.off("messagesRead", handleMessagesRead);
    };
  }, [socket, user?.id, connectionState.isConnected, utils]);

  const unreadCount = useMemo(() => {
    if (!chats || !user?.id) return 0;

    const count = chats.reduce((count, chat) => {
      const hasUnreadMessages =
        chat.lastMessage &&
        chat.lastMessage.sender &&
        chat.lastMessage.sender.id !== user.id &&
        !chat.lastMessage.readBy.includes(user.id);

      return hasUnreadMessages ? count + 1 : count;
    }, 0);

    console.log("🔢 useUnreadMessages: Unread count calculated", {
      totalChats: chats?.length || 0,
      unreadCount: count,
      userId: user?.id,
      connectionStatus: connectionState.status,
      isConnected: connectionState.isConnected
    });

    return count;
  }, [chats, user?.id, connectionState.status, connectionState.isConnected]);

  const hasUnreadMessages = unreadCount > 0;

  return {
    unreadCount,
    hasUnreadMessages,
  };
}
