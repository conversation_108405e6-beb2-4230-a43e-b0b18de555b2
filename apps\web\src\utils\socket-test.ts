/**
 * Socket Connection Test Utility
 * 
 * This utility helps test the socket connection behavior with expired tokens
 * to ensure the infinite loop fix is working correctly.
 */

import { SocketTokenManager } from '@/lib/utils/socket-token-manager';

export interface SocketTestResult {
  success: boolean;
  message: string;
  details?: any;
}

export class SocketConnectionTester {
  private static testResults: Array<{ timestamp: number; result: SocketTestResult }> = [];

  /**
   * Test token refresh behavior with an expired token
   */
  static async testTokenRefreshWithExpiredToken(): Promise<SocketTestResult> {
    console.log("🧪 Testing token refresh with expired token...");
    
    try {
      // Create a mock expired token (this would normally come from a real expired token)
      const mockExpiredToken = "expired.jwt.token.example";
      
      // Reset state before test
      SocketTokenManager.resetRefreshState();
      
      // Attempt token refresh
      const refreshResult = await SocketTokenManager.refreshTokens(mockExpiredToken);
      
      if (refreshResult.success) {
        return {
          success: true,
          message: "Token refresh succeeded as expected",
          details: { refreshResult }
        };
      } else {
        return {
          success: true,
          message: "Token refresh failed gracefully (expected for test)",
          details: { error: refreshResult.error }
        };
      }
    } catch (error) {
      return {
        success: false,
        message: "Token refresh test threw unexpected error",
        details: { error: error instanceof Error ? error.message : error }
      };
    }
  }

  /**
   * Test circuit breaker functionality
   */
  static async testCircuitBreaker(): Promise<SocketTestResult> {
    console.log("🧪 Testing circuit breaker functionality...");
    
    try {
      // Reset state
      SocketTokenManager.resetRefreshState();
      
      // Attempt multiple refreshes to trigger circuit breaker
      const results = [];
      for (let i = 0; i < 5; i++) {
        const result = await SocketTokenManager.refreshTokens("expired.token");
        results.push(result);
        
        // If circuit breaker kicks in, we should get an error about max attempts
        if (!result.success && result.error?.includes("Maximum refresh attempts")) {
          return {
            success: true,
            message: "Circuit breaker activated correctly",
            details: { attemptNumber: i + 1, results }
          };
        }
      }
      
      return {
        success: false,
        message: "Circuit breaker did not activate as expected",
        details: { results }
      };
    } catch (error) {
      return {
        success: false,
        message: "Circuit breaker test threw unexpected error",
        details: { error: error instanceof Error ? error.message : error }
      };
    }
  }

  /**
   * Test refresh state management
   */
  static testRefreshStateManagement(): SocketTestResult {
    console.log("🧪 Testing refresh state management...");
    
    try {
      // Reset state
      SocketTokenManager.resetRefreshState();
      
      // Check initial state
      const initialState = SocketTokenManager.getRefreshState();
      if (initialState.refreshInProgress || initialState.refreshAttempts !== 0) {
        return {
          success: false,
          message: "Initial state not properly reset",
          details: { initialState }
        };
      }
      
      // Test state tracking
      const stateAfterReset = SocketTokenManager.getRefreshState();
      
      return {
        success: true,
        message: "Refresh state management working correctly",
        details: { initialState, stateAfterReset }
      };
    } catch (error) {
      return {
        success: false,
        message: "Refresh state test threw unexpected error",
        details: { error: error instanceof Error ? error.message : error }
      };
    }
  }

  /**
   * Run all tests
   */
  static async runAllTests(): Promise<Array<{ testName: string; result: SocketTestResult }>> {
    console.log("🧪 Running all socket connection tests...");
    
    const tests = [
      { name: "Token Refresh with Expired Token", test: () => this.testTokenRefreshWithExpiredToken() },
      { name: "Circuit Breaker", test: () => this.testCircuitBreaker() },
      { name: "Refresh State Management", test: () => this.testRefreshStateManagement() },
    ];
    
    const results = [];
    
    for (const test of tests) {
      console.log(`🧪 Running test: ${test.name}`);
      try {
        const result = await test.test();
        results.push({ testName: test.name, result });
        
        // Store result for later analysis
        this.testResults.push({ timestamp: Date.now(), result });
        
        console.log(`${result.success ? '✅' : '❌'} ${test.name}: ${result.message}`);
      } catch (error) {
        const errorResult: SocketTestResult = {
          success: false,
          message: `Test execution failed: ${error instanceof Error ? error.message : error}`,
        };
        results.push({ testName: test.name, result: errorResult });
        console.log(`❌ ${test.name}: ${errorResult.message}`);
      }
    }
    
    return results;
  }

  /**
   * Get test history
   */
  static getTestHistory() {
    return this.testResults;
  }

  /**
   * Clear test history
   */
  static clearTestHistory() {
    this.testResults = [];
  }
}

// Export for use in development/debugging
if (typeof window !== 'undefined') {
  (window as any).SocketConnectionTester = SocketConnectionTester;
}
