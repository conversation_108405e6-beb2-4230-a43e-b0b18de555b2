"use client";

import { useState } from "react";
import { format } from "date-fns";

import { SerializedContract } from "@repo/server/src/types/contract";
import { isPdfGenerationAllowed, getPdfGenerationDisabledMessage, isContractEditingAllowed, getContractEditingDisabledMessage } from "@repo/server/src/utils/contractPdf";

import { Button } from "../ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "../ui/card";
import { Badge } from "../ui/badge";

interface ContractPreviewProps {
  contract: SerializedContract;
  onEdit?: () => void;
  onApprove?: () => void;
  onReject?: () => void;
  onGeneratePdf?: () => void;
  isLoading?: boolean;
  showActions?: boolean;
}

export function ContractPreview({
  contract,
  onEdit,
  onApprove,
  onReject,
  onGeneratePdf,
  isLoading = false,
  showActions = true,
}: ContractPreviewProps) {
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    deliverables: true,
    payment: true,
    terms: false,
  });

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section],
    }));
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "DRAFT":
        return "tw-bg-gray-100 tw-text-gray-800";
      case "PENDING_BRAND_REVIEW":
        return "tw-bg-yellow-100 tw-text-yellow-800";
      case "PENDING_BRAND_APPROVAL":
        return "tw-bg-blue-100 tw-text-blue-800";
      case "PENDING_ATHLETE_SIGNATURE":
        return "tw-bg-purple-100 tw-text-purple-800";
      case "SIGNED":
        return "tw-bg-green-100 tw-text-green-800";
      case "CANCELLED":
        return "tw-bg-red-100 tw-text-red-800";
      case "EXPIRED":
        return "tw-bg-gray-100 tw-text-gray-600";
      default:
        return "tw-bg-gray-100 tw-text-gray-800";
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  return (
    <div className="tw-space-y-6">
      {/* Contract Header */}
      <Card>
        <CardHeader>
          <div className="tw-flex tw-justify-between tw-items-start">
            <div>
              <CardTitle className="tw-text-xl tw-font-bold">
                {contract.title}
              </CardTitle>
              <p className="tw-text-sm tw-text-gray-600 tw-mt-1">
                Contract #{contract.contractNumber} • Version {contract.version}
              </p>
            </div>
            <Badge className={getStatusColor(contract.status)}>
              {contract.status.replace(/_/g, ' ')}
            </Badge>
          </div>
          <div className="tw-grid tw-grid-cols-1 md:tw-grid-cols-3 tw-gap-4 tw-mt-4">
            <div>
              <p className="tw-text-sm tw-font-medium tw-text-gray-500">Created</p>
              <p className="tw-text-sm">{format(new Date(contract.createdAt), 'MMM dd, yyyy')}</p>
            </div>
            <div>
              <p className="tw-text-sm tw-font-medium tw-text-gray-500">Campaign Period</p>
              <p className="tw-text-sm">
                {format(new Date(contract.terms.campaignDuration.startDate), 'MMM dd')} - {format(new Date(contract.terms.campaignDuration.endDate), 'MMM dd, yyyy')}
              </p>
            </div>
            <div>
              <p className="tw-text-sm tw-font-medium tw-text-gray-500">Total Value</p>
              <p className="tw-text-lg tw-font-semibold tw-text-green-600">
                {formatCurrency(contract.terms.totalCompensation)}
              </p>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Deliverables Section */}
      <Card>
        <CardHeader>
          <div className="tw-flex tw-justify-between tw-items-center">
            <CardTitle className="tw-text-lg">Deliverables & Requirements</CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => toggleSection('deliverables')}
            >
              {expandedSections.deliverables ? 'Collapse' : 'Expand'}
            </Button>
          </div>
        </CardHeader>
        {expandedSections.deliverables && (
          <CardContent>
            <div className="tw-space-y-4">
              {contract.terms.deliverables.map((deliverable, index) => (
                <div key={deliverable.deliverableId} className="tw-border tw-rounded-lg tw-p-4">
                  <div className="tw-flex tw-justify-between tw-items-start tw-mb-2">
                    <h4 className="tw-font-medium">{index + 1}. {deliverable.name}</h4>
                    <Badge variant="outline">{formatCurrency(deliverable.compensation)}</Badge>
                  </div>
                  <p className="tw-text-sm tw-text-gray-600 tw-mb-2">{deliverable.description}</p>
                  <div className="tw-flex tw-justify-between tw-text-sm tw-text-gray-500">
                    <span>Due: {format(new Date(deliverable.dueDate), 'MMM dd, yyyy')}</span>
                    <span>Type: {deliverable.type.replace(/_/g, ' ')}</span>
                  </div>
                  {deliverable.requirements.length > 0 && (
                    <div className="tw-mt-3">
                      <p className="tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-1">Requirements:</p>
                      <ul className="tw-text-sm tw-text-gray-600 tw-list-disc tw-list-inside tw-space-y-1">
                        {deliverable.requirements.map((req, reqIndex) => (
                          <li key={reqIndex}>{req}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        )}
      </Card>

      {/* Payment Schedule Section */}
      <Card>
        <CardHeader>
          <div className="tw-flex tw-justify-between tw-items-center">
            <CardTitle className="tw-text-lg">Payment Schedule</CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => toggleSection('payment')}
            >
              {expandedSections.payment ? 'Collapse' : 'Expand'}
            </Button>
          </div>
        </CardHeader>
        {expandedSections.payment && (
          <CardContent>
            <div className="tw-space-y-3">
              {contract.terms.paymentSchedule.map((payment, index) => (
                <div key={index} className="tw-flex tw-justify-between tw-items-center tw-p-3 tw-bg-gray-50 tw-rounded-lg">
                  <div>
                    <p className="tw-font-medium">{payment.description}</p>
                    {payment.milestone && (
                      <p className="tw-text-sm tw-text-gray-600">Milestone: {payment.milestone}</p>
                    )}
                  </div>
                  <div className="tw-text-right">
                    <p className="tw-font-semibold">{formatCurrency(payment.amount)}</p>
                    <p className="tw-text-sm tw-text-gray-600">
                      Due: {format(new Date(payment.dueDate), 'MMM dd, yyyy')}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        )}
      </Card>

      {/* Terms & Conditions Section */}
      <Card>
        <CardHeader>
          <div className="tw-flex tw-justify-between tw-items-center">
            <CardTitle className="tw-text-lg">Terms & Conditions</CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => toggleSection('terms')}
            >
              {expandedSections.terms ? 'Collapse' : 'Expand'}
            </Button>
          </div>
        </CardHeader>
        {expandedSections.terms && (
          <CardContent className="tw-space-y-4">
            {contract.terms.additionalTerms && contract.terms.additionalTerms.length > 0 && (
              <div>
                <h4 className="tw-font-medium tw-mb-2">Additional Terms</h4>
                <ul className="tw-list-disc tw-list-inside tw-space-y-1 tw-text-sm tw-text-gray-600">
                  {contract.terms.additionalTerms.map((term, index) => (
                    <li key={index}>{term}</li>
                  ))}
                </ul>
              </div>
            )}

            {contract.terms.cancellationPolicy && (
              <div>
                <h4 className="tw-font-medium tw-mb-2">Cancellation Policy</h4>
                <p className="tw-text-sm tw-text-gray-600">{contract.terms.cancellationPolicy}</p>
              </div>
            )}

            {contract.terms.intellectualPropertyRights && (
              <div>
                <h4 className="tw-font-medium tw-mb-2">Intellectual Property Rights</h4>
                <p className="tw-text-sm tw-text-gray-600">{contract.terms.intellectualPropertyRights}</p>
              </div>
            )}

            {contract.terms.confidentialityClause && (
              <div>
                <h4 className="tw-font-medium tw-mb-2">Confidentiality</h4>
                <p className="tw-text-sm tw-text-gray-600">{contract.terms.confidentialityClause}</p>
              </div>
            )}
          </CardContent>
        )}
      </Card>

      {/* Action Buttons */}
      {showActions && (
        <Card>
          <CardContent className="tw-pt-6">
            <div className="tw-flex tw-flex-wrap tw-gap-3">
              {onGeneratePdf && (
                <div className="tw-flex tw-flex-col tw-gap-2">
                  <Button
                    variant="outline"
                    onClick={onGeneratePdf}
                    disabled={isLoading || !isPdfGenerationAllowed(contract.status)}
                    title={!isPdfGenerationAllowed(contract.status) ? getPdfGenerationDisabledMessage(contract.status) : undefined}
                  >
                    Generate PDF
                  </Button>

                  {!isPdfGenerationAllowed(contract.status) && (
                    <p className="tw-text-sm tw-text-gray-500 tw-max-w-xs">
                      {getPdfGenerationDisabledMessage(contract.status)}
                    </p>
                  )}
                </div>
              )}
              {onEdit && (
                <div className="tw-flex tw-flex-col tw-gap-2">
                  <Button
                    variant="outline"
                    onClick={onEdit}
                    disabled={isLoading || !isContractEditingAllowed(contract.status)}
                    title={!isContractEditingAllowed(contract.status) ? getContractEditingDisabledMessage(contract.status) : undefined}
                  >
                    Edit Contract
                  </Button>

                  {!isContractEditingAllowed(contract.status) && (
                    <p className="tw-text-sm tw-text-gray-500 tw-max-w-xs">
                      {getContractEditingDisabledMessage(contract.status)}
                    </p>
                  )}
                </div>
              )}
              {onApprove && (contract.status === "PENDING_BRAND_REVIEW" || contract.status === "PENDING_BRAND_APPROVAL") && (
                <Button
                  onClick={onApprove}
                  disabled={isLoading}
                  className="tw-bg-green-600 hover:tw-bg-green-700"
                >
                  Approve & Send to Athlete
                </Button>
              )}
              {onReject && contract.status !== "ATHLETE_SIGNED" && contract.status !== "CANCELLED" && (
                <Button
                  variant="destructive"
                  onClick={onReject}
                  disabled={isLoading}
                >
                  Cancel Contract
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
