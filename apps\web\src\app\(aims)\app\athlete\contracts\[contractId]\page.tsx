"use client";

import { useParams } from "next/navigation";
import { trpc } from "@/lib/trpc/client";

import { AthleteContractView } from "@/components/contract/AthleteContractView";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { LoadingSpinner } from "@/components/ui/LoadingSpinner";
import Link from "next/link";

function BackToContractsButton() {
  return (
    <Button
      variant="outline"
      asChild
      className="tw-flex tw-items-center tw-gap-2 tw-text-aims-text-primary"
    >
      <Link href="/app/athlete/contracts">
        ← Back to Contracts
      </Link>
    </Button>
  );
}

export default function AthleteContractPage() {
  const params = useParams();
  const contractId = params.contractId as string;

  // Fetch contract details
  const {
    data: contract,
    isLoading,
    error,
    refetch,
  } = trpc.contract.getById.useQuery({
    contractId,
  });

  const handleContractSigned = () => {
    // Refetch contract data to get updated status
    refetch();
  };

  if (isLoading) {
    return (
      <div className="tw-container tw-mx-auto tw-px-4 tw-py-8">
        <div className="tw-flex tw-justify-center tw-items-center tw-min-h-[400px]">
          <LoadingSpinner />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="tw-container tw-mx-auto tw-px-4 tw-py-8">
        <Card>
          <CardContent className="tw-py-12 tw-text-center">
            <div className="tw-space-y-4">
              <h2 className="tw-text-xl tw-font-semibold tw-text-red-600">
                Error Loading Contract
              </h2>
              <p className="tw-text-gray-600">
                {error.message || "Failed to load contract details. Please try again."}
              </p>
              <div className="tw-flex tw-justify-center tw-gap-3">
                <Button onClick={() => refetch()} variant="outline">
                  Try Again
                </Button>
                <BackToContractsButton />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!contract) {
    return (
      <div className="tw-container tw-mx-auto tw-px-4 tw-py-8">
        <Card>
          <CardContent className="tw-py-12 tw-text-center">
            <div className="tw-space-y-4">
              <h2 className="tw-text-xl tw-font-semibold tw-text-gray-600">
                Contract Not Found
              </h2>
              <p className="tw-text-gray-600">
                The contract you&amp;re looking for doesn&amp;t exist or you don&amp;t have permission to view it.
              </p>
              <BackToContractsButton />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="tw-container tw-mx-auto tw-px-4 tw-py-8 tw-space-y-6">
      {/* Navigation */}
      <div className="tw-flex tw-items-center tw-justify-between">
        <BackToContractsButton />
        
        {contract.status === "PENDING_ATHLETE_SIGNATURE" && 
         (!contract.expiresAt || new Date(contract.expiresAt) > new Date()) && (
          <div className="tw-bg-blue-50 tw-text-blue-700 tw-px-4 tw-py-2 tw-rounded-lg tw-border tw-border-blue-200">
            <span className="tw-font-medium">⚡ Action Required:</span> This contract is waiting for your signature
          </div>
        )}
      </div>

      {/* Contract View */}
      <AthleteContractView
        contract={contract}
        onContractSigned={handleContractSigned}
      />
    </div>
  );
}
