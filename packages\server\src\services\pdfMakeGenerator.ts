import pdfMake from 'pdfmake/build/pdfmake';
import pdfFonts from 'pdfmake/build/vfs_fonts';
import { TDocumentDefinitions } from 'pdfmake/interfaces';
import { SerializedContract } from "../types/contract";
import { SerializedCampaign } from "../types/campaign";
import { BrandProfile } from "../types/brand";
import { AthleteProfile } from "../models/user";

export interface ContractPdfData {
  contract: SerializedContract;
  campaign: SerializedCampaign;
  brand: BrandProfile & { _id: string };
  athlete: AthleteProfile & { _id: string };
}

// Set up fonts for pdfmake
pdfMake.vfs = pdfFonts.vfs;

/**
 * PDFMake-based PDF Generator Service
 * Lightweight and efficient alternative to Puppeteer/Playwright
 */
export class PdfMakeGeneratorService {
  private static instance: PdfMakeGeneratorService | null = null;

  public static getInstance(): PdfMakeGeneratorService {
    if (!PdfMakeGeneratorService.instance) {
      PdfMakeGeneratorService.instance = new PdfMakeGeneratorService();
    }
    return PdfMakeGeneratorService.instance;
  }

  /**
   * Generate contract PDF using pdfmake
   */
  async generateContractPdf(data: ContractPdfData): Promise<Buffer> {
    console.log('[PDFMake Generator] Starting PDF generation');

    try {
      const docDefinition = this.createDocumentDefinition(data);
      
      return new Promise<Buffer>((resolve, reject) => {
        try {
          const pdfDoc = pdfMake.createPdf(docDefinition);

          pdfDoc.getBuffer((buffer: Buffer) => {
            console.log('[PDFMake Generator] PDF generation successful');
            resolve(buffer);
          });
        } catch (error) {
          console.error('[PDFMake Generator] PDF generation failed:', error);
          reject(new Error(`PDF generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`));
        }
      });
    } catch (error) {
      console.error('[PDFMake Generator] Error creating document definition:', error);
      throw new Error(`PDF generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Create pdfmake document definition from contract data
   */
  private createDocumentDefinition(data: ContractPdfData): TDocumentDefinitions {
    const { contract, campaign, brand, athlete } = data;

    return {
      pageSize: 'A4',
      pageMargins: [40, 60, 40, 60],
      
      content: [
        // Header
        {
          text: 'CAMPAIGN AGREEMENT',
          style: 'header',
          alignment: 'center',
          margin: [0, 0, 0, 10]
        },
        {
          text: `Contract #${contract.contractNumber}`,
          style: 'subheader',
          alignment: 'center',
          margin: [0, 0, 0, 30]
        },

        // Campaign Information Section
        {
          text: 'Campaign Information',
          style: 'sectionHeader',
          margin: [0, 0, 0, 10]
        },
        {
          table: {
            widths: [120, '*'],
            body: [
              [
                { text: 'Campaign:', style: 'label' },
                { text: campaign.name, style: 'value' }
              ],
              [
                { text: 'Brand:', style: 'label' },
                { text: brand.companyName, style: 'value' }
              ],
              [
                { text: 'Athlete:', style: 'label' },
                { text: athlete.name, style: 'value' }
              ],
              [
                { text: 'Total Value:', style: 'label' },
                { text: `$${contract.terms.totalCompensation}`, style: 'value' }
              ]
            ]
          },
          layout: 'noBorders',
          margin: [0, 0, 0, 20]
        },

        // Deliverables Section
        {
          text: 'Deliverables',
          style: 'sectionHeader',
          margin: [0, 0, 0, 10]
        },
        ...contract.terms.deliverables.map((deliverable) => ({
          table: {
            widths: ['*'],
            body: [
              [
                {
                  stack: [
                    { text: deliverable.name, style: 'deliverableTitle' },
                    { text: `Compensation: $${deliverable.compensation}`, style: 'deliverableDetail' },
                    { text: `Due: ${new Date(deliverable.dueDate).toLocaleDateString()}`, style: 'deliverableDetail' }
                  ],
                  margin: [10, 10, 10, 10]
                }
              ]
            ]
          },
          layout: {
            hLineWidth: () => 1,
            vLineWidth: () => 1,
            hLineColor: () => '#dddddd',
            vLineColor: () => '#dddddd'
          },
          margin: [0, 0, 0, 10] as [number, number, number, number]
        })),

        // Signature Section
        {
          text: 'Signatures',
          style: 'sectionHeader',
          margin: [0, 30, 0, 20]
        },
        {
          columns: [
            {
              width: '48%',
              stack: [
                { text: 'Brand Representative', style: 'signatureLabel' },
                { text: '', margin: [0, 30, 0, 5] }, // Signature space
                { canvas: [{ type: 'line', x1: 0, y1: 0, x2: 200, y2: 0, lineWidth: 1 }] },
                { text: 'Date: _______________', style: 'dateField', margin: [0, 10, 0, 0] }
              ]
            },
            {
              width: '4%',
              text: ''
            },
            {
              width: '48%',
              stack: [
                { text: `Athlete: ${athlete.name}`, style: 'signatureLabel' },
                { text: '', margin: [0, 30, 0, 5] }, // Signature space
                { canvas: [{ type: 'line', x1: 0, y1: 0, x2: 200, y2: 0, lineWidth: 1 }] },
                { text: 'Date: _______________', style: 'dateField', margin: [0, 10, 0, 0] }
              ]
            }
          ]
        }
      ],

      styles: {
        header: {
          fontSize: 20,
          bold: true,
          color: '#333333'
        },
        subheader: {
          fontSize: 14,
          color: '#666666'
        },
        sectionHeader: {
          fontSize: 14,
          bold: true,
          color: '#333333',
          decoration: 'underline'
        },
        label: {
          fontSize: 12,
          bold: true,
          color: '#333333'
        },
        value: {
          fontSize: 12,
          color: '#333333'
        },
        deliverableTitle: {
          fontSize: 12,
          bold: true,
          color: '#333333'
        },
        deliverableDetail: {
          fontSize: 11,
          color: '#555555',
          margin: [0, 2, 0, 0]
        },
        signatureLabel: {
          fontSize: 12,
          bold: true,
          color: '#333333'
        },
        dateField: {
          fontSize: 11,
          color: '#333333'
        }
      },

      defaultStyle: {
        fontSize: 12,
        lineHeight: 1.4
      }
    };
  }
}

// Export singleton instance
export const pdfMakeGenerator = PdfMakeGeneratorService.getInstance();
