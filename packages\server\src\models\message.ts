import { Document, model, Schema } from "mongoose";
import { MessageType } from "../types/chat";

export interface MessageDocument extends Document {
  _id: Schema.Types.ObjectId;
  chatId: Schema.Types.ObjectId;
  senderId: Schema.Types.ObjectId;
  content: string;
  type: MessageType;
  campaignId?: Schema.Types.ObjectId;
  contractId?: Schema.Types.ObjectId;
  readBy: Schema.Types.ObjectId[];
  createdAt: Date;
  updatedAt: Date;
}

export interface Message {
  id: string;
  chatId: string;
  senderId: string;
  content: string;
  type: MessageType;
  campaignId?: string;
  contractId?: string;
  readBy: string[];
  createdAt: string;
  updatedAt: string;
}

const messageSchema = new Schema<MessageDocument>(
  {
    chatId: {
      type: Schema.Types.ObjectId,
      ref: "Chats",
      required: true,
    },
    senderId: {
      type: Schema.Types.ObjectId,
      ref: "Users",
      required: true,
    },
    content: {
      type: String,
      required: true,
    },
    type: {
      type: String,
      enum: Object.values(MessageType),
      default: MessageType.TEXT,
    },
    campaignId: {
      type: Schema.Types.ObjectId,
      ref: "Campaigns",
    },
    contractId: {
      type: Schema.Types.ObjectId,
      ref: "Contracts",
    },
    readBy: [
      {
        type: Schema.Types.ObjectId,
        ref: "Users",
        default: [],
      },
    ],
  },
  { timestamps: true },
);

// Add indexes for faster querying
messageSchema.index({ chatId: 1, createdAt: -1 });
messageSchema.index({ senderId: 1 });
messageSchema.index({ type: 1, campaignId: 1 });

// Add method to transform document to client format
messageSchema.methods.toClient = function (): Message {
  return {
    id: this._id.toString(),
    chatId: this.chatId.toString(),
    senderId: this.senderId.toString(),
    content: this.content,
    type: this.type,
    campaignId: this.campaignId?.toString(),
    contractId: this.contractId?.toString(),
    readBy: this.readBy.map((id: Schema.Types.ObjectId) => id.toString()),
    createdAt: this.createdAt.toISOString(),
    updatedAt: this.updatedAt.toISOString(),
  };
};

const MessageModel = model("Messages", messageSchema);
export default MessageModel;
