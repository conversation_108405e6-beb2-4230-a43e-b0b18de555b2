import { Types } from "mongoose";
import AthleteModel from "../models/athlete";
import AthleteWalletModel from "../models/athleteWallet";
import AthletePayoutRequestModel from "../models/athletePayoutRequest";
import WalletTransactionModel from "../models/walletTransaction";
import UserModel from "../models/user";
import { PayoutRequestStatus, TransactionType } from "../types/athlete";
import { UserType } from "../models/user";
import { ExtendedTRPCError } from "../utils/trpc";
import {
  getAthleteWalletWithHistory,
  createStripeConnectAccount,
  createStripeAccountLink,
  getStripeAccountStatus,
  processPayoutRequest,
} from "../services/athletePayoutService";

// Minimum payout amount in dollars
const MINIMUM_PAYOUT_AMOUNT = 5;

/**
 * Gets athlete wallet information with transaction history
 */
export const getAthleteWallet = async (
  userId: string,
  userType: UserType
) => {
  if (userType !== "athlete") {
    throw new ExtendedTRPCError("FORBIDDEN", "Only athletes can access wallet information");
  }

  // Find athlete by userId
  const athlete = await AthleteModel.findOne({ userId });
  if (!athlete) {
    throw new ExtendedTRPCError("NOT_FOUND", "Athlete profile not found");
  }

  return getAthleteWalletWithHistory(athlete._id.toString());
};

/**
 * Creates a payout request for an athlete
 */
export const createPayoutRequest = async (
  userId: string,
  userType: UserType,
  amount: number
) => {
  if (userType !== "athlete") {
    throw new ExtendedTRPCError("FORBIDDEN", "Only athletes can request payouts");
  }

  // Find athlete by userId
  const athlete = await AthleteModel.findOne({ userId });
  if (!athlete) {
    throw new ExtendedTRPCError("NOT_FOUND", "Athlete profile not found");
  }

  // Validate amount
  if (amount < MINIMUM_PAYOUT_AMOUNT) {
    throw new ExtendedTRPCError(
      "BAD_REQUEST", 
      `Minimum payout amount is $${MINIMUM_PAYOUT_AMOUNT}`
    );
  }

  // Get athlete wallet
  const wallet = await AthleteWalletModel.findOne({ athleteId: athlete._id });
  if (!wallet) {
    throw new ExtendedTRPCError("NOT_FOUND", "Wallet not found");
  }

  // Check if athlete has sufficient balance
  if (wallet.availableBalance < amount) {
    throw new ExtendedTRPCError(
      "BAD_REQUEST",
      `Insufficient balance. Available: $${wallet.availableBalance.toFixed(2)}`
    );
  }

  // Check if athlete has Stripe Connect account
  if (!wallet.stripeConnectAccountId) {
    throw new ExtendedTRPCError(
      "BAD_REQUEST",
      "Please connect your bank account before requesting a payout"
    );
  }

  // Check for existing pending payout requests
  const existingPendingRequest = await AthletePayoutRequestModel.findOne({
    athleteId: athlete._id,
    status: { $in: [PayoutRequestStatus.PENDING, PayoutRequestStatus.PROCESSING] }
  });

  if (existingPendingRequest) {
    throw new ExtendedTRPCError(
      "BAD_REQUEST",
      "You already have a pending payout request. Please wait for it to be processed."
    );
  }

  // Create payout request
  const payoutRequest = new AthletePayoutRequestModel({
    athleteId: athlete._id,
    amount,
    status: PayoutRequestStatus.PENDING,
    requestedAt: new Date(),
    metadata: {
      stripeConnectAccountId: wallet.stripeConnectAccountId,
    },
  });

  await payoutRequest.save();

  // Deduct from available balance (will be restored if payout fails)
  wallet.availableBalance -= amount;
  await wallet.save();

  // Create transaction record
  await WalletTransactionModel.create({
    athleteId: athlete._id,
    type: TransactionType.PAYOUT_REQUESTED,
    amount: -amount, // Negative because it's a deduction
    description: `Payout request for $${amount.toFixed(2)}`,
    payoutRequestId: payoutRequest._id,
  });

  console.log(`[Athlete Wallet] Created payout request ${payoutRequest._id} for athlete ${athlete._id}, amount: $${amount}`);

  return payoutRequest.toClient();
};

/**
 * Gets athlete's payout request history
 */
export const getPayoutRequests = async (
  userId: string,
  userType: UserType,
  limit: number = 20
) => {
  if (userType !== "athlete") {
    throw new ExtendedTRPCError("FORBIDDEN", "Only athletes can access payout requests");
  }

  // Find athlete by userId
  const athlete = await AthleteModel.findOne({ userId });
  if (!athlete) {
    throw new ExtendedTRPCError("NOT_FOUND", "Athlete profile not found");
  }

  const payoutRequests = await AthletePayoutRequestModel
    .find({ athleteId: athlete._id })
    .sort({ createdAt: -1 })
    .limit(limit)
    .lean();

  return payoutRequests.map(request => ({
    ...request,
    id: request._id.toString(),
    athleteId: request.athleteId.toString(),
  }));
};

/**
 * Cancels a pending payout request
 */
export const cancelPayoutRequest = async (
  userId: string,
  userType: UserType,
  payoutRequestId: string
) => {
  if (userType !== "athlete") {
    throw new ExtendedTRPCError("FORBIDDEN", "Only athletes can cancel payout requests");
  }

  // Find athlete by userId
  const athlete = await AthleteModel.findOne({ userId });
  if (!athlete) {
    throw new ExtendedTRPCError("NOT_FOUND", "Athlete profile not found");
  }

  // Find payout request
  const payoutRequest = await AthletePayoutRequestModel.findOne({
    _id: payoutRequestId,
    athleteId: athlete._id,
  });

  if (!payoutRequest) {
    throw new ExtendedTRPCError("NOT_FOUND", "Payout request not found");
  }

  // Can only cancel pending requests
  if (payoutRequest.status !== PayoutRequestStatus.PENDING) {
    throw new ExtendedTRPCError(
      "BAD_REQUEST",
      "Can only cancel pending payout requests"
    );
  }

  // Update request status
  payoutRequest.status = PayoutRequestStatus.CANCELLED;
  await payoutRequest.save();

  // Restore balance to wallet
  const wallet = await AthleteWalletModel.findOne({ athleteId: athlete._id });
  if (wallet) {
    wallet.availableBalance += payoutRequest.amount;
    await wallet.save();
  }

  // Create transaction record
  await WalletTransactionModel.create({
    athleteId: athlete._id,
    type: TransactionType.PAYOUT_CANCELLED,
    amount: payoutRequest.amount, // Positive because it's being restored
    description: `Cancelled payout request for $${payoutRequest.amount.toFixed(2)}`,
    payoutRequestId: payoutRequest._id,
  });

  console.log(`[Athlete Wallet] Cancelled payout request ${payoutRequestId} for athlete ${athlete._id}`);

  return payoutRequest.toClient();
};

/**
 * Creates a Stripe Connect account for an athlete
 */
export const setupStripeConnect = async (
  userId: string,
  userType: UserType
) => {
  if (userType !== "athlete") {
    throw new ExtendedTRPCError("FORBIDDEN", "Only athletes can set up Stripe Connect");
  }

  // Find athlete and user
  const athlete = await AthleteModel.findOne({ userId });
  if (!athlete) {
    throw new ExtendedTRPCError("NOT_FOUND", "Athlete profile not found");
  }

  const user = await UserModel.findById(userId);
  if (!user) {
    throw new ExtendedTRPCError("NOT_FOUND", "User not found");
  }

  // Check if athlete already has a Stripe account
  const wallet = await AthleteWalletModel.findOne({ athleteId: athlete._id });
  if (wallet?.stripeConnectAccountId) {
    throw new ExtendedTRPCError("BAD_REQUEST", "Stripe account already exists");
  }

  // Create Stripe Express account
  const account = await createStripeConnectAccount(athlete._id.toString(), user.email);

  return {
    accountId: account.id,
    status: 'incomplete',
  };
};

/**
 * Creates an onboarding link for Stripe Connect
 */
export const createStripeOnboardingLink = async (
  userId: string,
  userType: UserType,
  returnUrl: string,
  refreshUrl: string
) => {
  console.log(`[Stripe Connect] Creating onboarding link for athlete ${userId}`);
  console.log(`[Stripe Connect] URLs - Return: ${returnUrl}, Refresh: ${refreshUrl}`);

  if (userType !== "athlete") {
    throw new ExtendedTRPCError("FORBIDDEN", "Only athletes can access Stripe onboarding");
  }

  // Validate URLs with detailed error messages
  try {
    const returnUrlObj = new URL(returnUrl);
    const refreshUrlObj = new URL(refreshUrl);

    console.log(`[Stripe Connect] URL validation successful:`, {
      returnUrl: {
        href: returnUrlObj.href,
        protocol: returnUrlObj.protocol,
        hostname: returnUrlObj.hostname,
        port: returnUrlObj.port
      },
      refreshUrl: {
        href: refreshUrlObj.href,
        protocol: refreshUrlObj.protocol,
        hostname: refreshUrlObj.hostname,
        port: refreshUrlObj.port
      }
    });

    // Additional validation for Stripe requirements
    if (!returnUrlObj.protocol.startsWith('http')) {
      throw new Error(`Return URL must use HTTP or HTTPS protocol, got: ${returnUrlObj.protocol}`);
    }
    if (!refreshUrlObj.protocol.startsWith('http')) {
      throw new Error(`Refresh URL must use HTTP or HTTPS protocol, got: ${refreshUrlObj.protocol}`);
    }

  } catch (error) {
    console.error(`[Stripe Connect] Invalid URLs provided:`, { returnUrl, refreshUrl, error });
    const errorMessage = error instanceof Error ? error.message : 'Unknown URL validation error';
    throw new ExtendedTRPCError("BAD_REQUEST", `Invalid URL format: ${errorMessage}`);
  }

  // Find athlete
  const athlete = await AthleteModel.findOne({ userId });
  if (!athlete) {
    throw new ExtendedTRPCError("NOT_FOUND", "Athlete profile not found");
  }

  // Create account link
  const accountLink = await createStripeAccountLink(
    athlete._id.toString(),
    returnUrl,
    refreshUrl
  );

  return {
    url: accountLink.url,
    expiresAt: accountLink.expires_at,
  };
};

/**
 * Gets Stripe Connect account status for an athlete
 */
export const getAthleteStripeStatus = async (
  userId: string,
  userType: UserType
) => {
  if (userType !== "athlete") {
    throw new ExtendedTRPCError("FORBIDDEN", "Only athletes can access Stripe status");
  }

  // Find athlete
  const athlete = await AthleteModel.findOne({ userId });
  if (!athlete) {
    throw new ExtendedTRPCError("NOT_FOUND", "Athlete profile not found");
  }

  return getStripeAccountStatus(athlete._id.toString());
};
