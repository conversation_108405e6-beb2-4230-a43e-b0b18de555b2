import React, { useRef } from "react";
import Image from "next/image";
import Link from "next/link";
import {
  CameraIcon,
  GiftIcon,
  ShareIcon as HeroShareIcon,
  LightBulbIcon,
  UserGroupIcon,
  WrenchIcon,
} from "@heroicons/react/24/solid";
import { motion, useInView } from "framer-motion";

import BookACall from "../ui/BookACall";
import { Button } from "../ui/button";

function FeatureSection({
  imageSrc,
  imageAlt,
  title,
  description,
  offset = "left",
  reverse = false,
}) {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  // Choose the transform based on offset direction
  const offsetTransform =
    offset === "left"
      ? "tw-translate-x-[24px] tw-translate-y-[24px]"
      : "tw-translate-x-[-24px] tw-translate-y-[24px]";

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, y: 50 }}
      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
      transition={{ duration: 0.6 }}
      className={`tw-px-4 sm:tw-px-6 lg:tw-px-8 tw-py-6 sm:tw-py-8 tw-gap-6 sm:tw-gap-8 md:tw-gap-16 tw-flex tw-flex-col md:tw-flex-row tw-items-center${
        reverse ? " md:tw-flex-row-reverse" : ""
      }`}
    >
      <div className="tw-hidden lg:tw-block tw-relative tw-w-full tw-h-[220px] sm:tw-h-[320px] md:tw-w-[700px] md:tw-h-[480px] tw-flex-shrink-0">
        {/* Orange offset background */}
        <div
          className={`tw-absolute tw-inset-0 tw-bg-aims-primary tw-rounded-lg tw-z-0 ${offsetTransform}`}
        ></div>
        {/* Main image card */}
        <div className="tw-absolute tw-inset-0 tw-rounded-lg tw-overflow-hidden tw-z-10">
          <Image
            src={imageSrc}
            alt={imageAlt}
            fill
            className="tw-rounded-lg"
            sizes="(max-width: 768px) 100vw, 700px"
          />
        </div>
      </div>
      <div className="tw-mt-6 sm:tw-mt-8 md:tw-mt-0 tw-w-full">
        <h2 className="tw-text-xl sm:tw-text-2xl lg:tw-text-3xl tw-font-semibold tw-text-aims-text-primary lg:tw-text-left tw-text-center tw-leading-tight">
          {title}
        </h2>
        <p className="tw-text-aims-dark-7 tw-mt-2 sm:tw-mt-3 lg:tw-text-left tw-text-center tw-text-sm sm:tw-text-base tw-leading-relaxed">
          {description}
        </p>
      </div>
    </motion.div>
  );
}

// Reusable BlueCircleIcon component
function BlueCircleIcon({ icon: Icon, label }) {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, scale: 0.8 }}
      animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.8 }}
      transition={{ duration: 0.6 }}
      className="tw-flex tw-flex-col tw-items-center tw-gap-2 sm:tw-gap-3"
    >
      <div className="tw-bg-blue-500 tw-w-28 tw-h-28 sm:tw-w-32 sm:tw-h-32 lg:tw-w-40 lg:tw-h-40 tw-rounded-full tw-flex tw-items-center tw-justify-center tw-overflow-hidden">
        {Icon === HeroShareIcon ? (
          <span className="tw-inline-block tw-translate-x-[-4px]">
            <Icon className="tw-w-20 tw-h-20 sm:tw-w-24 sm:tw-h-24 lg:tw-w-32 lg:tw-h-32 tw-text-aims-text-primary" />
          </span>
        ) : (
          <Icon className="tw-w-20 tw-h-20 sm:tw-w-24 sm:tw-h-24 lg:tw-w-32 lg:tw-h-32 tw-text-aims-text-primary" />
        )}
      </div>
      <span className="tw-mt-2 tw-text-aims-text-primary tw-text-sm sm:tw-text-base lg:tw-text-lg tw-font-medium tw-text-center tw-leading-tight tw-px-2">
        {label}
      </span>
    </motion.div>
  );
}

export default function WhyAims() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  return (
    <div
      className="tw-bg-gradient-to-b"
      style={{
        background:
          "linear-gradient(to bottom, #153477 0%, #111928 20%, #111928 100%)",
      }}
    >
      <div className="tw-max-w-7xl tw-mx-auto tw-px-4 sm:tw-px-6 lg:tw-px-8 tw-py-8 sm:tw-py-10 md:tw-py-16">
        <motion.h1
          ref={ref}
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.6 }}
          className="tw-text-3xl sm:tw-text-4xl tw-font-bold tw-text-aims-text-primary tw-text-center"
        >
          Why Choose Aims?
        </motion.h1>
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="tw-max-w-3xl tw-mx-auto"
        >
          <p className="tw-text-aims-dark-7 tw-text-center tw-mt-4">
            Choose from a variety of campaign formats to fit your brand's unique
            goals. Whether you're looking for original content, product reviews,
            event participation, or custom strategies, AIMS offers flexible
            options to connect with the right athletes and drive real results.
          </p>
        </motion.div>
        {/* Feature Sections */}
        <div className="tw-mt-10 md:tw-mt-16 tw-flex tw-flex-col md:tw-gap-32">
          <FeatureSection
            imageSrc="/landing-page/athlete-access.png"
            imageAlt="Athlete Access screenshot"
            title="Athlete Access"
            description="Access a nationwide roster of motivated collegiate athletes ready to elevate your business through authentic partnerships."
            offset="left"
            reverse={false}
          />

          <FeatureSection
            imageSrc="/landing-page/campaign-management.png"
            imageAlt="Campaign Management screenshot"
            title="Campaign Management Done for You"
            description="No searching, no negotiating, no chasing deliverables — we manage the entire process so you can launch campaigns faster and easier than ever."
            offset="right"
            reverse={true}
          />
          <FeatureSection
            imageSrc="/landing-page/affordable-campaigns.png"
            imageAlt="Affordable Campaigns screenshot"
            title="Affordable Campaigns, Big-Time Impact"
            description="AIMS makes athlete marketing accessible for businesses of all sizes. With flexible pricing and no massive agency fees, you can launch powerful campaigns without breaking your budget."
            offset="left"
            reverse={false}
          />
        </div>
        {/* Testimonial Section */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="tw-mt-16 sm:tw-mt-24 md:tw-mt-32 lg:tw-mt-64 tw-flex tw-flex-col md:tw-flex-row tw-items-center tw-gap-6 sm:tw-gap-8 md:tw-gap-16"
        >
          <div className="md:tw-flex tw-w-full md:tw-w-auto tw-justify-center">
            <Image
              src="/landing-page/testimonial.png"
              alt="Testimonial section"
              width={560}
              height={400}
              className="tw-rounded-2xl tw-max-w-full"
              sizes="(max-width: 768px) 90vw, 500px"
              style={{ objectFit: "cover" }}
            />
            {/* <div className="tw-w-[400px] tw-h-[250px] tw-rounded-2xl tw-flex tw-flex-col tw-items-center tw-justify-center tw-p-6 tw-bg-gradient-to-r tw-from-[#153477] tw-to-[#19408c]">
              <span className="tw-text-aims-text-primary tw-text-lg tw-font-medium tw-text-center tw-mb-4">
                "AIMS makes NIL deals easy. I've landed local deals quickly, and
                the platform handles everything from contact to payment!"
              </span>
              <span className="tw-text-aims-text-primary tw-text-base tw-font-medium">
                - Tiana Brooks, Track and Field Athlete
              </span>
            </div> */}
          </div>
          <div className="tw-flex tw-flex-col tw-items-center md:tw-items-start tw-text-center md:tw-text-left tw-w-full md:tw-w-auto">
            <h2 className="tw-text-2xl sm:tw-text-4xl tw-font-bold tw-text-aims-text-primary tw-mb-6 md:tw-mb-8">
              Get started with AIMS
            </h2>
            <BookACall />
          </div>
        </motion.div>
        {/* Your Campaign Your Way Section */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="tw-mt-16 sm:tw-mt-20 md:tw-mt-32 lg:tw-mt-56 tw-flex tw-flex-col tw-items-center tw-justify-center tw-gap-6 sm:tw-gap-8 md:tw-gap-16"
        >
          <div className="tw-max-w-3xl tw-w-full md:tw-w-auto">
            <h2 className="tw-text-3xl sm:tw-text-5xl tw-font-bold tw-text-aims-text-primary tw-mb-6 md:tw-mb-8 tw-text-center">
              Your Campaign, Your Way
            </h2>
            <p className="tw-text-aims-dark-7 tw-mb-6 md:tw-mb-8 tw-text-center">
              From original content creation to live event appearances, AIMS
              offers a range of campaign types designed to boost your brand
              visibility, engagement, and credibility. Choose the approach that
              best fits your goals and connect with athletes who bring your
              vision to life.
            </p>
          </div>
          <div className="tw-grid tw-grid-cols-1 sm:tw-grid-cols-2 md:tw-grid-cols-3 tw-gap-8 sm:tw-gap-12 lg:tw-gap-16 tw-max-w-5xl tw-w-full tw-place-items-center">
            <BlueCircleIcon
              icon={GiftIcon}
              label="Product Reviews / Gifted Collaborations"
            />
            <BlueCircleIcon icon={HeroShareIcon} label="Content Sharing" />
            <BlueCircleIcon icon={LightBulbIcon} label="Content Creation" />
            <BlueCircleIcon icon={WrenchIcon} label="Custom Campaigns" />
            <BlueCircleIcon icon={CameraIcon} label="Photo and Video Shoots" />
            <BlueCircleIcon icon={UserGroupIcon} label="In-Person Events" />
          </div>
          {/* Call-to-action section below blue circles */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
            transition={{ duration: 0.6, delay: 0.8 }}
            className="tw-mt-16 tw-w-full tw-flex tw-justify-center"
          >
            <div className="tw-relative tw-w-full tw-flex">
              {/* Image Card with offset */}
              <div className="tw-hidden lg:tw-block tw-relative tw-z-10 tw-rounded-xl tw-overflow-hidden">
                <Image
                  src="/landing-page/soccer-player.png"
                  alt="College athlete soccer player"
                  width={411}
                  height={405}
                  className="tw-rounded-xl tw-object-cover tw-mr-[100px] tw-my-14"
                  style={{
                    background: `radial-gradient(160% 100% at 20% 20%, #91c6f8 0%, #61e2f5 75%, #aef9fd 100%), 
                    linear-gradient(to right, rgba(0, 16, 48, 0.05), rgba(0, 0, 0, 0.09))`,
                  }}
                />
              </div>
              {/* Blue background with text/buttons */}
              <div className="tw-flex-1 tw-bg-gradient-to-r tw-from-[#19408c] tw-via-[#153477] tw-to-[#19408c] tw-rounded-2xl tw-py-8 sm:tw-py-12 lg:tw-py-16 tw-px-6 sm:tw-px-8 lg:tw-pl-8 lg:tw-pr-32 tw-flex tw-flex-col tw-justify-center tw-items-center lg:tw-items-end tw-z-0 tw-min-h-[320px] sm:tw-min-h-[380px] md:tw-min-h-[520px] tw-shadow-lg xl:tw-ml-[-400px]">
                <div className="tw-flex tw-flex-col tw-max-w-xl tw-text-center lg:tw-text-left">
                  <h3 className="tw-text-2xl sm:tw-text-3xl lg:tw-text-4xl tw-font-bold tw-text-aims-text-primary tw-mb-3 sm:tw-mb-4 tw-leading-tight">
                    Start Connecting with College Athletes
                  </h3>
                  <p className="tw-text-aims-dark-7 tw-mb-6 tw-text-sm sm:tw-text-base tw-leading-relaxed">
                    Join AIMS today and let us manage your game changing
                    marketing campaigns
                  </p>
                  <div className="tw-flex tw-flex-col sm:tw-flex-row tw-gap-3 sm:tw-gap-4 tw-justify-center lg:tw-justify-start">
                    <div className="tw-w-full sm:tw-w-auto">
                      <BookACall />
                    </div>
                    <Button asChild variant="ghost" className="tw-w-full sm:tw-w-auto tw-h-12 sm:tw-h-10 tw-text-sm sm:tw-text-base">
                      <Link href="/auth/register?userType=athlete" className="tw-flex tw-items-center tw-justify-center tw-min-h-[44px]">
                        Athlete Registration
                      </Link>
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
}
