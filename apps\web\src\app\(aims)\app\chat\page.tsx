"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { ChatList } from "@/components/chat/ChatList";
import { ChatRoom } from "@/components/chat/ChatRoom";
import { trpc } from "@/lib/trpc/client";
import { useAuth } from "@/hooks/use-auth";
import { FullPageLoadingSpinner } from "@/components/ui/LoadingSpinner";

export default function ChatPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user } = useAuth();
  const [selectedChatId, setSelectedChatId] = useState<string | null>(
    searchParams.get("id")
  );
  const [isDesktop, setIsDesktop] = useState(false);

  // Fetch all chats to check if any exist
  const { data: chats, isLoading: chatsLoading } = trpc.chat.getChats.useQuery();

  // Fetch chat details for desktop view
  const { data: chat, isLoading } = trpc.chat.getChatById.useQuery(
    { chatId: selectedChatId || "" },
    { enabled: !!selectedChatId && isDesktop }
  );

  // Get the other participant (not the current user)
  const otherParticipant = chat?.participants.find(
    (p) => p.id !== user?.id
  );

  // Handle responsive layout
  useEffect(() => {
    const checkScreenSize = () => {
      setIsDesktop(window.innerWidth >= 1024);
    };

    // Initial check
    checkScreenSize();

    // Add event listener for window resize
    window.addEventListener("resize", checkScreenSize);

    // Cleanup
    return () => window.removeEventListener("resize", checkScreenSize);
  }, []);

  // Handle chat selection
  const handleChatSelect = (chatId: string) => {
    if (isDesktop) {
      setSelectedChatId(chatId);
      // Update URL without navigation
      const newUrl = `${window.location.pathname}?id=${chatId}`;
      window.history.pushState({ path: newUrl }, "", newUrl);
    } else {
      // Navigate to chat page on mobile
      router.push(`/app/chat/${chatId}`);
    }
  };

  return (
    <div className="tw-flex tw-flex-col lg:tw-flex-row tw-h-[calc(100vh-64px)]">
      {/* Chat List Column */}
      <div className="tw-w-full lg:tw-w-1/3 tw-overflow-hidden">
        <div className="tw-p-6">
          <h1 className="tw-text-2xl tw-font-bold tw-mb-4">Messages</h1>
          <ChatList onChatSelect={handleChatSelect} selectedChatId={selectedChatId} />
        </div>
      </div>

      {/* Chat Room Column (Desktop only) */}
      {isDesktop && (
        <div className="tw-hidden lg:tw-block lg:tw-w-2/3 tw-overflow-hidden">
          {chatsLoading ? (
            <div className="tw-flex tw-items-center tw-justify-center tw-h-full">
              <FullPageLoadingSpinner />
            </div>
          ) : !chats || chats.length === 0 ? (
            <div className="tw-flex tw-items-center tw-justify-center tw-h-full">
              <div className="tw-text-center tw-p-6">
                <h2 className="tw-text-xl tw-font-semibold tw-mb-2">No conversations yet</h2>
                <p className="tw-text-aims-text-secondary">
                  Start a conversation by messaging someone from their profile
                </p>
              </div>
            </div>
          ) : selectedChatId ? (
            isLoading ? (
              <div className="tw-flex tw-items-center tw-justify-center tw-h-full">
                <FullPageLoadingSpinner />
              </div>
            ) : (
              <ChatRoom chatId={selectedChatId} otherParticipant={otherParticipant} />
            )
          ) : (
            <div className="tw-flex tw-items-center tw-justify-center tw-h-full">
              <div className="tw-text-center tw-p-6">
                <h2 className="tw-text-xl tw-font-semibold tw-mb-2">Select a conversation</h2>
                <p className="tw-text-aims-text-secondary">
                  Choose a chat from the list to start messaging
                </p>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
