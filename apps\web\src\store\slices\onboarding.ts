import { createSlice, PayloadAction } from "@reduxjs/toolkit";

import { RootState } from "..";

export type AthleteBasicInfo = {
  college: string;
  sport: string;
  position: string;
  birthDate: string;
  gender: string;
  profilePictureUrl: string | null;
  yearInSchool?: string;
  bio?: string;
  hometown?: string;
};

export type AthletePaymentInfo = {
  shoot: number | null;
  inPerson: number | null;
  contentShare: number | null;
  contentCreation: number | null;
  giftedCollab: number | null;
  other: number | null;
  referralSource: string;
  referralName: string | null;
  referralVenmo: string | null;
};

export type BrandBasicInfo = {
  companyName: string;
  industry: string;
  website: string;
  location: string;
  description: string;
  logoUrl: string | null;
};

export type OnboardingState = {
  currentStep: number;
  userType: "athlete" | "brand" | null;
  athleteInfo: {
    basicInfo: AthleteBasicInfo;
    interests: {
      businessInterests: string[];
    };
    paymentInfo: AthletePaymentInfo;
  };
  brandInfo: {
    basicInfo: BrandBasicInfo;
  };
  socialMedia: {
    instagram: string;
    twitter: string;
    tiktok: string;
    youtube: string;
  };
};

export const initialState: OnboardingState = {
  currentStep: 1,
  userType: null,
  athleteInfo: {
    basicInfo: {
      college: "",
      sport: "",
      position: "",
      birthDate: "",
      gender: "",
      profilePictureUrl: null,
      yearInSchool: "",
      bio: "",
      hometown: "",
    },
    interests: {
      businessInterests: [],
    },
    paymentInfo: {
      shoot: null,
      inPerson: null,
      contentShare: null,
      contentCreation: null,
      giftedCollab: null,
      other: null,
      referralSource: "",
      referralName: null,
      referralVenmo: null,
    },
  },
  brandInfo: {
    basicInfo: {
      companyName: "",
      industry: "",
      website: "",
      location: "",
      description: "",
      logoUrl: null,
    },
  },
  socialMedia: {
    instagram: "",
    twitter: "",
    tiktok: "",
    youtube: "",
  },
};

export const onboardingSlice = createSlice({
  name: "onboarding",
  initialState,
  reducers: {
    setCurrentStep: (state, action: PayloadAction<number>) => {
      state.currentStep = action.payload;
    },
    setUserType: (state, action: PayloadAction<"athlete" | "brand">) => {
      state.userType = action.payload;
    },
    updateAthleteBasicInfo: (
      state,
      action: PayloadAction<Partial<AthleteBasicInfo>>,
    ) => {
      state.athleteInfo.basicInfo = {
        ...state.athleteInfo.basicInfo,
        ...action.payload,
      };
    },
    updateAthletePaymentInfo: (
      state,
      action: PayloadAction<Partial<AthletePaymentInfo>>,
    ) => {
      state.athleteInfo.paymentInfo = {
        ...state.athleteInfo.paymentInfo,
        ...action.payload,
      };
    },
    updateBrandBasicInfo: (
      state,
      action: PayloadAction<Partial<BrandBasicInfo>>,
    ) => {
      state.brandInfo.basicInfo = {
        ...state.brandInfo.basicInfo,
        ...action.payload,
      };
    },
    updateInterests: (
      state,
      action: PayloadAction<OnboardingState["athleteInfo"]["interests"]>,
    ) => {
      state.athleteInfo.interests = action.payload;
    },
    updateSocialMedia: (
      state,
      action: PayloadAction<OnboardingState["socialMedia"]>,
    ) => {
      state.socialMedia = action.payload;
    },
    resetOnboarding: () => initialState,
  },
});

export const {
  setCurrentStep,
  setUserType,
  updateAthleteBasicInfo,
  updateAthletePaymentInfo,
  updateBrandBasicInfo,
  updateInterests,
  updateSocialMedia,
  resetOnboarding,
} = onboardingSlice.actions;

// Base selectors
export const selectOnboarding = (state: RootState) => state.onboarding;
export const selectCurrentStep = (state: RootState) =>
  state.onboarding.currentStep;
export const selectUserType = (state: RootState) => state.onboarding.userType;

// Athlete selectors
export const selectAthleteBasicInfo = (state: RootState) =>
  state.onboarding.athleteInfo.basicInfo;
export const selectAthletePaymentInfo = (state: RootState) =>
  state.onboarding.athleteInfo.paymentInfo;
export const selectInterests = (state: RootState) =>
  state.onboarding.athleteInfo.interests;

// Brand selectors
export const selectBrandBasicInfo = (state: RootState) =>
  state.onboarding.brandInfo.basicInfo;

// Shared selectors
export const selectSocialMedia = (state: RootState) =>
  state.onboarding.socialMedia;

export const onboardingReducer = onboardingSlice.reducer;
