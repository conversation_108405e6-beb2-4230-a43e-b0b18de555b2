import { MessageType } from "@repo/server/src/types/chat";

export interface MessageSenderInterface {
  id: string;
  name: string;
  userType: string;
  profilePicture?: {
    url: string;
    key: string;
    uploadedAt: string;
  } | null;
  brand?: {
    id: string;
    name: string;
  } | null;
}

export interface MessageInterface {
  id: string;
  chatId: string;
  content: string;
  type: MessageType;
  campaignId?: string;
  contractId?: string;
  sender?: MessageSenderInterface;
  readBy: string[];
  createdAt: string;
  updatedAt: string;
  isOptimistic?: boolean;
}