import jwt from "jsonwebtoken";

import { ExtendedTRPCError } from "../utils/trpc";

export const verifyToken = async (token: string) => {
  try {
    if (!process.env.JWT_SECRET) {
      throw new ExtendedTRPCError(
        "INTERNAL_SERVER_ERROR",
        "JWT secret is not configured",
      );
    }

    const payload = jwt.verify(token, process.env.JWT_SECRET) as { id: string };
    if (!payload.id) {
      throw new ExtendedTRPCError("UNAUTHORIZED", "Invalid token payload");
    }

    return payload;
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      throw new ExtendedTRPCError("UNAUTHORIZED", "Token has expired");
    }
    throw new ExtendedTRPCError("UNAUTHORIZED", "Invalid token");
  }
};
