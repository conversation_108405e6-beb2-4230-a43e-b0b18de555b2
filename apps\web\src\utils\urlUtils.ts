/**
 * Utility functions for URL generation and validation
 */

/**
 * Gets the base URL for the application
 * Handles both client-side and server-side environments
 */
export const getAppBaseUrl = (): string => {
  // Client-side: Use window.location.origin
  if (typeof window !== 'undefined') {
    const origin = window.location.origin;
    if (origin && origin !== 'null' && origin !== 'undefined') {
      return origin;
    }
    
    // Fallback: construct URL manually
    const protocol = window.location.protocol || 'http:';
    const hostname = window.location.hostname || 'localhost';
    const port = window.location.port ? `:${window.location.port}` : '';
    return `${protocol}//${hostname}${port}`;
  }
  
  // Server-side: Use environment variable or default
  return process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3010';
};

/**
 * Validates a URL and returns detailed information about it
 */
export const validateUrl = (url: string): { isValid: boolean; error?: string; details?: any } => {
  try {
    const urlObj = new URL(url);
    
    // Check for valid protocol
    if (!urlObj.protocol.startsWith('http')) {
      return {
        isValid: false,
        error: `Invalid protocol: ${urlObj.protocol}. Must be HTTP or HTTPS.`
      };
    }
    
    // Check for valid hostname
    if (!urlObj.hostname) {
      return {
        isValid: false,
        error: 'Missing hostname in URL'
      };
    }
    
    return {
      isValid: true,
      details: {
        href: urlObj.href,
        protocol: urlObj.protocol,
        hostname: urlObj.hostname,
        port: urlObj.port,
        pathname: urlObj.pathname,
        search: urlObj.search
      }
    };
  } catch (error) {
    return {
      isValid: false,
      error: error instanceof Error ? error.message : 'Invalid URL format'
    };
  }
};

/**
 * Creates Stripe Connect redirect URLs with proper validation
 */
export const createStripeConnectUrls = (basePath: string = '/app/athlete/wallet') => {
  const baseUrl = getAppBaseUrl();
  const cleanBaseUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;
  
  const returnUrl = `${cleanBaseUrl}${basePath}?setup=complete`;
  const refreshUrl = `${cleanBaseUrl}${basePath}?setup=refresh`;
  
  // Validate both URLs
  const returnValidation = validateUrl(returnUrl);
  const refreshValidation = validateUrl(refreshUrl);
  
  if (!returnValidation.isValid) {
    throw new Error(`Invalid return URL: ${returnValidation.error}`);
  }
  
  if (!refreshValidation.isValid) {
    throw new Error(`Invalid refresh URL: ${refreshValidation.error}`);
  }
  
  return {
    returnUrl,
    refreshUrl,
    baseUrl: cleanBaseUrl,
    validation: {
      returnUrl: returnValidation.details,
      refreshUrl: refreshValidation.details
    }
  };
};
