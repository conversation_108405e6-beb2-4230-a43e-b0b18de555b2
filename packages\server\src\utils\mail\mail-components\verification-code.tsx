import React from "react";

import { EmailTemplate } from "./EmailTemplate";

interface VerificationCodeProps {
  code: string;
  email: string;
}

export const VerificationCode: React.FC<VerificationCodeProps> = ({
  code,
  email,
}) => {
  return (
    <EmailTemplate email={email}>
      <div
        style={{
          width: "100%",
          borderRadius: "10px",
          backgroundColor: "#111928",
          padding: "32px 0 24px 0",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <p
          style={{
            color: "#DEE4EE",
            fontSize: "1.1rem",
            marginBottom: "18px",
            textAlign: "center",
            fontWeight: 500,
          }}
        >
          Use the following code to log in:
        </p>
        <div
          style={{
            backgroundColor: "#FC8D00",
            color: "#000000",
            borderRadius: "10px",
            padding: "16px 40px",
            fontSize: "2rem",
            fontWeight: 700,
            letterSpacing: "0.2em",
            marginBottom: "12px",
            boxShadow: "0 2px 8px rgba(0,0,0,0.08)",
            textAlign: "center",
            fontFamily: "'Poppins', Arial, sans-serif",
            display: "block"
          }}
        >
          {code}
        </div>
        <p
          style={{
            color: "#6B7280",
            fontSize: "0.95rem",
            marginTop: "8px",
            textAlign: "center",
          }}
        >
          This code will expire in 10 minutes.
        </p>
      </div>
    </EmailTemplate>
  );
};
