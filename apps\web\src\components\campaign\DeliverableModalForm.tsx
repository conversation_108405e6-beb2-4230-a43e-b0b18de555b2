import React, { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  Di<PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/components/ui/toast/use-toast";

import { DeliverableType } from "@repo/server/src/types/deliverable";

import { Textarea } from "../ui/textarea";
import { DeliverableInput, deliverableTypeOptions } from "./DeliverablesForm";

// Add content types constant
const CONTENT_TYPES = [
  "Instagram Post",
  "Instagram Story",
  "Instagram Reel",
  "TikTok Video",
  "Twitter/X Post",
];

interface ContentSelectionProps {
  content: string[];
  onChange: (content: string[]) => void;
  label?: string;
}

function ContentSelection({
  content,
  onChange,
  label = "Content",
}: ContentSelectionProps) {
  const [input, setInput] = useState<string>("");

  return (
    <div className="tw-w-full tw-col-span-1 md:tw-col-span-2">
      <div className="tw-w-full tw-grid tw-grid-cols-1 md:tw-grid-cols-2 tw-gap-4 tw-items-start">
        {/* Left column - Selected Content */}
        <div>
          <Label required>{label}</Label>
          <div className="tw-flex tw-flex-col tw-gap-2">
            <div className="tw-flex tw-flex-wrap tw-gap-2 tw-mb-1">
              {(content || []).map((item) => (
                <span
                  key={item}
                  className="tw-bg-aims-primary tw-text-black tw-px-3 tw-py-1 tw-rounded-full tw-flex tw-items-center tw-gap-1 tw-text-sm tw-font-semibold"
                >
                  {item}
                  <button
                    type="button"
                    onClick={() =>
                      onChange((content || []).filter((c) => c !== item))
                    }
                    className="tw-ml-1 tw-text-black hover:tw-text-red-600"
                    aria-label={`Remove ${item}`}
                  >
                    ×
                  </button>
                </span>
              ))}
            </div>
            <Input
              placeholder="Enter a new content type or search for an existing one"
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === "Enter" && input.trim()) {
                  e.preventDefault();
                  onChange([...(content || []), input.trim()]);
                  setInput("");
                }
              }}
              className="tw-bg-aims-dark-4"
            />
          </div>
        </div>

        {/* Right column - Content Type Suggestions */}
        <div>
          <Label>Available Content Types</Label>
          <div className="tw-flex tw-flex-wrap tw-gap-2 tw-max-h-[200px] tw-overflow-y-auto">
            {CONTENT_TYPES.filter(
              (type) =>
                type.toLowerCase().includes(input.toLowerCase()) &&
                !(content || []).includes(type),
            ).map((type) => (
              <button
                key={type}
                type="button"
                onClick={() => onChange([...(content || []), type])}
                className="tw-px-3 tw-py-1 tw-rounded-full tw-text-sm tw-font-semibold tw-border tw-border-gray-600 tw-bg-gray-700 tw-text-gray-200 hover:tw-bg-aims-primary hover:tw-text-black"
              >
                {type}
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

const initialDeliverable: DeliverableInput = {
  name: "",
  daysToComplete: 0,
  minimumPayment: 0,
  description: "",
  type: DeliverableType.PHOTO_VIDEO_SHOOT,
};

interface DeliverableModalFormProps {
  open: boolean;
  onSubmit: (d: DeliverableInput) => void;
  onCancel: () => void;
  initialValues?: DeliverableInput;
}

export function DeliverableModalForm({
  open,
  onSubmit,
  onCancel,
  initialValues,
}: DeliverableModalFormProps) {
  const [deliverable, setDeliverable] = useState<DeliverableInput>(
    initialValues || initialDeliverable,
  );
  const { toast } = useToast();

  // Reset form when initialValues changes
  useEffect(() => {
    if (initialValues) {
      setDeliverable(initialValues);
    } else {
      setDeliverable(initialDeliverable);
    }
  }, [initialValues]);

  const handleAdd = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate basic required fields
    if (!deliverable.name.trim()) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Deliverable name is required",
      });
      return;
    }

    if (!deliverable.description.trim()) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Deliverable description is required",
      });
      return;
    }

    if (deliverable.daysToComplete <= 0) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Days to complete must be greater than 0",
      });
      return;
    }

    if (deliverable.minimumPayment < 0) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Minimum payment cannot be negative",
      });
      return;
    }

    // Validate type-specific required fields
    switch (deliverable.type) {
      case DeliverableType.PHOTO_VIDEO_SHOOT:
        if (!deliverable.location?.trim()) {
          toast({
            variant: "destructive",
            title: "Error",
            description: "Location is required for photo/video shoots",
          });
          return;
        }
        if (!deliverable.date?.trim()) {
          toast({
            variant: "destructive",
            title: "Error",
            description: "Date is required for photo/video shoots",
          });
          return;
        }
        break;

      case DeliverableType.IN_PERSON:
        if (!deliverable.location?.trim()) {
          toast({
            variant: "destructive",
            title: "Error",
            description: "Location is required for in-person events",
          });
          return;
        }
        if (!deliverable.date?.trim()) {
          toast({
            variant: "destructive",
            title: "Error",
            description: "Date is required for in-person events",
          });
          return;
        }
        break;

      case DeliverableType.CONTENT_SHARE:
        if (!deliverable.content || deliverable.content.length === 0) {
          toast({
            variant: "destructive",
            title: "Error",
            description: "At least one content type is required for content sharing",
          });
          return;
        }
        break;

      case DeliverableType.GIFTED_COLLABORATION:
        if (!deliverable.productName?.trim()) {
          toast({
            variant: "destructive",
            title: "Error",
            description: "Product name is required for gifted collaborations",
          });
          return;
        }
        if (deliverable.productPrice === undefined || deliverable.productPrice < 0) {
          toast({
            variant: "destructive",
            title: "Error",
            description: "Product price is required and must be non-negative for gifted collaborations",
          });
          return;
        }
        break;
    }

    // All validations passed
    onSubmit({ ...deliverable });
    setDeliverable(initialDeliverable);
    toast({
      variant: "success",
      title: "Success",
      description: "Deliverable saved successfully",
    });
  };

  const handleCancel = () => {
    setDeliverable(initialDeliverable);
    onCancel();
  };

  const renderTypeSpecificFields = () => {
    switch (deliverable.type) {
      case DeliverableType.PHOTO_VIDEO_SHOOT:
        return (
          <>
            <div>
              <Label required>Location</Label>
              <Input
                value={deliverable.location || ""}
                onChange={(e) =>
                  setDeliverable({ ...deliverable, location: e.target.value })
                }
                placeholder="Enter shoot location"
                className="tw-bg-aims-dark-4"
              />
            </div>
            <div>
              <Label required>Date</Label>
              <Input
                type="date"
                value={deliverable.date || ""}
                onChange={(e) =>
                  setDeliverable({
                    ...deliverable,
                    date: e.target.value,
                  })
                }
                placeholder="Enter date"
                className="tw-bg-aims-dark-4"
              />
            </div>
            <div>
              <Label>Time</Label>
              <Input
                value={deliverable.time || ""}
                onChange={(e) =>
                  setDeliverable({
                    ...deliverable,
                    time: e.target.value,
                  })
                }
                placeholder="Enter shoot time"
                className="tw-bg-aims-dark-4 no-spinner"
              />
            </div>
          </>
        );
      case DeliverableType.IN_PERSON:
        return (
          <>
            <div>
              <Label required>Location</Label>
              <Input
                value={deliverable.location || ""}
                onChange={(e) =>
                  setDeliverable({ ...deliverable, location: e.target.value })
                }
                placeholder="Enter event location"
                className="tw-bg-aims-dark-4"
              />
            </div>
            <div>
              <Label required>Date</Label>
              <Input
                type="date"
                value={deliverable.date || ""}
                onChange={(e) =>
                  setDeliverable({ ...deliverable, date: e.target.value })
                }
                className="tw-bg-aims-dark-4"
              />
            </div>
            <div>
              <Label>Time</Label>
              <Input
                value={deliverable.time || ""}
                onChange={(e) =>
                  setDeliverable({
                    ...deliverable,
                    time: e.target.value,
                  })
                }
                placeholder="Enter event time"
                className="tw-bg-aims-dark-4 no-spinner"
              />
            </div>
          </>
        );
      case DeliverableType.CONTENT_CREATION:
        return (
          <ContentSelection
            content={deliverable.content || []}
            onChange={(content) => setDeliverable({ ...deliverable, content })}
          />
        );
      case DeliverableType.CONTENT_SHARE:
        return (
          <ContentSelection
            content={deliverable.content || []}
            onChange={(content) => setDeliverable({ ...deliverable, content })}
            label="Content to Share"
          />
        );
      case DeliverableType.GIFTED_COLLABORATION:
        return (
          <>
            <div>
              <Label required>Product Name</Label>
              <Input
                value={deliverable.productName || ""}
                onChange={(e) =>
                  setDeliverable({
                    ...deliverable,
                    productName: e.target.value,
                  })
                }
                placeholder="Enter product name"
                className="tw-bg-aims-dark-4"
              />
            </div>
            <div>
              <Label required>Product Price</Label>
              <div className="tw-relative">
                <span className="tw-absolute tw-left-3 tw-top-1/2 tw--translate-y-1/2 tw-text-aims-text-secondary">
                  $
                </span>
                <Input
                  type="number"
                  value={
                    deliverable.productPrice === undefined
                      ? ""
                      : deliverable.productPrice
                  }
                  onChange={(e) =>
                    setDeliverable({
                      ...deliverable,
                      productPrice:
                        e.target.value === ""
                          ? undefined
                          : parseInt(e.target.value),
                    })
                  }
                  placeholder="Enter product price"
                  className="tw-bg-aims-dark-4 tw-pl-8 no-spinner"
                />
              </div>
            </div>
          </>
        );
      default:
        return null;
    }
  };

  return (
    <Dialog
      open={open}
      onOpenChange={(open) => {
        if (!open) handleCancel();
      }}
    >
      <DialogContent className="tw-flex tw-flex-col tw-p-6 tw-max-w-7xl tw-max-h-[700px]">
        <DialogHeader className="tw-flex tw-items-center tw-justify-between">
          <DialogTitle className="tw-text-aims-text-primary tw-text-xl tw-font-bold">
            {initialValues ? "Edit Deliverable" : "New Deliverable"}
          </DialogTitle>
        </DialogHeader>
        <hr className="tw-border-aims-dark-4" />
        <form
          onSubmit={handleAdd}
          className="tw-flex tw-flex-col tw-gap-6 tw-flex-1 tw-h-full tw-my-auto"
        >
          {/* Top row: Name, Days, Payment */}
          <div className="tw-flex-1 tw-flex tw-flex-col tw-gap-2 tw-overflow-auto">
            <div className="tw-grid tw-grid-cols-1 md:tw-grid-cols-3 tw-gap-2">
              <div>
                <Label required>Deliverable name</Label>
                <Input
                  value={deliverable.name}
                  onChange={(e) =>
                    setDeliverable({ ...deliverable, name: e.target.value })
                  }
                  placeholder="Name"
                  className="tw-bg-aims-dark-4"
                />
              </div>
              <div>
                <Label required>Days to complete</Label>
                <div className="tw-relative">
                  <Input
                    type="number"
                    value={deliverable.daysToComplete || ""}
                    onChange={(e) =>
                      setDeliverable({
                        ...deliverable,
                        daysToComplete: e.target.value
                          ? parseInt(e.target.value)
                          : 0,
                      })
                    }
                    className="tw-bg-aims-dark-4 tw-pr-10 no-spinner"
                  />
                  <span className="tw-absolute tw-right-3 tw-top-1/2 tw--translate-y-1/2 tw-text-aims-text-secondary">
                    Days
                  </span>
                </div>
                <div className="tw-text-xs tw-text-aims-text-secondary tw-mt-1">
                  Enter the number of days the participant has to complete the
                  deliverable
                </div>
              </div>
              <div>
                <Label required>Minimum payment</Label>
                <div className="tw-relative">
                  <span className="tw-absolute tw-left-3 tw-top-1/2 tw--translate-y-1/2 tw-text-aims-text-secondary">
                    $
                  </span>
                  <Input
                    type="number"
                    min={0}
                    value={deliverable.minimumPayment}
                    onChange={(e) =>
                      setDeliverable({
                        ...deliverable,
                        minimumPayment: parseInt(e.target.value),
                      })
                    }
                    className="tw-bg-aims-dark-4 tw-pl-8 no-spinner"
                  />
                </div>
                <div className="tw-text-xs tw-text-aims-text-secondary tw-mt-1">
                  Enter the minimum amount you are willing to pay the
                  participant
                </div>
              </div>
            </div>
            {/* Description */}
            <div>
              <Label required>Deliverable description</Label>
              {/* Replace with a rich text editor if you have one */}
              <Textarea
                value={deliverable.description}
                onChange={(e) =>
                  setDeliverable({
                    ...deliverable,
                    description: e.target.value,
                  })
                }
                placeholder="Write text here ..."
                className="tw-bg-aims-dark-4 tw-w-full tw-h-32 tw-p-3"
              />
            </div>
            {/* Type */}
            <div className="tw-mb-4">
              <Label required>Type</Label>
              <Select
                value={deliverable.type}
                onValueChange={(value) =>
                  setDeliverable({
                    ...deliverable,
                    type: value as DeliverableType,
                  })
                }
              >
                <SelectTrigger className="!tw-bg-aims-dark-4 tw-text-aims-text-primary tw-rounded-lg">
                  <SelectValue placeholder="Select the type of deliverable" />
                </SelectTrigger>
                <SelectContent>
                  {deliverableTypeOptions.map((opt) => (
                    <SelectItem key={opt.value} value={opt.value}>
                      {opt.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            {/* Type-specific fields */}
            <div className="tw-grid tw-grid-cols-1 md:tw-grid-cols-2 tw-gap-4">
              {renderTypeSpecificFields()}
            </div>
          </div>
          {/* Footer */}
          <DialogFooter className="tw-flex tw-justify-end tw-gap-4">
            <DialogClose asChild>
              <Button
                type="button"
                variant="ghost"
                onClick={handleCancel}
                className="tw-bg-aims-dark-5 tw-text-aims-text-primary tw-rounded-full tw-px-8 tw-py-2"
              >
                Cancel
              </Button>
            </DialogClose>
            <Button
              type="submit"
              className=" tw-rounded-full tw-px-8 tw-py-2 tw-font-semibold"
            >
              Save
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
