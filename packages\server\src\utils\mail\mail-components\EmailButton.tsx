import React from "react";

interface EmailButtonProps {
  href: string;
  children: React.ReactNode;
}

export const EmailButton: React.FC<EmailButtonProps> = ({ href, children }) => (
  <div style={{ width: "60%", margin: "24px auto" }}>
    <a
      href={href}
      style={{
        display: "block",
        backgroundColor: "#FC8D00",
        borderRadius: "10px",
        width: "100%",
        height: "40px",
        lineHeight: "40px",
        textAlign: "center",
        color: "#000000",
        fontWeight: 700,
        textDecoration: "none",
        fontFamily: "'Poppins', Arial, sans-serif",
        fontSize: "1rem",
        cursor: "pointer",
      }}
    >
      {children}
    </a>
  </div>
);
