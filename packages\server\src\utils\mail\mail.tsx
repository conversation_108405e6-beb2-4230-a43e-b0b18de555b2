import React from "react";
import nodemailer from "nodemailer";
import React<PERSON><PERSON><PERSON>erver from "react-dom/server";

import { addEmailLog } from "./AddEmailLog";
import { SendPasswordReset } from "./mail-components/password-reset";
import { PasswordResetSuccessful } from "./mail-components/password-reset-success";
import { VerificationCode } from "./mail-components/verification-code";
import { VerificationEmail } from "./mail-components/verification-email";
import { NewEmailVerification } from "./mail-components/verify-new-email";

const trans = {
  host: process.env.MAIL_TRAP_HOST,
  port: parseInt(process.env.MAIL_TRAP_PORT!!),
  auth: {
    user: process.env.MAIL_TRAP_USER,
    pass: process.env.MAIL_TRAP_PASSWORD,
  },
};

var transport = nodemailer.createTransport(trans);

const sendVerification = async (email: string, link: string): Promise<void> => {
  const element: React.ReactElement = (
    <VerificationEmail vLink={link} email={email} />
  );
  const compiledHTML = ReactDOMServer.renderToStaticMarkup(element);
  await addEmailLog(
    process.env.MAIL_TRAP_FROM_EMAIL || "<EMAIL>",
    email,
    "Verification Email Sent",
  );

  await transport.sendMail({
    from: process.env.MAIL_TRAP_FROM_EMAIL,
    subject: "AIMS Account Verification",
    to: email,
    html: compiledHTML,
  });
};

const sendPasswordResetLink = async (email: string, link: string) => {
  const compiledHTML = ReactDOMServer.renderToStaticMarkup(
    <SendPasswordReset rLink={link} email={email} />,
  );
  await addEmailLog(
    process.env.MAIL_TRAP_FROM_EMAIL || "<EMAIL>",
    email,
    "Password Reset",
  );

  await transport.sendMail({
    from: process.env.MAIL_TRAP_FROM_EMAIL,
    subject: "AIMS Password Reset",
    to: email,
    html: compiledHTML,
  });
};

const sendPasswordUpdateMessage = async (email: string) => {
  const compiledHTML = ReactDOMServer.renderToStaticMarkup(
    <PasswordResetSuccessful email={email} />,
  );
  await addEmailLog(
    process.env.MAIL_TRAP_FROM_EMAIL || "<EMAIL>",
    email,
    "Password Updated",
  );

  await transport.sendMail({
    from: process.env.MAIL_TRAP_FROM_EMAIL,
    subject: "AIMS Password Updated",
    to: email,
    html: compiledHTML,
  });
};

const sendVerificationCode = async (email: string, code: string) => {
  const compiledHTML = ReactDOMServer.renderToStaticMarkup(
    <VerificationCode code={code} email={email} />,
  );
  await addEmailLog(
    process.env.MAIL_TRAP_FROM_EMAIL || "<EMAIL>",
    email,
    "Verification Code Sent",
  );

  await transport.sendMail({
    from: process.env.MAIL_TRAP_FROM_EMAIL,
    subject: "AIMS Login Verification Code",
    to: email,
    html: compiledHTML,
  });
};

const sendNewEmailVerification = async (email: string, link: string) => {
  const compiledHTML = ReactDOMServer.renderToStaticMarkup(
    <NewEmailVerification vLink={link} email={email} />,
  );
  await addEmailLog(
    process.env.MAIL_TRAP_FROM_EMAIL || "<EMAIL>",
    email,
    "Email Verification Sent",
  );

  await transport.sendMail({
    from: process.env.MAIL_TRAP_FROM_EMAIL,
    subject: "AIMS Change Email Verification",
    to: email,
    html: compiledHTML,
  });
};

const mail = {
  sendVerification,
  sendPasswordResetLink,
  sendPasswordUpdateMessage,
  sendVerificationCode,
  sendNewEmailVerification,
};

export default mail;
