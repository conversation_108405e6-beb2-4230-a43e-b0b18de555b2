"use client";

import { useCallback, useEffect, useState } from "react";
import { useParams, useRouter } from "next/navigation";
import { AppLayout } from "@/components/ui/AppLayout";
import { Button } from "@/components/ui/button";
import { LoadingSpinner } from "@/components/ui/LoadingSpinner";
import { useAuth } from "@/hooks/use-auth";
import { client } from "@/lib/trpc/client";
import {
  resetCampaign,
  selectEditBasicInfo,
  selectEditCurrentStep,
  setBasicInfo,
  setCurrentStep,
} from "@/store/slices/editCampaign";
import { useDispatch, useSelector } from "react-redux";

import { CampaignStatus } from "@repo/server/src/types/campaign";

export default function CampaignEditLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const router = useRouter();
  const dispatch = useDispatch();
  const { getProfile } = useAuth();
  const currentStep = useSelector(selectEditCurrentStep);
  const basicInfo = useSelector(selectEditBasicInfo);
  const params = useParams();
  const campaignId =
    typeof params?.id === "string"
      ? params.id
      : Array.isArray(params?.id) && params?.id.length > 0
        ? params.id[0]
        : undefined;
  const isEditMode = !!campaignId;
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState("");
  const [updateError, setUpdateError] = useState("");
  const [loading, setLoading] = useState(true);

  const TOTAL_STEPS = 5; // Basic Info, Requirements, Deliverables, and Review

  const checkAuth = useCallback(async () => {
    const profile = await getProfile();
    if (!profile) {
      router.push("/auth/login");
    }
  }, [getProfile, router]);

  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  const steps = [
    `/campaign/${campaignId}`,
    `/campaign/${campaignId}/interests`,
    `/campaign/${campaignId}/deliverables`,
    `/campaign/${campaignId}/additional-info`,
    `/campaign/${campaignId}/review`,
  ];

  const validateBasicInfo = () => {
    if (
      !basicInfo.name ||
      !basicInfo.description ||
      !basicInfo.startDate ||
      !basicInfo.endDate
    ) {
      setError(
        "Please fill in all required fields in the basic information section.",
      );
      dispatch(setCurrentStep(1));
      router.push(steps[0]);
      return false;
    }
    if (basicInfo.description.length < 10) {
      setError("Description must be at least 10 characters long.");
      dispatch(setCurrentStep(1));
      router.push(steps[0]);
      return false;
    }
    return true;
  };

  const validateRequirements = () => {
    if (basicInfo.deliverables.length === 0) {
      setError("Please add at least one item to the deliverables section.");
      dispatch(setCurrentStep(3));
      router.push(steps[2]);
      return false;
    }
    return true;
  };

  const handleNext = async () => {
    if (currentStep < TOTAL_STEPS) {
      const nextStep = currentStep + 1;
      if (nextStep === TOTAL_STEPS && !validateBasicInfo()) {
        dispatch(setCurrentStep(1));
        router.push(steps[0]);
        return;
      }
      dispatch(setCurrentStep(nextStep));
      router.push(steps[nextStep - 1]);
      setError("");
    }
  };

  const handleSave = async (status?: CampaignStatus) => {
    if (!validateBasicInfo() || !validateRequirements()) {
      return;
    }
    try {
      setIsSubmitting(true);
      setError("");
      setUpdateError("");
      // Update existing campaign
      if (!campaignId) {
        throw new Error("Campaign ID is required");
      }

      const updateData: any = {
        campaignId,
        name: basicInfo.name,
        description: basicInfo.description,
        deliverables: basicInfo.deliverables,
        startDate: new Date(basicInfo.startDate),
        endDate: new Date(basicInfo.endDate),
        visibility: basicInfo.visibility,
        price: basicInfo.price,
        interests: basicInfo.interests,
        files: basicInfo.files,
      };

      // Only include status if explicitly provided
      if (status !== undefined) {
        updateData.status = status;
      }

      await client.campaign.update.mutate(updateData);
      dispatch(resetCampaign());
      router.push("/app/brand/campaigns");
    } catch (error) {
      setUpdateError(
        error instanceof Error
          ? error.message
          : "Failed to update campaign",
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSavePreserveStatus = () => handleSave();
  const handleSaveAsDraft = () => handleSave(CampaignStatus.DRAFT);

  const handleBack = () => {
    if (currentStep > 1) {
      const prevStep = currentStep - 1;
      dispatch(setCurrentStep(prevStep));
      router.push(steps[prevStep - 1]);
    }
  };

  const handleCancel = () => {
    if (
      confirm(
        "Are you sure you want to cancel campaign creation? Your progress will be lost.",
      )
    ) {
      dispatch(resetCampaign());
      router.push("/app/brand/campaigns");
    }
  };

  useEffect(() => {
    async function fetchCampaign() {
      if (isEditMode && !basicInfo.name) {
        try {
          const data = await client.campaign.getById.query({
            campaignId: campaignId,
          });
          dispatch(
            setBasicInfo({
              name: data.name,
              description: data.description,
              price: data.price,
              startDate: data.startDate,
              endDate: data.endDate,
              visibility: data.visibility,
              interests: data.interests,
              deliverables: data.deliverables,
              files: data.files,
            }),
          );
        } catch (err) {
          console.error("Failed to load campaign info", err);
        } finally {
          setLoading(false);
        }
      } else {
        setLoading(false);
      }
    }
    fetchCampaign();
  }, [basicInfo.name, isEditMode, campaignId, dispatch]);

  if (loading) {
    return <LoadingSpinner />;
  }

  const footer = (
    <footer className="tw-pt-6 tw-px-6 tw-border-t tw-border-gray-700 tw-bg-aims-bg tw-mb-5">
      <div className="tw-mx-auto tw-max-w-[1200px] tw-flex tw-justify-between tw-items-center">
        <div className="tw-flex tw-gap-4">
          <Button
            variant="ghost"
            onClick={handleCancel}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          {currentStep > 1 && (
            <Button
              variant="ghost"
              onClick={handleBack}
              disabled={isSubmitting}
            >
              Back
            </Button>
          )}
        </div>
        {currentStep === TOTAL_STEPS ? (
          <div className="tw-flex tw-gap-3">
            <Button
              variant="outline"
              onClick={handleSaveAsDraft}
              disabled={isSubmitting}
              className="tw-flex tw-items-center tw-gap-2 tw-text-aims-text-primary"
            >
              {isSubmitting && <LoadingSpinner />}
              Save as Draft
            </Button>
            <Button
              onClick={handleSavePreserveStatus}
              disabled={isSubmitting}
              className="tw-flex tw-items-center tw-gap-2"
            >
              {isSubmitting && <LoadingSpinner />}
              Save
            </Button>
          </div>
        ) : (
          <Button
            onClick={handleNext}
            disabled={isSubmitting}
            className="tw-flex tw-items-center tw-gap-2"
          >
            Next
          </Button>
        )}
      </div>
    </footer>
  );

  return (
    <AppLayout
      contentClassName="tw-flex tw-flex-col tw-w-full tw-p-0"
      footer={footer}
    >
      {/* Progress Steps */}
      <div className="tw-bg-aims-dark-3 tw-py-6">
        <div className="tw-max-w-[800px] tw-mx-auto">
          <div className="tw-flex tw-justify-center tw-items-center">
            {Array.from({ length: TOTAL_STEPS }, (_, i) => i + 1).map(
              (step, index) => (
                <div key={step} className="tw-flex tw-flex-col tw-items-center">
                  <div className="tw-flex tw-items-center">
                    <button
                      className={`tw-w-12 tw-h-12 tw-rounded-full tw-flex tw-flex-col tw-items-center tw-justify-center tw-relative ${
                        step <= currentStep
                          ? "tw-bg-aims-primary"
                          : "tw-bg-gray-600"
                      }`}
                      disabled={step === TOTAL_STEPS ? true : false}
                      onClick={() => {
                        if (step === currentStep) return;
                        if (step === TOTAL_STEPS) {
                          handleNext();
                          return;
                        }
                        dispatch(setCurrentStep(step));
                        router.push(steps[step - 1]);
                      }}
                    >
                      <span className="tw-text-aims-text-primary tw-font-medium">
                        {step}
                      </span>
                    </button>
                    {index < TOTAL_STEPS - 1 && (
                      <div
                        className={`tw-w-32 tw-h-[2px] tw-mx-[-12px] ${
                          step < currentStep
                            ? "tw-bg-aims-primary"
                            : "tw-bg-gray-600"
                        }`}
                      ></div>
                    )}
                  </div>
                </div>
              ),
            )}
          </div>
        </div>
      </div>
      <div className="tw-flex-1 tw-flex tw-flex-col">
        <div className="tw-max-w-[1200px] tw-mx-auto tw-px-4 tw-py-8 tw-w-full">
          {/* Main Content */}
          {children}
          {error && (
            <div className="tw-mt-6 tw-p-4 tw-bg-red-500/10 tw-border tw-border-red-500 tw-rounded-lg tw-text-red-500">
              {error}
            </div>
          )}
          {updateError && (
            <div className="tw-mt-6 tw-p-4 tw-bg-red-500/10 tw-border tw-border-red-500 tw-rounded-lg tw-text-red-500">
              {updateError}
            </div>
          )}
        </div>
      </div>
    </AppLayout>
  );
}
