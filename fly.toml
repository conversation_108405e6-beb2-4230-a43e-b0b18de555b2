# fly.toml app configuration file generated for aims on 2025-04-15T14:21:50-06:00
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'aims'
primary_region = 'den'

[build]

[http_service]
  internal_port = 3000
  force_https = true
  auto_stop_machines = 'stop'
  auto_start_machines = true
  min_machines_running = 0
  processes = ['app']

[[vm]]
  memory = '1gb'
  cpu_kind = 'shared'
  cpus = 1
