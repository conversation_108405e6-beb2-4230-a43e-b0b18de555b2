"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import { client } from "@/lib/trpc/client";
import { TokensStorage } from "@/lib/utils/tokens-storage";
import { setAuthState } from "@/store/slices/auth";
import { ArrowPathIcon } from "@heroicons/react/24/outline";
import { useDispatch } from "react-redux";
import type { AuthResponse } from "@repo/server/src/models/user";

// Type guard to check if the response contains auth data
function hasAuthData(response: any): response is AuthResponse {
  return (
    response &&
    typeof response === "object" &&
    "profile" in response &&
    "tokens" in response &&
    typeof response.tokens === "object" &&
    "access" in response.tokens &&
    "refresh" in response.tokens
  );
}

export default function VerifyEmail() {
  const [status, setStatus] = useState<
    "verifying" | "logging-in" | "success" | "error" | "different-device"
  >("verifying");
  const [message, setMessage] = useState(
    "Please wait, we are verifying your account",
  );
  const router = useRouter();
  const searchParams = useSearchParams();
  const dispatch = useDispatch();

  useEffect(() => {
    const verifyEmail = async () => {
      try {
        const id = searchParams.get("id");
        const token = searchParams.get("token");

        if (!id || !token) {
          setStatus("error");
          setMessage("Invalid verification link");
          return;
        }

        // Check if this is a different device
        const isDifferentDevice = !localStorage.getItem("userType");

        let response;
        try {
          response = await client.auth.verifyEmailVerificationLink.mutate({
            id,
            token,
          });
        } catch (error) {
          setStatus("error");
          setMessage("Failed to verify email" + error);
          return;
        }

        // Handle auto-login
        if (hasAuthData(response)) {
          if (isDifferentDevice) {
            setStatus("different-device");
            setMessage(
              "Your email has been verified! Please return to your original browser tab to continue.",
            );
            return;
          }

          setStatus("logging-in");
          setMessage("Email verified! Logging you in...");

          // Store tokens and update auth state
          TokensStorage.setTokens(
            response.tokens.access,
            response.tokens.refresh,
          );
          dispatch(setAuthState(response));

          // Add a delay before showing success and redirecting
          setTimeout(() => {
            setStatus("success");
            setMessage("Successfully logged in! Redirecting...");

            // Use the redirectUrl from the response
            try {
              setTimeout(() => {
                if (response.redirectUrl) {
                  router.push(response.redirectUrl);
                } else if (response.profile.userType === "athlete") {
                  router.push("/onboarding/athlete");
                } else {
                  router.push("/onboarding/brand");
                }
              }, 1500);
            } catch (error) {
              setStatus("error");
              setMessage("Failed to redirect to app" + error);
            }
          }, 1500);
        }
      } catch (error) {
        setStatus("error");
        setMessage((error as Error).message);
      }
    };

    verifyEmail();
  }, [searchParams, router, dispatch]);

  return (
    <div className="tw-w-full">
      <div className="tw-text-center">
        <div className="tw-flex tw-flex-col tw-items-center tw-justify-center tw-gap-4">
          {(status === "verifying" || status === "logging-in") && (
            <ArrowPathIcon className="tw-h-8 tw-w-8 tw-text-blue-600 tw-animate-spin" />
          )}
          <h1 className="tw-text-2xl tw-font-bold tw-tracking-tight">
            {status === "verifying" && "Verifying Email"}
            {status === "logging-in" && "Logging In"}
            {status === "success" && "Success!"}
            {status === "error" && "Verification Error"}
            {status === "different-device" && "Email Verified"}
          </h1>
          <p
            className={`tw-mt-2 ${
              status === "error"
                ? "tw-text-red-600"
                : status === "success"
                  ? "tw-text-green-600"
                  : "tw-text-gray-600"
            }`}
          >
            {message}
          </p>
          {status === "error" && (
            <Button
              onClick={() => router.push("/auth/login")}
              variant="secondary"
              className="tw-text-aims-text-primary"
            >
              Back to Sign In
            </Button>
          )}
          {status === "different-device" && (
            <Button
              onClick={() => window.close()}
              variant="secondary"
              className="tw-text-aims-text-primary"
            >
              Close This Window
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
