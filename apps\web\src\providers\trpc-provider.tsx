"use client";

import { useState } from "react";
import { getBaseUrl, trpc } from "@/lib/trpc/client";
import { TokensStorage } from "@/lib/utils/tokens-storage";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { httpBatchLink } from "@trpc/client";

export default function TRPCProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const [queryClient] = useState(() => new QueryClient());
  const [trpcClient] = useState(() =>
    trpc.createClient({
      links: [
        httpBatchLink({
          url: `${getBaseUrl()}/trpc`,
          headers() {
            const tokens = TokensStorage.getTokens();
            return {
              Authorization: tokens?.accessToken
                ? `Bearer ${tokens.accessToken}`
                : "",
            };
          },
          fetch: async (input, options) => {
            // Add timeout for long-running requests like PDF generation
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 300000); // 5 minutes

            const optionsWithTimeout = {
              ...options,
              signal: controller.signal,
            };

            let response;
            try {
              response = await fetch(input, optionsWithTimeout);
              clearTimeout(timeoutId);
            } catch (error) {
              clearTimeout(timeoutId);
              if (error instanceof Error && error.name === 'AbortError') {
                throw new Error('Request timeout: The operation took too long to complete. Please try again.');
              }
              throw error;
            }

            // Handle unauthorized errors
            if (response.status === 401) {
              const tokens = TokensStorage.getTokens();
              if (!tokens?.refreshToken) {
                return response;
              }

              // Try to refresh the token
              const refreshResult = await fetch(
                `${getBaseUrl()}/auth.refreshToken`,
                {
                  method: "POST",
                  headers: {
                    "Content-Type": "application/json",
                  },
                  body: JSON.stringify({ refreshToken: tokens.refreshToken }),
                },
              ).then((r) => r.json());

              if (refreshResult.error) {
                TokensStorage.clearTokens();
                return response;
              }

              // Store new tokens
              const { access, refresh } = refreshResult.result.data.tokens;
              TokensStorage.setTokens(access, refresh);

              // Retry the original request with timeout
              const retryController = new AbortController();
              const retryTimeoutId = setTimeout(() => retryController.abort(), 300000); // 5 minutes

              try {
                const retryResponse = await fetch(input, {
                  ...optionsWithTimeout,
                  signal: retryController.signal,
                  headers: {
                    ...('headers' in optionsWithTimeout && optionsWithTimeout.headers ? optionsWithTimeout.headers as Record<string, string> : {}),
                    Authorization: `Bearer ${access}`,
                  },
                });
                clearTimeout(retryTimeoutId);
                return retryResponse;
              } catch (retryError) {
                clearTimeout(retryTimeoutId);
                if (retryError instanceof Error && retryError.name === 'AbortError') {
                  throw new Error('Request timeout: The operation took too long to complete. Please try again.');
                }
                throw retryError;
              }
            }

            return response;
          },
        }),
      ],
    }),
  );

  return (
    <trpc.Provider client={trpcClient} queryClient={queryClient}>
      <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
    </trpc.Provider>
  );
}
