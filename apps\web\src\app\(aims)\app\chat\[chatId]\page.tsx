"use client";

import { useEffect, useState } from "react";
import { useParams, useRouter } from "next/navigation";
import { ChatRoom } from "@/components/chat/ChatRoom";
import { trpc } from "@/lib/trpc/client";
import { useAuth } from "@/hooks/use-auth";
import { FullPageLoadingSpinner } from "@/components/ui/LoadingSpinner";
import { ArrowLeftIcon } from "@heroicons/react/24/solid";

export default function ChatRoomPage() {
  const params = useParams();
  const router = useRouter();
  const chatId = params.chatId as string;
  const { user } = useAuth();
  const [isDesktop, setIsDesktop] = useState(false);

  // Fetch chat details to get participant information
  const { data: chat, isLoading } = trpc.chat.getChatById.useQuery({
    chatId,
  });

  // Get the other participant (not the current user)
  const otherParticipant = chat?.participants.find(
    (p) => p.id !== user?.id,
  );

  // Handle responsive layout
  useEffect(() => {
    const checkScreenSize = () => {
      setIsDesktop(window.innerWidth >= 1024);
    };

    // Initial check
    checkScreenSize();

    // Add event listener for window resize
    window.addEventListener("resize", checkScreenSize);

    // Cleanup
    return () => window.removeEventListener("resize", checkScreenSize);
  }, []);

  // Redirect to main chat page on desktop
  useEffect(() => {
    if (isDesktop) {
      router.push(`/app/chat?id=${chatId}`);
    }
  }, [isDesktop, chatId, router]);

  if (isLoading) {
    return (
      <div className="tw-overflow-hidden tw-w-full tw-max-w-full tw-flex tw-items-center tw-justify-center tw-min-h-[400px]">
        <FullPageLoadingSpinner />
      </div>
    );
  }

  return (
    <div className="tw-overflow-hidden tw-w-full tw-max-w-full">
      <div className="tw-p-2 tw-pb-0">
        <button 
          onClick={() => router.push('/app/chat')}
          className="tw-flex tw-items-center tw-gap-2 tw-text-aims-text-secondary hover:tw-text-aims-text-primary"
        >
          <ArrowLeftIcon className="tw-w-4 tw-h-4 tw-text-aims-primary" />
          <span>Back to messages</span>
        </button>
      </div>
      <ChatRoom chatId={chatId} otherParticipant={otherParticipant} />
    </div>
  );
}
