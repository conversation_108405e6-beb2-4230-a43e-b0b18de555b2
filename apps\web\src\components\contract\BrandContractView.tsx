"use client";

import { useState, useEffect, useMemo } from "react";
import { format } from "date-fns";
import { trpc } from "@/lib/trpc/client";

import { ContractStatus, SerializedContract } from "@repo/server/src/types/contract";
import { isPdfGenerationAllowed, getPdfGenerationDisabledMessage, isContractEditingAllowed, getContractEditingDisabledMessage } from "@repo/server/src/utils/contractPdf";
import { useSocket } from "@/hooks/use-socket";

import { Button } from "../ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "../ui/card";
import { Badge } from "../ui/badge";
import { useToast } from "../ui/toast/use-toast";
import { DeliverablesTable } from "../ui/DeliverablesTable";
import { BrandContractSigningModal } from "./BrandContractSigningModal";
import { PaymentStatusBadge, PaymentActionIndicator } from "./PaymentStatusBadge";
import { ContractEditModal } from "./ContractEditModal";
import { LoadingSpinner } from "../ui/LoadingSpinner";
import { ClipboardDocumentListIcon, ExclamationTriangleIcon, CheckIcon, ClockIcon } from "@heroicons/react/24/solid";

interface BrandContractViewProps {
  contract: SerializedContract;
  onContractUpdated?: () => void;
}

export function BrandContractView({
  contract,
  onContractUpdated,
}: BrandContractViewProps) {
  const { toast } = useToast();
  const { socket } = useSocket();
  const [isGeneratingPdf, setIsGeneratingPdf] = useState(false);
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);
  const [showSigningModal, setShowSigningModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);

  const generatePdfMutation = trpc.contract.generatePdf.useMutation();
  const updateStatusMutation = trpc.contract.updateStatus.useMutation();
  const clearPaymentIntentMutation = trpc.contract.clearPaymentIntent.useMutation();
  const syncPaymentStatusMutation = trpc.contract.syncPaymentStatus.useMutation();
  const fulfillContractMutation = trpc.contract.fulfillContract.useMutation();

  // Listen for real-time contract status updates
  useEffect(() => {
    if (!socket) return;

    const handleContractStatusUpdate = (data: {
      contractId: string;
      status: ContractStatus;
      contract: SerializedContract;
    }) => {
      // Only handle updates for this specific contract
      if (data.contractId === contract.id) {
        console.log('Received contract status update:', data);
        // Trigger contract data refresh
        onContractUpdated?.();

        // Show toast notification for important status changes
        if (data.status === ContractStatus.AWAITING_DELIVERABLES) {
          toast({
            title: "Payment Completed",
            description: "Contract payment has been processed successfully. The athlete can now start working on deliverables.",
            variant: "default",
          });
        }
      }
    };

    socket.on('contractStatusUpdated', handleContractStatusUpdate);

    return () => {
      socket.off('contractStatusUpdated', handleContractStatusUpdate);
    };
  }, [socket, contract.id, onContractUpdated, toast]);

  // Handle URL hash scrolling
  useEffect(() => {
    const handleHashChange = () => {
      const hash = window.location.hash.substring(1);
      if (hash) {
        const element = document.getElementById(hash);
        if (element) {
          element.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      }
    };

    // Handle initial hash on component mount
    handleHashChange();

    // Listen for hash changes
    window.addEventListener('hashchange', handleHashChange);

    return () => {
      window.removeEventListener('hashchange', handleHashChange);
    };
  }, []);

  // Fetch campaign deliverables to get missing fields (like daysToComplete)
  const deliverableIds = contract.terms.deliverables.map(d => d.deliverableId);
  const { data: campaignDeliverables, isLoading: deliverablesLoading } = trpc.deliverable.getByIds.useQuery(
    { deliverableIds },
    { enabled: deliverableIds.length > 0 }
  );

  // Fetch deliverable submissions for this campaign to check approval status
  const { data: submissions, isLoading: submissionsLoading } = trpc.deliverableSubmission.getCampaignSubmissions.useQuery(
    { campaignId: contract.campaignId },
    { enabled: contract.status === ContractStatus.AWAITING_BRAND_APPROVAL }
  );

  // Check if all deliverables are approved
  const areAllDeliverablesApproved = useMemo(() => {
    if (!submissions || contract.status !== ContractStatus.AWAITING_BRAND_APPROVAL) {
      return false;
    }

    // Get submissions for this specific athlete (contract)
    const athleteSubmissions = submissions.filter(sub => sub.athleteId === contract.athleteId);

    // Check if we have submissions for all deliverables and they're all approved
    const deliverableIds = contract.terms.deliverables.map(d => d.deliverableId);

    return deliverableIds.every(deliverableId => {
      const submission = athleteSubmissions.find(sub => sub.deliverableId === deliverableId);
      return submission && submission.status === 'APPROVED';
    });
  }, [submissions, contract.status, contract.athleteId, contract.terms.deliverables]);

  // Transform contract deliverables to BaseDeliverable format for DeliverablesTable
  // This preserves the athlete-specific pricing (compensation) from the contract
  const contractDeliverablesForTable = contract.terms.deliverables.map(contractDeliverable => {
    // Find the corresponding campaign deliverable to get missing fields
    const campaignDeliverable = campaignDeliverables?.find(cd => cd.id === contractDeliverable.deliverableId);

    return {
      id: contractDeliverable.deliverableId,
      campaignId: contract.campaignId,
      name: contractDeliverable.name,
      description: contractDeliverable.description,
      daysToComplete: campaignDeliverable?.daysToComplete || 0,
      minimumPayment: contractDeliverable.compensation, // Use athlete-specific pricing from contract
      type: contractDeliverable.type as any, // Type assertion needed for compatibility
      createdAt: contract.createdAt,
      updatedAt: contract.updatedAt,
      // Include type-specific fields if available
      location: campaignDeliverable?.location,
      date: campaignDeliverable?.date,
      time: campaignDeliverable?.time,
      content: campaignDeliverable?.content,
      productName: campaignDeliverable?.productName,
      productPrice: campaignDeliverable?.productPrice,
    };
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case "DRAFT":
        return "tw-bg-gray-100 tw-text-gray-800";
      case "PENDING_BRAND_REVIEW":
        return "tw-bg-yellow-100 tw-text-yellow-800";
      case "PENDING_BRAND_APPROVAL":
        return "tw-bg-orange-100 tw-text-orange-800";
      case "PENDING_ATHLETE_SIGNATURE":
        return "tw-bg-blue-100 tw-text-blue-800";
      case "ATHLETE_SIGNED":
        return "tw-bg-green-100 tw-text-green-800";
      case "PENDING_BRAND_SIGNATURE":
        return "tw-bg-purple-100 tw-text-purple-800";
      case "BRAND_SIGNED":
        return "tw-bg-green-100 tw-text-green-800";
      case "PENDING_PAYMENT":
        return "tw-bg-orange-100 tw-text-orange-800";
      case "PAID":
        return "tw-bg-blue-100 tw-text-blue-800";
      case "AWAITING_DELIVERABLES":
        return "tw-bg-purple-100 tw-text-purple-800";
      case "FULFILLED":
        return "tw-bg-emerald-100 tw-text-emerald-800";
      case "SIGNED":
        return "tw-bg-green-100 tw-text-green-800";
      case "CANCELLED":
        return "tw-bg-red-100 tw-text-red-800";
      case "EXPIRED":
        return "tw-bg-gray-100 tw-text-gray-600";
      default:
        return "tw-bg-gray-100 tw-text-gray-800";
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const handleGeneratePdf = async () => {
    setIsGeneratingPdf(true);
    try {
      const result = await generatePdfMutation.mutateAsync({
        contractId: contract.id,
        regenerate: false,
      });
      
      // Open PDF in new tab
      window.open(result.pdfUrl, '_blank');
      
      toast({
        title: "PDF Generated",
        description: "Contract PDF has been generated successfully.",
        variant: "success",
      });
    } catch (error) {
      console.error("Failed to generate PDF:", error);
      toast({
        title: "Error",
        description: "Failed to generate PDF. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingPdf(false);
    }
  };

  const handleApproveContract = async () => {
    setIsUpdatingStatus(true);
    try {
      await updateStatusMutation.mutateAsync({
        contractId: contract.id,
        status: ContractStatus.PENDING_ATHLETE_SIGNATURE,
      });
      
      toast({
        title: "Contract Approved",
        description: "Contract has been approved and sent to the athlete for signature.",
        variant: "success",
      });
      
      onContractUpdated?.();
    } catch (error) {
      console.error("Failed to approve contract:", error);
      toast({
        title: "Error",
        description: "Failed to approve contract. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsUpdatingStatus(false);
    }
  };

  const handleReviewComplete = async () => {
    setIsUpdatingStatus(true);
    try {
      await updateStatusMutation.mutateAsync({
        contractId: contract.id,
        status: ContractStatus.PENDING_BRAND_APPROVAL,
      });
      
      toast({
        title: "Review Complete",
        description: "Contract review has been marked as complete.",
        variant: "success",
      });
      
      onContractUpdated?.();
    } catch (error) {
      console.error("Failed to complete review:", error);
      toast({
        title: "Error",
        description: "Failed to complete review. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsUpdatingStatus(false);
    }
  };

  const handleSigningComplete = () => {
    setShowSigningModal(false);
    onContractUpdated?.();
    toast({
      title: "Contract Signed",
      description: "You have successfully signed the contract!",
      variant: "success",
    });
  };

  const isExpired = contract.expiresAt && new Date(contract.expiresAt) < new Date();
  const oneWeekFromNow = new Date();
  oneWeekFromNow.setDate(oneWeekFromNow.getDate() + 7);
  const expiresWithinWeek = contract.expiresAt && new Date(contract.expiresAt) < oneWeekFromNow;
  const canReview = contract.status === ContractStatus.PENDING_BRAND_REVIEW;
  const canApprove = contract.status === ContractStatus.PENDING_BRAND_APPROVAL;
  const canEdit = isContractEditingAllowed(contract.status);
  const canSign = contract.status === ContractStatus.PENDING_BRAND_SIGNATURE;

  return (
    <div className="tw-space-y-6">
      {/* Contract Header */}
      <Card id="header">
        <CardHeader>
          <div className="tw-flex tw-justify-between tw-items-start">
            <div>
              <CardTitle className="tw-text-xl tw-font-bold">
                {contract.title}
              </CardTitle>
              <p className="tw-text-sm tw-text-aims-dark-6 tw-mt-1">
                Contract #{contract.contractNumber} • Version {contract.version}
              </p>
            </div>
            <div className="tw-flex tw-gap-2">
              <Badge className={getStatusColor(contract.status)}>
                {contract.status.replace(/_/g, ' ')}
              </Badge>
              <PaymentStatusBadge
                contractStatus={contract.status}
                paymentStatus={contract.paymentStatus}
              />
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Status-specific alerts */}
      {canReview && (
        <Card className="tw-bg-yellow-50 tw-border-yellow-200">
          <CardContent className="tw-py-4">
            <div className="tw-flex tw-items-center tw-gap-3">
              <div className="tw-w-8 tw-h-8 tw-min-w-8 tw-min-h-8 tw-max-w-8 tw-max-h-8 tw-bg-yellow-600 tw-text-white tw-rounded-full tw-flex tw-items-center tw-justify-center tw-flex-shrink-0">
                <ExclamationTriangleIcon className="tw-w-5 tw-h-5" />
              </div>
              <div>
                <h3 className="tw-font-semibold tw-text-yellow-900">Review Required</h3>
                <p className="tw-text-sm tw-text-yellow-700">
                  This contract needs your review before it can be approved and sent to the athlete.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {canApprove && (
        <Card className="tw-bg-orange-50 tw-border-orange-200">
          <CardContent className="tw-py-4">
            <div className="tw-flex tw-items-center tw-gap-3">
              <div className="tw-w-8 tw-h-8 tw-min-w-8 tw-min-h-8 tw-max-w-8 tw-max-h-8 tw-bg-orange-600 tw-text-white tw-rounded-full tw-flex tw-items-center tw-justify-center tw-flex-shrink-0">
                <CheckIcon className="tw-w-5 tw-h-5" />
              </div>
              <div>
                <h3 className="tw-font-semibold tw-text-orange-900">Ready for Approval</h3>
                <p className="tw-text-sm tw-text-orange-700">
                  This contract has been reviewed and is ready to be approved and sent to the athlete.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {contract.status === ContractStatus.PENDING_ATHLETE_SIGNATURE && (
        <Card className="tw-bg-blue-50 tw-border-blue-200">
          <CardContent className="tw-py-4">
            <div className="tw-flex tw-items-center tw-gap-3">
              <div className="tw-w-8 tw-h-8 tw-min-w-8 tw-min-h-8 tw-max-w-8 tw-max-h-8 tw-bg-blue-600 tw-text-white tw-rounded-full tw-flex tw-items-center tw-justify-center tw-flex-shrink-0">
                <ClockIcon className="tw-w-5 tw-h-5" />
              </div>
              <div>
                <h3 className="tw-font-semibold tw-text-blue-900">Waiting for Athlete Signature</h3>
                <p className="tw-text-sm tw-text-blue-700">
                  This contract has been sent to the athlete and is waiting for their signature.
                  {contract.sentToAthleteAt && ` Sent on ${format(new Date(contract.sentToAthleteAt), 'MMM dd, yyyy')}.`}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {contract.status === ContractStatus.ATHLETE_SIGNED && (
        <Card className="tw-bg-green-50 tw-border-green-200">
          <CardContent className="tw-py-4">
            <div className="tw-flex tw-items-center tw-gap-3">
              <div className="tw-w-8 tw-h-8 tw-min-w-8 tw-min-h-8 tw-max-w-8 tw-max-h-8 tw-bg-green-600 tw-text-white tw-rounded-full tw-flex tw-items-center tw-justify-center tw-flex-shrink-0">
                <CheckIcon className="tw-w-5 tw-h-5" />
              </div>
              <div>
                <h3 className="tw-font-semibold tw-text-green-900">Athlete Signed - Preparing for Brand Signature</h3>
                <p className="tw-text-sm tw-text-green-700">
                  The athlete has successfully signed this contract.
                  {contract.athleteSignedAt && ` Signed on ${format(new Date(contract.athleteSignedAt), 'MMM dd, yyyy')}.`}
                  {" "}The contract is being automatically prepared for your signature.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {canSign && (
        <Card className="tw-bg-blue-50 tw-border-blue-200">
          <CardContent className="tw-py-4">
            <div className="tw-flex tw-items-center tw-gap-3">
              <div className="tw-w-8 tw-h-8 tw-min-w-8 tw-min-h-8 tw-max-w-8 tw-max-h-8 tw-bg-blue-600 tw-text-white tw-rounded-full tw-flex tw-items-center tw-justify-center tw-flex-shrink-0">
                <CheckIcon className="tw-w-5 tw-h-5" />
              </div>
              <div>
                <h3 className="tw-font-semibold tw-text-blue-900">Ready for Brand Signature</h3>
                <p className="tw-text-sm tw-text-blue-700">
                  The athlete has signed the contract and it&apos;s now ready for your signature to complete the agreement.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {contract.status === ContractStatus.PENDING_PAYMENT && (
        <Card className="tw-bg-green-50 tw-border-green-200">
          <CardContent className="tw-py-4">
            <div className="tw-flex tw-items-center tw-gap-3">
              <div className="tw-w-8 tw-h-8 tw-min-w-8 tw-min-h-8 tw-max-w-8 tw-max-h-8 tw-bg-green-600 tw-text-white tw-rounded-full tw-flex tw-items-center tw-justify-center tw-flex-shrink-0">
                <CheckIcon className="tw-w-5 tw-h-5" />
              </div>
              <div>
                <h3 className="tw-font-semibold tw-text-green-900">Contract Awaiting Payment</h3>
                <p className="tw-text-sm tw-text-green-700">
                  This contract has been successfully signed by both parties and is now awaiting payment.
                  {contract.brandSignedAt && ` Brand signed on ${format(new Date(contract.brandSignedAt), 'MMM dd, yyyy')}.`}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {contract.status === ContractStatus.AWAITING_DELIVERABLES && (
        <Card className="tw-bg-purple-50 tw-border-purple-200">
          <CardContent className="tw-py-4">
            <div className="tw-flex tw-items-center tw-gap-3">
              <div className="tw-w-8 tw-h-8 tw-min-w-8 tw-min-h-8 tw-max-w-8 tw-max-h-8 tw-bg-purple-600 tw-text-white tw-rounded-full tw-flex tw-items-center tw-justify-center tw-flex-shrink-0">
                <ClipboardDocumentListIcon className="tw-w-5 tw-h-5" />
              </div>
              <div>
                <h3 className="tw-font-semibold tw-text-purple-900">Athlete Working on Deliverables</h3>
                <p className="tw-text-sm tw-text-purple-700">
                  Payment has been completed and the athlete has been notified to start working on deliverables.
                  {contract.paymentCompletedAt && ` Payment completed on ${format(new Date(contract.paymentCompletedAt), 'MMM dd, yyyy')}.`}
                  {" "}You will be notified when deliverables are submitted for review.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {contract.status === ContractStatus.AWAITING_BRAND_APPROVAL && (
        <Card className="tw-bg-orange-50 tw-border-orange-200">
          <CardContent className="tw-py-4">
            <div className="tw-flex tw-items-center tw-gap-3">
              <div className="tw-w-8 tw-h-8 tw-min-w-8 tw-min-h-8 tw-max-w-8 tw-max-h-8 tw-bg-orange-600 tw-text-white tw-rounded-full tw-flex tw-items-center tw-justify-center tw-flex-shrink-0">
                <ClipboardDocumentListIcon className="tw-w-5 tw-h-5" />
              </div>
              <div className="tw-flex-1">
                <div className="tw-flex tw-justify-between tw-items-center">
                  <h3 className="tw-font-semibold tw-text-orange-900">All Deliverables Submitted - Awaiting Your Review</h3>
                  {areAllDeliverablesApproved && (
                    <Badge className="tw-bg-green-100 tw-text-green-800 tw-flex tw-items-center tw-gap-1">
                      <CheckIcon className="tw-w-3 tw-h-3" /> All Approved
                    </Badge>
                  )}
                </div>
                <p className="tw-text-sm tw-text-orange-700">
                  The athlete has submitted all deliverables for this contract. Please review each submission below and approve or provide feedback.
                  Once all deliverables are approved, you can mark the contract as fulfilled.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Expiration Warning */}
      {expiresWithinWeek && contract.status === ContractStatus.PENDING_ATHLETE_SIGNATURE && (
        <Card className={`${isExpired ? 'tw-bg-red-50 tw-border-red-200' : 'tw-bg-yellow-50 tw-border-yellow-200'}`}>
          <CardContent className="tw-py-4">
            <div className="tw-flex tw-items-center tw-gap-3">
              <div className={`tw-w-8 tw-h-8 ${isExpired ? 'tw-bg-red-600' : 'tw-bg-yellow-600'} tw-text-white tw-rounded-full tw-flex tw-items-center tw-justify-center tw-text-sm tw-font-medium`}>
                ⚠
              </div>
              <div>
                <h3 className={`tw-font-semibold ${isExpired ? 'tw-text-red-900' : 'tw-text-yellow-900'}`}>
                  {isExpired ? 'Contract Expired' : 'Contract Expiring Soon'}
                </h3>
                <p className={`tw-text-sm ${isExpired ? 'tw-text-red-700' : 'tw-text-yellow-700'}`}>
                  {isExpired
                    ? `This contract expired on ${format(new Date(contract.expiresAt!), 'MMM dd, yyyy')}.`
                    : `This contract will expire on ${format(new Date(contract.expiresAt!), 'MMM dd, yyyy')}.`
                  }
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Contract Overview */}
      <Card id="overview">
        <CardHeader>
          <CardTitle>Contract Overview</CardTitle>
        </CardHeader>
        <CardContent className="tw-space-y-4">
          <div className="tw-grid tw-grid-cols-1 md:tw-grid-cols-2 tw-gap-4">
            <div>
              <h4 className="tw-font-medium tw-text-xl">Total Compensation</h4>
              <p className="tw-text-md tw-font-semibold tw-text-green-600">
                {formatCurrency(contract.terms.totalCompensation)}
              </p>
            </div>
            <div>
              <h4 className="tw-font-medium tw-text-xl">Campaign Duration</h4>
              <p className="tw-text-md tw-text-aims-dark-6">
                {format(new Date(contract.terms.campaignDuration.startDate), 'MMM dd, yyyy')} - {format(new Date(contract.terms.campaignDuration.endDate), 'MMM dd, yyyy')}
              </p>
            </div>
            <div>
              <h4 className="tw-font-medium tw-text-xl">Created</h4>
              <p className="tw-text-md tw-text-aims-dark-6">
                {format(new Date(contract.createdAt), 'MMM dd, yyyy')}
              </p>
            </div>
            <div>
              <h4 className="tw-font-medium tw-text-xl">Last Updated</h4>
              <p className="tw-text-md tw-text-aims-dark-6">
                {format(new Date(contract.updatedAt), 'MMM dd, yyyy')}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Deliverables */}
      <Card id="deliverables">
        <CardHeader>
          <CardTitle>Deliverables ({contract.terms.deliverables.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {deliverablesLoading ? (
            <div className="tw-flex tw-justify-center tw-py-8">
              <div className="tw-text-sm tw-text-gray-500">Loading deliverables...</div>
            </div>
          ) : contractDeliverablesForTable.length > 0 ? (
            <div className="tw-overflow-hidden tw-rounded-lg tw-ring-1 tw-ring-aims-dark-3">
              <DeliverablesTable deliverables={contractDeliverablesForTable} />
            </div>
          ) : (
            <div className="tw-text-center tw-py-8">
              <div className="tw-text-sm tw-text-gray-500">No deliverables found</div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Payment Schedule */}
      <Card id="payment-schedule">
        <CardHeader>
          <CardTitle>Payment Schedule</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="tw-space-y-3">
            {contract.terms.paymentSchedule.map((payment, index) => (
              <div key={index} className="tw-flex tw-justify-between tw-items-center tw-p-3 tw-border tw-rounded-lg">
                <div>
                  <h4 className="tw-font-medium">{payment.description}</h4>
                  {payment.milestone && (
                    <p className="tw-text-sm tw-text-aims-dark-6">{payment.milestone}</p>
                  )}
                </div>
                <div className="tw-text-right">
                  <p className="tw-font-semibold tw-text-green-600">
                    {formatCurrency(payment.amount)}
                  </p>
                  <p className="tw-text-sm tw-text-aims-dark-6">
                    Due: {format(new Date(payment.dueDate), 'MMM dd, yyyy')}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Additional Terms */}
      {(contract.terms.additionalTerms?.length || contract.terms.cancellationPolicy || contract.terms.intellectualPropertyRights || contract.terms.confidentialityClause) && (
        <Card id="terms">
          <CardHeader>
            <CardTitle>Additional Terms & Conditions</CardTitle>
          </CardHeader>
          <CardContent className="tw-space-y-4">
            {contract.terms.additionalTerms && contract.terms.additionalTerms.length > 0 && (
              <div>
                <h4 className="tw-font-medium tw-mb-2">Additional Terms</h4>
                <ul className="tw-text-sm tw-text-aims-dark-6 tw-list-disc tw-list-inside tw-space-y-1">
                  {contract.terms.additionalTerms.map((term, index) => (
                    <li key={index}>{term}</li>
                  ))}
                </ul>
              </div>
            )}

            {contract.terms.cancellationPolicy && (
              <div>
                <h4 className="tw-font-medium tw-mb-2">Cancellation Policy</h4>
                <p className="tw-text-sm tw-text-aims-dark-6">{contract.terms.cancellationPolicy}</p>
              </div>
            )}

            {contract.terms.intellectualPropertyRights && (
              <div>
                <h4 className="tw-font-medium tw-mb-2">Intellectual Property Rights</h4>
                <p className="tw-text-sm tw-text-aims-dark-6">{contract.terms.intellectualPropertyRights}</p>
              </div>
            )}

            {contract.terms.confidentialityClause && (
              <div>
                <h4 className="tw-font-medium tw-mb-2">Confidentiality</h4>
                <p className="tw-text-sm tw-text-aims-dark-6">{contract.terms.confidentialityClause}</p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Contract Participants */}
      <Card id="participants">
        <CardHeader>
          <CardTitle>Contract Participants</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="tw-space-y-4">
            {contract.participants.map((participant, index) => {
              const profile = participant.userType === "brand"
                ? contract.participantProfiles?.brand
                : contract.participantProfiles?.athlete;

              const displayName = profile?.name || `${participant.userType === "brand" ? "Brand" : "Athlete"} User`;
              const companyName = participant.userType === "brand" && contract.participantProfiles?.brand?.companyName
                ? contract.participantProfiles.brand.companyName
                : null;

              return (
                <div key={index} className="tw-flex tw-justify-between tw-items-center tw-p-3 tw-border tw-rounded-lg">
                  <div>
                    <h4 className="tw-font-medium">
                      {displayName}
                    </h4>
                    {companyName && (
                      <p className="tw-text-sm tw-text-aims-dark-6">{companyName}</p>
                    )}
                  </div>
                  <div className="tw-text-right">
                    {participant.signedAt ? (
                      <div className="tw-flex tw-items-center tw-text-green-600">
                        <span className="tw-text-sm tw-font-medium">
                          ✓ Signed on {format(new Date(participant.signedAt), 'MMM dd, yyyy')}
                        </span>
                      </div>
                    ) : (
                      <Badge variant="outline" className="tw-text-aims-dark-6">
                        Pending Signature {participant.userType === "brand" ? "& Payment" : ""}
                      </Badge>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Action Buttons */}

      <Card id="review">
        <CardContent className="tw-pt-6">
          <div className="tw-flex tw-flex-wrap tw-justify-between tw-gap-3">
            <div className="tw-flex tw-flex-wrap tw-gap-3">
            <div className="tw-flex tw-flex-col tw-gap-2">
              <Button
                variant="outline"
                onClick={handleGeneratePdf}
                disabled={isGeneratingPdf || !isPdfGenerationAllowed(contract.status)}
                title={!isPdfGenerationAllowed(contract.status) ? getPdfGenerationDisabledMessage(contract.status) : undefined}
              >
                {isGeneratingPdf ?
                  <div className="tw-flex tw-items-center tw-gap-2">
                    <LoadingSpinner />
                    Generating...
                  </div>
                  : "Download PDF"}
              </Button>

              {!isPdfGenerationAllowed(contract.status) && (
                <p className="tw-text-sm tw-text-gray-500 tw-max-w-xs">
                  {getPdfGenerationDisabledMessage(contract.status)}
                </p>
              )}
            </div>

            {contract.pdfUrl && (
              <Button
                variant="outline"
                onClick={() => window.open(contract.pdfUrl, '_blank')}
              >
                View PDF
              </Button>
            )}
            </div>

            <div className="tw-flex tw-flex-wrap tw-gap-3">

                        {/* {(contract.status === ContractStatus.PENDING_BRAND_REVIEW || contract.status === ContractStatus.PENDING_BRAND_APPROVAL) && (
              <Button
                variant="outline"
                onClick={() => router.push(`/app/brand/contracts/${contract.id}/cancel`)}
                className="tw-text-red-600 tw-border-red-200 hover:tw-bg-red-50"
              >
                Cancel Contract
              </Button>
            )} */}

            <div className="tw-flex tw-flex-col tw-gap-2">
              <Button
                variant="outline"
                onClick={() => setShowEditModal(true)}
                disabled={!canEdit}
                title={!canEdit ? getContractEditingDisabledMessage(contract.status) : undefined}
              >
                Edit Contract
              </Button>

              {!canEdit && (
                <p className="tw-text-sm tw-text-gray-500 tw-max-w-xs">
                  {getContractEditingDisabledMessage(contract.status)}
                </p>
              )}
            </div>


            {canReview && (
              <Button
                onClick={handleReviewComplete}
                disabled={isUpdatingStatus}
                className="tw-text-black"
              >
                {isUpdatingStatus ? "Updating..." : "Complete Review"}
              </Button>
            )}

            {canApprove && (
              <Button
                onClick={handleApproveContract}
                disabled={isUpdatingStatus}
                className="tw-bg-green-600 hover:tw-bg-green-700"
              >
                {isUpdatingStatus ? "Approving..." : "Approve & Send to Athlete"}
              </Button>
            )}




            {canSign && (
              <Button
                onClick={() => setShowSigningModal(true)}
                disabled={isUpdatingStatus}
                className="tw-text-black"
              >
                Sign and Pay
              </Button>
            )}

            {/* Payment Button - Now handled by the enhanced signing modal */}
            {contract.status === ContractStatus.PENDING_PAYMENT && (
              <>
                <Button
                  onClick={() => setShowSigningModal(true)}
                  disabled={isUpdatingStatus}
                  className="tw-bg-green-600 hover:tw-bg-green-700"
                >
                  {contract.paymentStatus === "FAILED" ? "Retry Payment" : "Complete Payment"}
                </Button>

                {/* Sync Payment Status Button */}
                {process.env.NODE_ENV === "development" && (
                  <>  
                <Button
                  variant="outline"
                  onClick={async () => {
                    try {
                      const result = await syncPaymentStatusMutation.mutateAsync({
                        contractId: contract.id,
                      });
                      toast({
                        title: "Payment Status Synced",
                        description: `Payment status updated to: ${result.paymentStatus}`,
                        variant: "default",
                      });
                      onContractUpdated?.();
                    } catch (error) {
                      console.error("Failed to sync payment status:", error);
                      toast({
                        title: "Sync Failed",
                        description: "Failed to sync payment status. Please try again.",
                        variant: "destructive",
                      });
                    }
                  }}
                  className="tw-text-blue-600 tw-border-blue-200"
                >
                  Sync Payment Status
                </Button>

                <Button
                  variant="outline"
                  onClick={async () => {
                    try {
                      await clearPaymentIntentMutation.mutateAsync({
                        contractId: contract.id,
                      });
                      toast({
                        title: "Debug",
                        description: "Payment intent cleared. Try payment again.",
                        variant: "default",
                      });
                      onContractUpdated?.();
                    } catch (error) {
                      console.error("Failed to clear payment intent:", error);
                    }
                  }}
                  className="tw-text-red-600 tw-border-red-200"
                >
                  Clear Payment Intent (Debug)
                </Button>
                </>
                )}
              </>
            )}

            {/* Contract Action Buttons */}
            {contract.status === ContractStatus.AWAITING_DELIVERABLES && (
              <Button
                onClick={async () => {
                  setIsUpdatingStatus(true);
                  try {
                    await fulfillContractMutation.mutateAsync({
                      contractId: contract.id,
                    });

                    toast({
                      title: "Contract Fulfilled",
                      description: "Contract has been marked as fulfilled.",
                      variant: "success",
                    });

                    onContractUpdated?.();
                  } catch (error) {
                    console.error("Failed to fulfill contract:", error);
                    toast({
                      title: "Error",
                      description: "Failed to fulfill contract. Please try again.",
                      variant: "destructive",
                    });
                  } finally {
                    setIsUpdatingStatus(false);
                  }
                }}
                disabled={isUpdatingStatus}
                className="tw-bg-emerald-600 hover:tw-bg-emerald-700"
              >
                {isUpdatingStatus ? "Updating..." : "Mark as Fulfilled"}
              </Button>
            )}

            {contract.status === ContractStatus.AWAITING_BRAND_APPROVAL && (
              <div className="tw-flex tw-gap-3">
                <Button
                  onClick={() => {
                    // Scroll to deliverables section for review
                    const deliverablesSection = document.getElementById('deliverables');
                    if (deliverablesSection) {
                      deliverablesSection.scrollIntoView({ behavior: 'smooth' });
                    }
                  }}
                  className="tw-text-black"
                >
                  Review Deliverables
                </Button>

                {areAllDeliverablesApproved && (
                  <Button
                    onClick={async () => {
                      setIsUpdatingStatus(true);
                      try {
                        await fulfillContractMutation.mutateAsync({
                          contractId: contract.id,
                        });

                        toast({
                          title: "Contract Fulfilled",
                          description: "Contract has been marked as fulfilled.",
                          variant: "success",
                        });

                        onContractUpdated?.();
                      } catch (error) {
                        console.error("Failed to fulfill contract:", error);
                        toast({
                          title: "Error",
                          description: "Failed to fulfill contract. Please try again.",
                          variant: "destructive",
                        });
                      } finally {
                        setIsUpdatingStatus(false);
                      }
                    }}
                    disabled={isUpdatingStatus}
                    className="tw-bg-emerald-600 hover:tw-bg-emerald-700"
                  >
                    {isUpdatingStatus ? "Updating..." : "Mark as Fulfilled"}
                  </Button>
                )}
{/* 
                {!areAllDeliverablesApproved && !submissionsLoading && (
                  <div className="tw-flex tw-items-center tw-gap-2 tw-text-sm tw-text-gray-600">
                    <ClockIcon className="tw-w-4 tw-h-4" />
                    Please review and approve all deliverables first
                  </div>
                )} */}
              </div>
            )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Payment Action Indicator */}
      <PaymentActionIndicator
        contractStatus={contract.status}
        paymentStatus={contract.paymentStatus}
        userType="brand"
        className="tw-mb-6"
      />

      {/* Brand Contract Signing Modal */}
      <BrandContractSigningModal
        isOpen={showSigningModal}
        onClose={() => setShowSigningModal(false)}
        contract={contract}
        onSigningComplete={handleSigningComplete}
      />



      {/* Contract Edit Modal */}
      <ContractEditModal
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        contract={contract}
        onSuccess={() => {
          setShowEditModal(false);
          onContractUpdated?.();
        }}
      />
    </div>
  );
}
