import { Request, Response } from "express";

import { stripe } from "../lib/stripe";
import BrandModel from "../models/brand";
import { processPaymentSuccess, processPaymentFailure } from "../services/paymentService";

export const handleStripeWebhook = async (req: Request, res: Response) => {
  const sig = req.headers["stripe-signature"];
  let event;

  try {
    event = stripe.webhooks.constructEvent(
      req.body,
      sig as string,
      process.env.STRIPE_WEBHOOK_SECRET!,
    );
    console.log("Webhook event received:", event.type);
  } catch (err: any) {
    console.error("Webhook signature verification failed.", err);
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  switch (event.type) {
    case "checkout.session.completed": {
      console.log("Processing checkout.session.completed");
      const session = event.data.object as any;
      const brandId = session.metadata.brandId;
      const subscriptionId = session.subscription;

      console.log("Updating brand with ID:", brandId);
      console.log("Subscription ID:", subscriptionId);

      try {
        const updatedBrand = await BrandModel.findByIdAndUpdate(
          brandId,
          {
            subscriptionActive: true,
            stripeSubscriptionId: subscriptionId,
          },
          { new: true },
        );

        console.log("Brand update result:", updatedBrand);
      } catch (error) {
        console.error("Error updating brand:", error);
      }
      break;
    }
    case "customer.subscription.updated": {
      console.log("Processing customer.subscription.updated");
      const subscription = event.data.object as any;
      const isActive = subscription.status === "active";
      console.log("Subscription status:", subscription.status);
      console.log("Is active:", isActive);

      try {
        const updatedBrand = await BrandModel.findOneAndUpdate(
          { stripeSubscriptionId: subscription.id },
          { subscriptionActive: isActive },
          { new: true },
        );
        console.log("Brand update result:", updatedBrand);
      } catch (error) {
        console.error("Error updating brand:", error);
      }
      break;
    }
    case "customer.subscription.deleted": {
      console.log("Processing customer.subscription.deleted");
      const subscription = event.data.object as any;

      try {
        const updatedBrand = await BrandModel.findOneAndUpdate(
          { stripeSubscriptionId: subscription.id },
          { subscriptionActive: false },
          { new: true },
        );
        console.log("Brand update result:", updatedBrand);
      } catch (error) {
        console.error("Error updating brand:", error);
      }
      break;
    }
    case "payment_intent.succeeded": {
      console.log("Processing payment_intent.succeeded");
      const paymentIntent = event.data.object as any;

      // Check if this is a contract payment (has contractId in metadata)
      if (paymentIntent.metadata?.contractId) {
        console.log("Processing contract payment success:", paymentIntent.id);
        try {
          const result = await processPaymentSuccess(paymentIntent.id);
          if (!result.success) {
            console.error("Failed to process contract payment success:", result.error);
          } else {
            console.log("Contract payment processed successfully");
          }
        } catch (error) {
          console.error("Error processing contract payment success:", error);
        }
      }
      break;
    }
    case "payment_intent.payment_failed": {
      console.log("Processing payment_intent.payment_failed");
      const paymentIntent = event.data.object as any;

      // Check if this is a contract payment (has contractId in metadata)
      if (paymentIntent.metadata?.contractId) {
        console.log("Processing contract payment failure:", paymentIntent.id);
        try {
          const failureReason = paymentIntent.last_payment_error?.message || "Payment failed";
          const result = await processPaymentFailure(paymentIntent.id, failureReason);
          if (!result.success) {
            console.error("Failed to process contract payment failure:", result.error);
          } else {
            console.log("Contract payment failure processed successfully");
          }
        } catch (error) {
          console.error("Error processing contract payment failure:", error);
        }
      }
      break;
    }
    default:
      console.log("Unhandled event type:", event.type);
      break;
  }

  res.json({ received: true });
};
