"use client";

import { useEffect } from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { FullPageLoadingSpinner } from "@/components/ui/LoadingSpinner";
import { useAuth } from "@/hooks/use-auth";
import { useSocket } from "@/hooks/use-socket";
import { trpc } from "@/lib/trpc/client";
import { BellIcon, InformationCircleIcon } from "@heroicons/react/24/solid";
import { format } from "date-fns";
import { MessageType } from "@repo/server/src/types/chat";

interface ChatListProps {
  onChatSelect?: (chatId: string) => void;
  selectedChatId?: string | null;
}

export function ChatList({ onChatSelect, selectedChatId }: ChatListProps) {
  const router = useRouter();
  const { user } = useAuth();
  const { socket, connectionState, markAsRead } = useSocket();
  const { data: chats, isLoading } = trpc.chat.getChats.useQuery();
  const utils = trpc.useUtils();

  useEffect(() => {
    if (!socket) {
      return;
    }

    const handleNewMessage = () => {
      // Simply invalidate the query to refetch the latest data
      utils.chat.getChats.invalidate();
    };

    const handleMessagesRead = () => {
      // Simply invalidate the query to refetch the latest data
      utils.chat.getChats.invalidate();
    };
    socket.on("newMessage", handleNewMessage);
    socket.on("messagesRead", handleMessagesRead);

    return () => {
      socket.off("newMessage", handleNewMessage);
      socket.off("messagesRead", handleMessagesRead);
    };
  }, [socket, connectionState.isConnected, user?.id, utils, markAsRead]);

  if (isLoading) {
    return <FullPageLoadingSpinner />;
  }

  if (!chats || chats.length === 0) {
    return (
      <div className="tw-text-center tw-p-6 tw-bg-aims-dark-2 tw-rounded-lg">
        <p className="tw-text-aims-text-secondary">No conversations yet</p>
      </div>
    );
  }

  const handleChatClick = (chatId: string, hasUnreadMessages: boolean) => {
    // Mark messages as read when clicking into chat
    if (hasUnreadMessages) {
      markAsRead(chatId);
    }
    
    // Use the provided onChatSelect function if available (desktop view)
    if (onChatSelect) {
      onChatSelect(chatId);
    } else {
      // Fall back to navigation (mobile view)
      router.push(`/app/chat/${chatId}`);
    }
  };

  return (
    <div className="tw-flex tw-flex-col tw-gap-2 tw-overflow-y-auto tw-max-h-[calc(100vh-150px)]">
      {chats.map((chat) => {
        // Check if this is a self-chat (system notifications)
        const isSelfChat = chat.participants.length === 1 && chat.participants[0].id === user?.id;

        // Get the other participant (not the current user) or self for self-chats
        const otherParticipant = isSelfChat
          ? chat.participants[0] // For self-chats, use the user themselves
          : chat.participants.find((p) => p.id !== user?.id);

        // Check if there are unread messages
        const hasUnreadMessages =
          chat.lastMessage &&
          chat.lastMessage.sender &&
          chat.lastMessage.sender.id !== user?.id &&
          !chat.lastMessage.readBy.includes(user?.id || "");

        // Display name for the chat
        const displayName = isSelfChat ? "System Notifications" : otherParticipant?.name;

        // Profile picture for the chat
        const profilePicture = isSelfChat
          ? "/no-profile-pic.jpg" // Use default for now, can be replaced with system icon
          : (otherParticipant?.profilePicture?.url || "/no-profile-pic.jpg");

        // Check if this chat is currently selected
        const isSelected = selectedChatId === chat.id;

        return (
          <button
            key={chat.id}
            onClick={() => handleChatClick(chat.id, !!hasUnreadMessages)}
            className={`tw-flex tw-items-center tw-gap-3 tw-p-4 tw-rounded-lg tw-transition-colors ${
              isSelected
                ? "tw-bg-aims-dark-4 tw-border-l-2 tw-border-aims-primary"
                : hasUnreadMessages
                  ? "tw-bg-aims-dark-3 hover:tw-bg-aims-dark-4" : "tw-bg-aims-dark-2 hover:tw-bg-aims-dark-3"
            }`}
          >
            <div className="tw-relative">
              {isSelfChat ? (
                <InformationCircleIcon className="tw-w-12 tw-h-12 tw-text-aims-text-primary" />
              ) : (
                <Image
                  src={profilePicture}
                  alt={displayName || "User"}
                  className="tw-w-12 tw-h-12 tw-rounded-full tw-object-cover"
                  width={128}
                  height={128}
                />
              )}
              {hasUnreadMessages && (
                <div className="tw-absolute -tw-top-1 -tw-right-1 tw-w-5 tw-h-5 tw-bg-blue-500 tw-rounded-full tw-flex tw-items-center tw-justify-center">
                  <BellIcon className="tw-w-3 tw-h-3 tw-text-white" />
                </div>
              )}
            </div>
            <div className="tw-flex-1 tw-text-left tw-overflow-hidden">
              <div className="tw-flex tw-justify-between tw-items-baseline">
                <h3 className="tw-font-medium tw-text-aims-text-primary tw-truncate">
                  {displayName || "Unknown User"}
                </h3>
                {chat.lastMessage && (
                  <span className="tw-text-xs tw-text-aims-text-secondary tw-whitespace-nowrap tw-ml-2">
                    {format(new Date(chat.lastMessage.createdAt), "h:mm a")}
                  </span>
                )}
              </div>
              <p className="tw-text-sm tw-text-aims-text-secondary tw-truncate">
                {chat.lastMessage ? (
                  chat.lastMessage.type === MessageType.TEXT ? (
                    chat.lastMessage.content
                  ) : (
                    <span className="tw-italic">
                      {chat.lastMessage.type === MessageType.CAMPAIGN_INVITE
                        ? "Campaign invitation"
                        : chat.lastMessage.type === MessageType.CAMPAIGN_ACCEPT
                        ? "Campaign accepted"
                        : chat.lastMessage.type === MessageType.CAMPAIGN_REJECT
                        ? "Campaign rejected"
                        : chat.lastMessage.type === MessageType.APPLICATION_ACCEPTED
                        ? "Application accepted"
                        : chat.lastMessage.type === MessageType.APPLICATION_REJECTED
                        ? "Application rejected"
                        : chat.lastMessage.type === MessageType.CONTRACT_SENT
                        ? "Contract sent"
                        : chat.lastMessage.type === MessageType.CONTRACT_SIGNED
                        ? "Contract signed"
                        : chat.lastMessage.type === MessageType.CONTRACT_REJECTED
                        ? "Contract rejected"
                        : chat.lastMessage.type === MessageType.CONTRACT_UPDATED
                        ? "Contract updated"
                        : chat.lastMessage.type === MessageType.CONTRACT_CANCELLED
                        ? "Contract cancelled"
                        : chat.lastMessage.type === MessageType.CONTRACT_EXPIRED
                        ? "Contract expired"
                        : chat.lastMessage.type === MessageType.CONTRACT_PAYMENT_REQUIRED
                        ? "Contract payment required"
                        : chat.lastMessage.type === MessageType.CONTRACT_PAYMENT_COMPLETED
                        ? "Contract payment completed"
                        : chat.lastMessage.type === MessageType.CONTRACT_PAYMENT_FAILED
                        ? "Contract payment failed"
                        : chat.lastMessage.type === MessageType.CONTRACT_FULFILLED
                        ? "Contract fulfilled"
                        : chat.lastMessage.type === MessageType.DELIVERABLE_SUBMISSION
                        ? "Deliverable submitted"
                        : chat.lastMessage.type === MessageType.EARNINGS_CREDITED
                        ? "Earnings credited"
                        : chat.lastMessage.type === MessageType.PAYOUT_COMPLETED
                        ? "Payout completed"
                        : chat.lastMessage.type === MessageType.PAYOUT_FAILED
                        ? "Payout failed"
                        : "New message"}
                    </span>
                  )
                ) : (
                  "No messages yet"
                )}
              </p>
            </div>
          </button>
        );
      })}
    </div>
  );
}
