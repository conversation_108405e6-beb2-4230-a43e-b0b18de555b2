import crypto from "crypto";
import {
  DeleteObjectCommand,
  PutBucketPolicyCommand,
  PutObjectCommand,
  S3Client,
} from "@aws-sdk/client-s3";
import { getSignedUrl as getS3SignedUrl } from "@aws-sdk/s3-request-presigner";

// Debug logging
console.log("AWS Config Debug:", {
  region: process.env.AWS_REGION || "not set",
  hasAccessKey: !!process.env.AWS_ACCESS_KEY_ID,
  hasSecretKey: !!process.env.AWS_SECRET_ACCESS_KEY,
  hasBucketName: !!process.env.AWS_BUCKET_NAME,
});

const s3Client = new S3Client({
  region: process.env.AWS_REGION || "us-east-1",
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || "",
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || "",
  },
});

const BUCKET_NAME = process.env.AWS_BUCKET_NAME!;

// Function to configure bucket policy for public read access
export const configureBucketPolicy = async () => {
  const bucketPolicy = {
    Version: "2012-10-17",
    Statement: [
      {
        Sid: "PublicReadForGetBucketObjects",
        Effect: "Allow",
        Principal: "*",
        Action: "s3:GetObject",
        Resource: `arn:aws:s3:::${BUCKET_NAME}/*`,
      },
    ],
  };

  try {
    const command = new PutBucketPolicyCommand({
      Bucket: BUCKET_NAME,
      Policy: JSON.stringify(bucketPolicy),
    });
    await s3Client.send(command);
    console.log("Successfully configured bucket policy");
  } catch (err) {
    console.error("Error configuring bucket policy:", err);
    throw new Error("Failed to configure bucket policy");
  }
};

// Function to initialize all S3 configurations
export const initializeS3Bucket = async () => {
  try {
    await configureBucketPolicy();
    console.log("Successfully initialized S3 bucket configurations");
  } catch (err) {
    console.error("Error initializing S3 bucket:", err);
    throw err;
  }
};

// Function to get the public URL for an object
const getPublicUrl = (key: string): string => {
  return `https://${BUCKET_NAME}.s3.${process.env.AWS_REGION}.amazonaws.com/${key}`;
};

// Define valid upload types
export type UploadType = "profile-picture" | "logo" | "file" | "contract";

export const generateUploadURL = async (
  fileType: string,
  uploadType: UploadType,
): Promise<{ url: string; key: string; publicUrl: string }> => {
  if (!process.env.AWS_REGION) {
    throw new Error("AWS Region is not configured");
  }
  if (!BUCKET_NAME) {
    throw new Error("AWS Bucket Name is not configured");
  }

  // For contract PDFs, use the provided filename, otherwise generate random
  let fileName: string;
  let key: string;

  if (uploadType === "contract") {
    // Use the provided filename for contracts (it should include .pdf extension)
    fileName = fileType; // fileType is actually the filename for contracts
    key = `contracts/${fileName}`;
  } else {
    const rawBytes = crypto.randomBytes(16);
    fileName = rawBytes.toString("hex");

    // Determine the folder based on upload type
    let folder: string;
    switch (uploadType) {
      case "profile-picture":
        folder = "profile-pictures";
        break;
      case "logo":
        folder = "logos";
        break;
      case "file":
        folder = "files";
        break;
      default:
        throw new Error(`Invalid upload type: ${uploadType}`);
    }
    key = `${folder}/${fileName}`;
  }

  // Set appropriate content type
  const contentType = uploadType === "contract" ? "application/pdf" : fileType;

  const command = new PutObjectCommand({
    Bucket: BUCKET_NAME,
    Key: key,
    ContentType: contentType,
    // Removed ACL since the bucket doesn't support ACLs
  });

  try {
    console.log(`[S3] Generating signed URL for key: ${key}, contentType: ${contentType}`);
    const signedUrl = await getS3SignedUrl(s3Client, command, {
      expiresIn: 300, // 5 minutes for PDF uploads
    });

    console.log(`[S3] Signed URL generated successfully`);
    return {
      url: signedUrl,
      key: key,
      publicUrl: getPublicUrl(key),
    };
  } catch (err) {
    console.error("Error generating signed URL:", err);
    if (err instanceof Error) {
      throw new Error(`Failed to generate upload URL: ${err.message}`);
    }
    throw new Error("Failed to generate upload URL");
  }
};

export const uploadPdfDirectly = async (
  fileName: string,
  pdfBuffer: Buffer,
): Promise<{ url: string; key: string; publicUrl: string }> => {
  const key = `contracts/${fileName}`;

  const command = new PutObjectCommand({
    Bucket: BUCKET_NAME,
    Key: key,
    Body: pdfBuffer,
    ContentType: 'application/pdf',
    // Removed ACL since the bucket doesn't support ACLs
    // Public access should be configured at the bucket level
  });

  try {
    console.log(`[S3] Uploading PDF directly to S3 with key: ${key}`);
    await s3Client.send(command);

    const publicUrl = getPublicUrl(key);
    console.log(`[S3] Direct upload successful, public URL: ${publicUrl}`);

    return {
      url: publicUrl,
      key: key,
      publicUrl: publicUrl,
    };
  } catch (err) {
    console.error("Error uploading PDF directly to S3:", err);
    if (err instanceof Error) {
      throw new Error(`Failed to upload PDF directly: ${err.message}`);
    }
    throw new Error("Failed to upload PDF directly");
  }
};

export const deleteFile = async (key: string): Promise<void> => {
  const command = new DeleteObjectCommand({
    Bucket: BUCKET_NAME,
    Key: key,
  });

  try {
    await s3Client.send(command);
  } catch (err) {
    console.error("Error deleting file from S3:", err);
    throw new Error("Failed to delete file");
  }
};

export const getSignedUrl = async (key: string, contentType: string) => {
  const command = new PutObjectCommand({
    Bucket: BUCKET_NAME,
    Key: key,
    ContentType: contentType,
  });

  return getS3SignedUrl(s3Client, command, { expiresIn: 3600 }); // URL expires in 1 hour
};
