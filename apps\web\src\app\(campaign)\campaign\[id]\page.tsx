"use client";

import { useParams } from "next/navigation";
import CampaignBasicInfo from "@/components/campaign/CampaignBasicInfo";
import { selectEditBasicInfo, setBasicInfo } from "@/store/slices/editCampaign";

export default function EditCampaignIndexPage() {
  const params = useParams();
  const isEditMode = !!params?.id;

  return (
    <CampaignBasicInfo
      basicInfoSelector={selectEditBasicInfo}
      setBasicInfoAction={setBasicInfo}
      isEditMode={isEditMode}
    />
  );
}
