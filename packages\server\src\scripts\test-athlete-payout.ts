/**
 * Test script for athlete payout system
 * This script demonstrates the athlete payout workflow
 */

import mongoose from "mongoose";
import { config } from "dotenv";
import AthleteWalletModel from "../models/athleteWallet";
import AthletePayoutRequestModel from "../models/athletePayoutRequest";
import WalletTransactionModel from "../models/walletTransaction";
import { 
  getOrCreateAthleteWallet,
  creditAthleteEarnings,
  updatePendingEarnings,
  getAthleteWalletWithHistory,
} from "../services/athletePayoutService";
import { TransactionType } from "../types/athlete";

// Load environment variables
config();

async function connectToDatabase() {
  try {
    await mongoose.connect(process.env.MONGODB_URI!);
    console.log("✅ Connected to MongoDB");
  } catch (error) {
    console.error("❌ Failed to connect to MongoDB:", error);
    process.exit(1);
  }
}

async function testAthletePayoutSystem() {
  console.log("\n🧪 Testing Athlete Payout System\n");

  // Test athlete ID (replace with actual athlete ID from your database)
  const testAthleteId = "507f1f77bcf86cd799439011"; // Example ObjectId

  try {
    // Test 1: Create/Get athlete wallet
    console.log("1️⃣ Testing wallet creation...");
    const wallet = await getOrCreateAthleteWallet(testAthleteId);
    console.log(`   ✅ Wallet created/retrieved for athlete ${testAthleteId}`);
    console.log(`   💰 Available Balance: $${wallet.availableBalance}`);
    console.log(`   ⏳ Pending Earnings: $${wallet.pendingEarnings}`);
    console.log(`   📊 Total Earnings: $${wallet.totalEarnings}`);

    // Test 2: Simulate adding pending earnings (when contract becomes active)
    console.log("\n2️⃣ Testing pending earnings update...");
    const mockContractId = "507f1f77bcf86cd799439012";
    
    // Create a mock transaction for pending earnings
    await WalletTransactionModel.create({
      athleteId: testAthleteId,
      type: TransactionType.EARNINGS_CREDITED,
      amount: 150.00,
      description: "Test earnings from mock contract",
      metadata: {
        contractNumber: "TEST-001",
        campaignName: "Test Campaign",
        deliverableNames: ["Test Deliverable 1", "Test Deliverable 2"],
      },
    });

    // Update wallet balances manually for testing
    wallet.availableBalance += 150.00;
    wallet.totalEarnings += 150.00;
    await wallet.save();

    console.log(`   ✅ Added $150.00 to available balance`);

    // Test 3: Get wallet with transaction history
    console.log("\n3️⃣ Testing wallet history retrieval...");
    const walletWithHistory = await getAthleteWalletWithHistory(testAthleteId, 10);
    console.log(`   ✅ Retrieved wallet with ${walletWithHistory.transactions.length} transactions`);
    
    walletWithHistory.transactions.forEach((tx, index) => {
      console.log(`   📝 Transaction ${index + 1}: ${tx.type} - $${tx.amount} - ${tx.description}`);
    });

    // Test 4: Display final wallet state
    console.log("\n4️⃣ Final wallet state:");
    const finalWallet = await AthleteWalletModel.findOne({ athleteId: testAthleteId });
    if (finalWallet) {
      console.log(`   💰 Available Balance: $${finalWallet.availableBalance}`);
      console.log(`   ⏳ Pending Earnings: $${finalWallet.pendingEarnings}`);
      console.log(`   📊 Total Earnings: $${finalWallet.totalEarnings}`);
      console.log(`   🏦 Stripe Account: ${finalWallet.stripeConnectAccountId || 'Not connected'}`);
      console.log(`   📋 Account Status: ${finalWallet.stripeAccountStatus || 'N/A'}`);
    }

    console.log("\n✅ Athlete payout system test completed successfully!");

  } catch (error) {
    console.error("\n❌ Test failed:", error);
  }
}

async function cleanupTestData() {
  console.log("\n🧹 Cleaning up test data...");
  
  const testAthleteId = "507f1f77bcf86cd799439011";
  
  try {
    // Remove test wallet
    await AthleteWalletModel.deleteOne({ athleteId: testAthleteId });
    
    // Remove test transactions
    await WalletTransactionModel.deleteMany({ athleteId: testAthleteId });
    
    // Remove test payout requests
    await AthletePayoutRequestModel.deleteMany({ athleteId: testAthleteId });
    
    console.log("✅ Test data cleaned up");
  } catch (error) {
    console.error("❌ Failed to clean up test data:", error);
  }
}

async function main() {
  await connectToDatabase();
  
  // Run the test
  await testAthletePayoutSystem();
  
  // Optionally clean up test data
  const shouldCleanup = process.argv.includes('--cleanup');
  if (shouldCleanup) {
    await cleanupTestData();
  }
  
  await mongoose.disconnect();
  console.log("\n👋 Disconnected from MongoDB");
}

// Run the test
if (require.main === module) {
  main().catch(console.error);
}

export { testAthletePayoutSystem, cleanupTestData };
