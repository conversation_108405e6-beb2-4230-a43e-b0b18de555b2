import Link from "next/link";

import { But<PERSON> } from "./button";

export default function BookACall() {
  return (
    <Button asChild className="tw-w-full sm:tw-w-auto tw-h-12 sm:tw-h-10 tw-text-sm sm:tw-text-base">
      <Link
        href="https://calendly.com/bradleyneuhaus-aimsmarketing/30min"
        target="_blank"
        className="tw-flex tw-items-center tw-justify-center tw-min-h-[44px]"
        aria-label="Book a call with AIMS team"
      >
        Book a Call
      </Link>
    </Button>
  );
}
