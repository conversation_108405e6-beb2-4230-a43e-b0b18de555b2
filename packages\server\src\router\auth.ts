import {
  checkEmailVerificationStatus,
  createNewUser,
  deleteAccount,
  generateForgotPasswordLink,
  generateVerificationLink,
  grantAccessToken,
  removeAllRefreshTokens,
  removeRefreshToken,
  signIn,
  signInWithOauth,
  signOut,
  updatePassword,
  updatePasswordWhenLoggedIn,
  verifyEmail,
  verifyEmailCode,
} from "../controllers/auth";
import {
  isValidPassResetToken,
  privateProcedure,
  publicProcedure,
  trpc,
} from "../lib/trpc";
import { verifyCaptcha } from "../utils/recaptcha";
import {
  emailSchema,
  forgotPasswordSchema,
  newUserSchema,
  refreshTokenSchema,
  resetPasswordSchema,
  signInSchema,
  signInWithOauthSchema,
  tokenSchema,
  updatePasswordSchema,
  updatePasswordWhenLoggedInSchema,
  verifyEmailCodeSchema,
  verifyTokenSchema,
} from "../validators/auth";

// process user authentication, public procedure
export const authRouter = trpc.router({
  signup: publicProcedure.input(newUserSchema).mutation(async ({ input }) => {
    await verifyCaptcha(input.captchaToken);
    return createNewUser(
      input.email,
      input.password,
      input.name,
      input.userType,
    );
  }),
  signin: publicProcedure
    .input(signInSchema)
    .mutation(async ({ ctx, input }) => {
      return signIn(ctx.req, input.email, input.password);
    }),
  profile: privateProcedure.query(({ ctx }) => {
    return ctx.req.user;
  }),
  signout: privateProcedure
    .input(refreshTokenSchema)
    .mutation(async ({ ctx, input }) => {
      return signOut(ctx.req.user.id, input.refreshToken);
    }),
  forgotPassword: publicProcedure
    .input(forgotPasswordSchema)
    .mutation(async ({ input }) => {
      await verifyCaptcha(input.captchaToken);
      return generateForgotPasswordLink(input.email);
    }),
  refreshToken: publicProcedure
    .input(refreshTokenSchema)
    .mutation(async ({ input }) => {
      return grantAccessToken(input.refreshToken);
    }),
  verifyPasswordResetToken: publicProcedure
    .input(verifyTokenSchema)
    .use(isValidPassResetToken)
    .mutation(async () => {
      return {
        valid: true,
      };
    }),
  verifyEmailCode: publicProcedure
    .input(verifyEmailCodeSchema)
    .mutation(async ({ ctx, input }) => {
      return verifyEmailCode(ctx.req, input.email, input.code);
    }),
  resetPassword: publicProcedure
    .input(resetPasswordSchema)
    .use(isValidPassResetToken)
    .mutation(async ({ input }) => {
      return updatePassword(input.id, input.password);
    }),
  sendEmailVerificationLink: publicProcedure
    .input(emailSchema)
    .mutation(async ({ input }) => {
      return generateVerificationLink(input.email);
    }),
  verifyEmailVerificationLink: publicProcedure
    .input(verifyTokenSchema)
    .mutation(async ({ ctx, input }) => {
      return verifyEmail(input.id, input.token, ctx.req);
    }),
  deleteRefreshToken: privateProcedure
    .input(tokenSchema)
    .mutation(({ ctx, input }) => {
      return removeRefreshToken(ctx.req.user.id, input.token);
    }),
  deleteAllRefreshTokens: privateProcedure.query(({ ctx }) => {
    return removeAllRefreshTokens(ctx.req.user.id);
  }),
  updatePassword: privateProcedure
    .input(updatePasswordSchema)
    .mutation(async ({ ctx, input }) => {
      return updatePassword(ctx.req.user.id, input.newPassword);
    }),
  updatePasswordWhenLoggedIn: privateProcedure
    .input(updatePasswordWhenLoggedInSchema)
    .mutation(async ({ ctx, input }) => {
      return updatePasswordWhenLoggedIn(
        ctx.req.user.id,
        input.currentPassword,
        input.newPassword,
      );
    }),
  deleteAccount: privateProcedure.mutation(async ({ ctx }) => {
    return deleteAccount(ctx.req.user.id);
  }),
  checkEmailVerificationStatus: publicProcedure
    .input(emailSchema)
    .query(async ({ input }) => {
      return checkEmailVerificationStatus(input.email);
    }),
  signInWithOauth: publicProcedure
    .input(signInWithOauthSchema)
    .mutation(async ({ input }) => {
      return signInWithOauth(input.oauthIdToken, input.userType);
    }),
});

export type AuthRouter = typeof authRouter;
