import { UserType } from "@/types/user";
import { useDispatch, useSelector } from "react-redux";

import type { RootState } from "../store";
import { client } from "../lib/trpc/client";
import { TokensStorage } from "../lib/utils/tokens-storage";
import {
  clearAuthState,
  getAuthState,
  setAuthState,
  setError,
  setLoading,
} from "../store/slices/auth";
import { resetOnboarding } from "../store/slices/onboarding";
import type { AuthResponse } from "@repo/server/src/models/user";

// Type guard to check if the response contains auth data
function hasAuthData(response: any): response is AuthResponse {
  return (
    response &&
    typeof response === "object" &&
    "profile" in response &&
    "tokens" in response &&
    typeof response.tokens === "object" &&
    "access" in response.tokens &&
    "refresh" in response.tokens
  );
}

export const useAuth = () => {
  const dispatch = useDispatch();
  const authState = useSelector((state: RootState) => getAuthState(state));

  const getProfile = async (forceRefresh = false) => {
    try {
      // Only return cached profile if not forcing refresh
      if (!forceRefresh && authState.profile && authState.tokens) {
        return authState.profile;
      }

      const storedTokens = TokensStorage.getTokens();
      if (!storedTokens) {
        return null;
      }

      const tokens = {
        access: storedTokens.accessToken,
        refresh: storedTokens.refreshToken,
      };

      dispatch(setLoading(true));
      const profile = await client.auth.profile.query();
      if (profile) {
        dispatch(setAuthState({ profile, tokens }));
        return profile;
      }
      return null;
    } catch (error) {
      const message =
        error instanceof Error ? error.message : "An error occurred";
      dispatch(setError(message));
      dispatch(clearAuthState());
      TokensStorage.clearTokens();
      return null;
    } finally {
      dispatch(setLoading(false));
    }
  };

  const signUp = async (
    name: string,
    email: string,
    password: string,
    userType: UserType,
    captchaToken: string,
  ) => {
    try {
      dispatch(setLoading(true));
      const response = await client.auth.signup.mutate({
        name,
        email,
        password,
        userType,
        captchaToken,
      });
      const dev = process.env.NODE_ENV === "development";
      if (dev) {
        if (hasAuthData(response)) {
          TokensStorage.setTokens(
            response.tokens.access,
            response.tokens.refresh,
          );
          dispatch(setAuthState(response));
        }
        return response;
      }
      return true;
    } catch (error) {
      const message =
        error instanceof Error ? error.message : "An error occurred";
      dispatch(setError(message));
      throw error;
    } finally {
      dispatch(setLoading(false));
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      dispatch(setLoading(true));
      const response = await client.auth.signin.mutate({ email, password });
      const dev = process.env.NODE_ENV === "development";
      if (dev) {
        if (hasAuthData(response)) {
          TokensStorage.setTokens(
            response.tokens.access,
            response.tokens.refresh,
          );
          dispatch(setAuthState(response));
        }
        return response;
      }
      // Sign in now only returns a message about verification code
      return response;
    } catch (error) {
      const message =
        error instanceof Error ? error.message : "An error occurred";
      dispatch(setError(message));
      throw error;
    } finally {
      dispatch(setLoading(false));
    }
  };

  const verifyEmailCode = async (email: string, code: string) => {
    try {
      dispatch(setLoading(true));
      const response = await client.auth.verifyEmailCode.mutate({
        email,
        code,
      });
      if (hasAuthData(response)) {
        TokensStorage.setTokens(
          response.tokens.access,
          response.tokens.refresh,
        );
        dispatch(setAuthState(response));
      }
      return response;
    } catch (error) {
      const message =
        error instanceof Error ? error.message : "An error occurred";
      dispatch(setError(message));
      throw error;
    } finally {
      dispatch(setLoading(false));
    }
  };

  const resendVerificationEmail = async (email: string) => {
    try {
      await client.auth.sendEmailVerificationLink.mutate({ email });
    } catch (error) {
      throw error;
    }
  };

  const signOut = async () => {
    try {
      if (authState.tokens) {
        await client.auth.signout.mutate({
          refreshToken: authState.tokens.refresh,
        });
      }
    } catch (error) {
      console.error("Error during sign out:", error);
    } finally {
      dispatch(clearAuthState());
      dispatch(resetOnboarding());
      TokensStorage.clearTokens();
    }
  };

  const forgotPassword = async (email: string, captchaToken: string) => {
    try {
      dispatch(setLoading(true));
      await client.auth.forgotPassword.mutate({ email, captchaToken });
      return true;
    } catch (error) {
      const message =
        error instanceof Error ? error.message : "An error occurred";
      dispatch(setError(message));
      throw error;
    } finally {
      dispatch(setLoading(false));
    }
  };

  const signInWithGoogle = async (oauthIdToken: string, userType: UserType) => {
    try {
      dispatch(setLoading(true));
      const response = await client.auth.signInWithOauth.mutate({
        oauthIdToken,
        userType,
      });
      if (hasAuthData(response)) {
        TokensStorage.setTokens(
          response.tokens.access,
          response.tokens.refresh,
        );
        dispatch(setAuthState(response));
      }
      return response;
    } catch (error) {
      const message =
        error instanceof Error ? error.message : "An error occurred";
      dispatch(setError(message));
      throw error;
    } finally {
      dispatch(setLoading(false));
    }
  };

  return {
    user: authState.profile,
    tokens: authState.tokens,
    loading: authState.loading,
    error: authState.error,
    isAuthenticated: authState.profile !== null,
    signIn,
    signUp,
    verifyEmailCode,
    resendVerificationEmail,
    signOut,
    getProfile,
    forgotPassword,
    signInWithGoogle,
  };
};
