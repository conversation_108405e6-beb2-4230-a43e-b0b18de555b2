import { privateProcedure, trpc } from "../lib/trpc";
import { brandProfileSchema, uploadUrlSchema, profilePictureSchema } from "../validators/profile";
import { getBrandProfile, updateBrandProfile, getUploadUrl, updateLogo, getBrand, createCheckoutSession } from "../controllers/brand";
import * as yup from "yup";
export const brandRouter = trpc.router({
  getProfile: privateProcedure
    .query(async ({ ctx }) => {
      return getBrandProfile(ctx.req.user.id);
    }),

  updateBrandProfile: privateProcedure
    .input(brandProfileSchema)
    .mutation(async ({ ctx, input }) => {
      return updateBrandProfile(ctx.req.user.id, ctx.req.user.userType, input);
    }),

  getUploadUrl: privateProcedure
    .input(uploadUrlSchema)
    .mutation(async ({ input }) => {
      return getUploadUrl(input.fileType);
    }),

  updateLogo: privateProcedure
    .input(profilePictureSchema)
    .mutation(async ({ ctx, input }) => {
      return updateLogo(ctx.req.user.id, input.url, input.key);
    }),

  getBrand: privateProcedure
    .input(yup.string().required())
    .query(async ({ ctx, input }) => {
      return getBrand(input);
    }),

  createCheckoutSession: privateProcedure
    .mutation(async ({ ctx }) => {
      return createCheckoutSession(ctx.req.user.id);
    }),
}); 