import { DocumentTextIcon, UserIcon } from "@heroicons/react/24/outline";

interface UserTypeToggleProps {
  userType: "brand" | "athlete";
  onChange: (type: "brand" | "athlete") => void;
}

export function UserTypeToggle({ userType, onChange }: UserTypeToggleProps) {
  return (
    <div className="tw-flex tw-rounded-lg tw-bg-aims-dark-2 tw-mb-6 tw-border tw-border-aims-dark-3">
      <button
        className={`tw-flex-1 tw-relative tw-py-2 tw-px-4 tw-rounded-md tw-text-sm tw-font-medium tw-transition-all tw-duration-300 tw-ease-in-out ${
          userType === "brand"
            ? "tw-text-aims-text-primary"
            : "tw-text-aims-text-secondary hover:tw-text-aims-text-primary"
        }`}
        onClick={() => onChange("brand")}
        type="button"
      >
        {/* Background highlight */}
        <div
          className={`tw-absolute tw-inset-0 tw-bg-aims-dark-4 tw-rounded-md tw-transition-opacity tw-duration-300 tw-ease-in-out ${
            userType === "brand" ? "tw-opacity-100" : "tw-opacity-0"
          }`}
        />
        {/* Content */}
        <span className="tw-flex tw-items-center tw-justify-center tw-gap-2 tw-relative tw-z-10">
          <DocumentTextIcon className="tw-w-5 tw-h-5" />
          Brand
        </span>
      </button>
      <button
        className={`tw-flex-1 tw-relative tw-py-2 tw-px-4 tw-rounded-md tw-text-sm tw-font-medium tw-transition-all tw-duration-300 tw-ease-in-out ${
          userType === "athlete"
            ? "tw-text-aims-text-primary"
            : "tw-text-aims-text-secondary hover:tw-text-aims-text-primary"
        }`}
        onClick={() => onChange("athlete")}
        type="button"
      >
        {/* Background highlight */}
        <div
          className={`tw-absolute tw-inset-0 tw-bg-aims-dark-4 tw-rounded-md tw-transition-opacity tw-duration-300 tw-ease-in-out ${
            userType === "athlete" ? "tw-opacity-100" : "tw-opacity-0"
          }`}
        />
        {/* Content */}
        <span className="tw-flex tw-items-center tw-justify-center tw-gap-2 tw-relative tw-z-10">
          <UserIcon className="tw-w-5 tw-h-5" />
          Athlete / Influencer
        </span>
      </button>
    </div>
  );
}
