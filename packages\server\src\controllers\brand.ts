import BrandModel from "../models/brand";
import { ExtendedTRPCError } from "../utils/trpc";
import { UserType } from "../models/user";
import { generateUploadURL, deleteFile } from "../utils/s3";
import { UploadUrlResponse } from "../validators/profile";
import { BrandProfile } from "../types/brand";
import { stripe } from "../lib/stripe";

export const updateBrandProfile = async (userId: string, userType: UserType, input: any) => {
    const profile = await BrandModel.findOne({ userId });
    if (!profile) {
        throw new ExtendedTRPCError("NOT_FOUND", "Brand profile not found");
    }
    if (userType !== "brand") {
        throw new ExtendedTRPCError("FORBIDDEN", "Only brand users can update brand profiles");
    }
    console.log(input);
    Object.assign(profile, input);
    await profile.save();
    return profile;
};

export const getBrandProfile = async (userId: string) => {
    const profile = await BrandModel.findOne({ userId });
    if (!profile) {
        throw new ExtendedTRPCError("NOT_FOUND", "Brand profile not found");
    }
    return profile;
};

export const getBrand = async (brandId: string) => {
    const brand = await BrandModel.findById(brandId);
    if (!brand) {
        throw new ExtendedTRPCError("NOT_FOUND", "Brand not found");
    }
    return brand as BrandProfile;
};
export const getUploadUrl = async (fileType: string): Promise<UploadUrlResponse> => {
    try {
        return await generateUploadURL(fileType, 'logo');
    } catch (error) {
        throw new ExtendedTRPCError("INTERNAL_SERVER_ERROR", "Failed to generate upload URL");
    }
};

export const updateLogo = async (userId: string, url: string, key: string) => {
    const brand = await BrandModel.findOne({ userId });
    if (!brand) {
        throw new ExtendedTRPCError("NOT_FOUND", "Brand profile not found");
    }

    // Delete old logo if exists
    if (brand.logo?.key) {
        try {
            await deleteFile(brand.logo.key);
            console.log('Successfully deleted old logo');
        } catch (error) {
            console.error('Failed to delete old logo:', error);
            // We continue even if deletion fails, to not block the new upload
        }
    }

    brand.logo = {
        url,
        key,
        uploadedAt: new Date(),
    };

    await brand.save();
    return { success: true };
};

export const createCheckoutSession = async (userId: string) => {
    const brand = await BrandModel.findOne({ userId });
    if (!brand) {
        throw new ExtendedTRPCError("NOT_FOUND", "Brand profile not found");
    }
    const session = await stripe.checkout.sessions.create({
        mode: "subscription",
        payment_method_types: ["card"],
        line_items: [
            {
                price: process.env.STRIPE_PRICE_ID,
                quantity: 1,
            }
        ],
        success_url: `${process.env.CLIENT_URL}/app/brand/`,
        cancel_url: `${process.env.CLIENT_URL}/app/brand/`,
        metadata: {
            brandId: brand._id.toString(),
        },
    });
    return session;
};
