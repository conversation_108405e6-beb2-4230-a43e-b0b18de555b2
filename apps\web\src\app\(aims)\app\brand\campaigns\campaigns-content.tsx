"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import {
  FullPageLoadingSpinner,
  LoadingSpinner,
} from "@/components/ui/LoadingSpinner";
import { Table, TableColumn } from "@/components/ui/Table";
import { trpc } from "@/lib/trpc/client";
import { resetCampaign } from "@/store/slices/campaign";
import {
  CheckIcon,
  ChevronDownIcon,
  PencilIcon,
  PlusIcon,
  TrashIcon,
} from "@heroicons/react/24/outline";
import * as Select from "@radix-ui/react-select";
import { format } from "date-fns";
import { useDispatch } from "react-redux";

import { CampaignStatus, SerializedCampaign } from "@repo/server/src/types/campaign";

export default function CampaignsContent() {
  const {
    data: campaigns,
    isLoading,
    refetch,
  } = trpc.campaign.getBrandCampaigns.useQuery();
  const deleteMutation = trpc.campaign.delete.useMutation();
  const updateStatusMutation = trpc.campaign.updateStatus.useMutation();
  const [deletingId, setDeletingId] = useState<string | null>(null);
  const [editingId, setEditingId] = useState<string | null>(null);
  const router = useRouter();
  const dispatch = useDispatch();

  const getStatusColor = (status: CampaignStatus) => {
    switch (status) {
      case CampaignStatus.ACTIVE:
        return "tw-bg-green-900/30 tw-text-green-400 tw-ring-green-400/30";
      case CampaignStatus.DRAFT:
        return "tw-bg-gray-900/30 tw-text-gray-400 tw-ring-gray-400/30";
      case CampaignStatus.PAUSED:
        return "tw-bg-yellow-900/30 tw-text-yellow-400 tw-ring-yellow-400/30";
      case CampaignStatus.COMPLETED:
        return "tw-bg-blue-900/30 tw-text-blue-400 tw-ring-blue-400/30";
      case CampaignStatus.CANCELLED:
        return "tw-bg-red-900/30 tw-text-red-400 tw-ring-red-400/30";
    }
  };

  const getStatusTextColor = (status: CampaignStatus) => {
    switch (status) {
      case CampaignStatus.ACTIVE:
        return "tw-text-green-400";
      case CampaignStatus.DRAFT:
        return "tw-text-gray-400";
      case CampaignStatus.PAUSED:
        return "tw-text-yellow-400";
      case CampaignStatus.COMPLETED:
        return "tw-text-blue-400";
      case CampaignStatus.CANCELLED:
        return "tw-text-red-400";
    }
  };

  const handleDelete = async (campaignId: string) => {
    if (
      !confirm(
        "Are you sure you want to delete this campaign? This action cannot be undone.",
      )
    )
      return;
    setDeletingId(campaignId);
    try {
      await deleteMutation.mutateAsync({ campaignId });
      await refetch();
    } catch (err) {
      alert("Failed to delete campaign" + err);
    } finally {
      setDeletingId(null);
    }
  };

  useEffect(() => {
    dispatch(resetCampaign());
  }, [dispatch]);

  if (isLoading) {
    return <FullPageLoadingSpinner />;
  }

  // Define columns for the Table component
  const columns: TableColumn<SerializedCampaign>[] = [
    {
      header: "Name",
      className:
        "tw-py-3.5 tw-pl-4 tw-pr-3 tw-text-left tw-text-sm tw-font-medium tw-text-aims-text-secondary tw-sm:tw-pl-6",
      cell: (campaign) => (
        <div className="tw-flex tw-flex-col">
          <div className="tw-font-medium tw-text-aims-text-primary">
            <Link
              href={`/app/campaign/${campaign.id}?brandView=true`}
              className="tw-text-aims-text-primary hover:tw-text-aims-primary"
            >
              {campaign.name}
            </Link>
          </div>
        </div>
      ),
    },
    {
      header: "Status",
      className:
        "tw-px-3 tw-py-3.5 tw-text-left tw-text-sm tw-font-medium tw-text-aims-text-secondary",
      cell: (campaign) => (
        <>
          <Select.Root
            value={campaign.status}
            onValueChange={async (newStatus) => {
              if (newStatus !== campaign.status) {
                try {
                  setEditingId(campaign.id);
                  await updateStatusMutation.mutateAsync({
                    campaignId: campaign.id,
                    status: newStatus,
                  });
                  await refetch();
                } catch (err) {
                  alert("Failed to update status");
                  console.log(err);
                } finally {
                  setEditingId(null);
                }
              }
            }}
            disabled={editingId === campaign.id}
          >
            <Select.Trigger
              className={`tw-inline-flex tw-items-center tw-rounded-md tw-px-2 tw-py-1 tw-text-xs tw-font-medium tw-ring-1 tw-ring-inset tw-bg-transparent tw-outline-none ${getStatusColor(campaign.status)}`}
            >
              <Select.Value />
              <ChevronDownIcon className="tw-ml-2 tw-h-4 tw-w-4" />
            </Select.Trigger>
            <Select.Portal>
              <Select.Content
                className="tw-z-50 tw-mt-1 tw-max-h-60 tw-w-[200px] tw-overflow-auto tw-rounded-md tw-bg-aims-dark-2 tw-py-1 tw-text-base tw-shadow-lg focus:tw-outline-none sm:tw-text-sm"
                position="popper"
                sideOffset={4}
              >
                <Select.Viewport>
                  {Object.values(CampaignStatus).map((status) => (
                    <Select.Item
                      key={status}
                      value={status}
                      className={`tw-cursor-pointer tw-select-none tw-py-2 tw-px-4 ${getStatusTextColor(status)}`}
                    >
                      <span className="tw-flex tw-items-center">
                        <Select.ItemText>{status}</Select.ItemText>
                        <Select.ItemIndicator>
                          <CheckIcon className="tw-ml-2 tw-h-4 tw-w-4" />
                        </Select.ItemIndicator>
                      </span>
                    </Select.Item>
                  ))}
                </Select.Viewport>
              </Select.Content>
            </Select.Portal>
          </Select.Root>
          {editingId === campaign.id && <LoadingSpinner />}
        </>
      ),
    },
    {
      header: "Price",
      className:
        "tw-px-3 tw-py-3.5 tw-text-left tw-text-sm tw-font-medium tw-text-aims-text-secondary",
      cell: (campaign) => (
        <span className="tw-text-sm tw-text-aims-text-secondary">
          {campaign.price.toLocaleString("en-US", {
            style: "currency",
            currency: "USD",
          })}
        </span>
      ),
    },
    {
      header: "Start Date",
      className:
        "tw-px-3 tw-py-3.5 tw-text-left tw-text-sm tw-font-medium tw-text-aims-text-secondary",
      cell: (campaign) => (
        <span className="tw-text-sm tw-text-aims-text-secondary">
          {format(new Date(campaign.startDate), "MMM d, yyyy")}
        </span>
      ),
    },
    {
      header: "End Date",
      className:
        "tw-px-3 tw-py-3.5 tw-text-left tw-text-sm tw-font-medium tw-text-aims-text-secondary",
      cell: (campaign) => (
        <span className="tw-text-sm tw-text-aims-text-secondary">
          {format(new Date(campaign.endDate), "MMM d, yyyy")}
        </span>
      ),
    },
    {
      header: "Deliverables",
      className:
        "tw-px-3 tw-py-3.5 tw-text-left tw-text-sm tw-font-medium tw-text-aims-text-secondary",
      cell: (campaign) => (
        <span className="tw-text-aims-text-secondary">
          {campaign.deliverables.length}
        </span>
      ),
    },
  ];

  return (
    <div className="tw-container tw-mx-auto tw-py-8 tw-px-4 sm:tw-px-6 lg:tw-px-8">
      <div className="tw-sm:flex tw-sm:items-center">
        <div className="tw-sm:flex-auto">
          <h4>Your Campaigns</h4>
          <p className="tw-mt-2 tw-text-sm tw-text-aims-text-secondary">
            A list of all your campaigns including their status, price, and
            other details.
          </p>
        </div>
        <div className="tw-mt-4 tw-sm:ml-16 tw-sm:mt-0 tw-sm:flex-none">
          <Button
            className="tw-flex tw-items-center tw-gap-2"
            asChild
          >
            <Link href="/campaign/new">
              <PlusIcon className="tw-w-5 tw-h-5" />
              Create Campaign
            </Link>
          </Button>
        </div>
      </div>
      <div className="tw-mt-8 tw-flow-root">
        <div className="tw--mx-4 tw--my-2 tw-overflow-x-auto tw-sm:tw--mx-6 lg:tw--mx-8">
          <div className="tw-inline-block tw-min-w-full tw-py-2 tw-align-middle tw-sm:tw-px-6 lg:tw-px-8">
            <div className="tw-overflow-hidden tw-rounded-lg tw-ring-1 tw-ring-aims-dark-3">
              <Table
                columns={columns}
                data={Array.isArray(campaigns) ? campaigns : []}
                rowKey={(row) => row.id}
                actions={(campaign) => (
                  <div className="tw-flex tw-justify-end tw-gap-2">
                    <Button
                      size="sm"
                      asChild
                    >
                      <Link href={`/app/campaign/${campaign.id}?brandView=true`}>
                        Manage
                      </Link>
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="tw-text-aims-dark-6 hover:tw-text-aims-text-primary"
                      asChild
                    >
                      <Link href={`/campaign/${campaign.id}`}>
                      <PencilIcon className="tw-h-4 tw-w-4" />
                      <span className="tw-sr-only">Edit</span>
                      </Link>
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="tw-text-red-400/70 hover:tw-text-red-400"
                      onClick={() => handleDelete(campaign.id)}
                      disabled={deletingId === campaign.id}
                    >
                      {deletingId === campaign.id ? (
                        <LoadingSpinner />
                      ) : (
                        <TrashIcon className="tw-h-4 tw-w-4" />
                      )}
                      <span className="tw-sr-only">Delete</span>
                    </Button>
                  </div>
                )}
                emptyState={
                  <div className="tw-flex tw-flex-col tw-items-center tw-gap-2">
                    <p className="tw-font-medium tw-text-aims-text-primary">
                      No campaigns found
                    </p>
                    <p className="tw-text-aims-text-secondary">
                      Get started by creating your first campaign
                    </p>
                    <Button
                      onClick={() => router.push("/campaign/new")}
                      className="tw-flex tw-items-center tw-gap-2"
                    >
                      <PlusIcon className="tw-w-5 tw-h-5" />
                      Create Campaign
                    </Button>
                  </div>
                }
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
