"use client";

import React, { useState } from "react";

import { Input } from "../ui/input";
import { useDispatch, useSelector } from "react-redux";
import { selectBasicInfo, setBasicInfo } from "@/store/slices/campaign";
import { selectEditBasicInfo, setBasicInfo as setEditBasicInfo } from "@/store/slices/editCampaign";
import { Label } from "../ui/label";
import { INTERESTS } from "@/lib/utils"

interface InterestsProps {
  isEditMode: boolean;
}

export default function Interests({ isEditMode }: InterestsProps) {
  const [input, setInput] = useState("");
  const dispatch = useDispatch();
  const basicInfo = useSelector(
    isEditMode ? selectEditBasicInfo : selectBasicInfo,
  );
  const selected = basicInfo.interests || [];

  const filteredTags = INTERESTS.filter(
    (tag) =>
      tag.toLowerCase().includes(input.toLowerCase()) &&
      !selected.includes(tag),
  );

  const handleAddTag = (tag: string) => {
    if (!selected.includes(tag)) {
      if (isEditMode) {
        dispatch(setEditBasicInfo({ ...basicInfo, interests: [...selected, tag] }));
      } else {
        dispatch(setBasicInfo({ ...basicInfo, interests: [...selected, tag] }));
      }
    }

    setInput("");
  };

  const handleRemoveTag = (tag: string) => {
    if (isEditMode) {
      dispatch(setEditBasicInfo({ ...basicInfo, interests: selected.filter((t) => t !== tag) }));
    } else {
      dispatch(setBasicInfo({ ...basicInfo, interests: selected.filter((t) => t !== tag) }));
    }
  };

  const handleInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" && input.trim()) {
      handleAddTag(input.trim());
    }
  };

  return (
    <div className="tw-mx-auto">
      <h2 className="tw-text-2xl tw-font-bold tw-text-center tw-text-aims-text-primary">
        Campaign interest matching
      </h2>
      <p className="tw-text-center tw-text-gray-400 tw-mb-8">
        Select a campaign interest to better match with an Athlete/Influencer.
      </p>

      <Label required>Interests for influencer matching</Label>
      <div className="tw-flex tw-flex-wrap tw-gap-2 tw-mb-2">
        {selected.map((tag) => (
          <span
            key={tag}
            className="tw-bg-aims-primary tw-text-black tw-px-3 tw-py-1 tw-rounded-full tw-flex tw-items-center tw-gap-1 tw-text-sm tw-font-semibold"
          >
            {tag}
            <button
              type="button"
              onClick={() => handleRemoveTag(tag)}
              className="tw-ml-1 tw-text-black hover:tw-text-red-600"
              aria-label={`Remove ${tag}`}
            >
              ×
            </button>
          </span>
        ))}
        <Input
          placeholder="Enter a new tag or search for an existing one."
          value={input}
          onChange={(e) => setInput(e.target.value)}
          onKeyDown={handleInputKeyDown}
          className="tw-min-w-[120px] tw-mb-6"
        />
      </div>

      <div className="tw-flex tw-flex-wrap tw-gap-2">
        {filteredTags.map((tag) => (
          <button
            key={tag}
            type="button"
            onClick={() => handleAddTag(tag)}
            className={`tw-px-3 tw-py-1 tw-rounded-full tw-text-sm tw-font-semibold tw-border tw-border-gray-600 tw-bg-gray-700 tw-text-gray-200 hover:tw-bg-aims-primary hover:tw-text-black ${
              selected.includes(tag) ? "tw-bg-aims-primary tw-text-black" : ""
            }`}
          >
            {tag}
          </button>
        ))}
      </div>
    </div>
  );
}
