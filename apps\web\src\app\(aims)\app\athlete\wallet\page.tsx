"use client";

import { useState } from "react";
import { trpc } from "@/lib/trpc/client";
import { FullPageLoadingSpinner } from "@/components/ui/LoadingSpinner";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/components/ui/toast/use-toast";
import { 
  BanknotesIcon, 
  ClockIcon, 
  CurrencyDollarIcon,
  ArrowUpIcon,
  ArrowDownIcon,
} from "@heroicons/react/24/outline";
import { format } from "date-fns";
import PayoutRequestModal from "@/components/athlete/PayoutRequestModal";
import StripeConnectSetup from "@/components/athlete/StripeConnectSetup";

export default function AthleteWalletPage() {
  const { toast } = useToast();
  const [showPayoutModal, setShowPayoutModal] = useState(false);

  const MINIMUM_PAYOUT = 5;

  // Fetch wallet data
  const { data: walletData, isLoading: walletLoading, refetch: refetchWallet } = 
    trpc.athlete.getWallet.useQuery();

  // Fetch payout requests
  const { data: payoutRequests, isLoading: payoutLoading, refetch: refetchPayouts } =
    trpc.athlete.getPayoutRequests.useQuery({ limit: 10 });

  // Fetch Stripe Connect status
  const { data: stripeStatus, isLoading: stripeLoading, refetch: refetchStripeStatus } =
    trpc.athlete.getStripeStatus.useQuery();

  const isLoading = walletLoading || payoutLoading || stripeLoading;

  if (isLoading) {
    return <FullPageLoadingSpinner />;
  }

  const wallet = walletData?.wallet;
  const transactions = walletData?.transactions || [];

  const handlePayoutSuccess = () => {
    setShowPayoutModal(false);
    refetchWallet();
    refetchPayouts();
    toast({
      title: "Payout Requested",
      description: "Your payout request has been submitted and will be processed within 1-2 business days.",
      variant: "default",
    });
  };

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case "EARNINGS_CREDITED":
        return <ArrowDownIcon className="tw-h-4 tw-w-4 tw-text-green-600" />;
      case "PAYOUT_REQUESTED":
      case "PAYOUT_COMPLETED":
        return <ArrowUpIcon className="tw-h-4 tw-w-4 tw-text-blue-600" />;
      case "PAYOUT_FAILED":
      case "PAYOUT_CANCELLED":
        return <ArrowDownIcon className="tw-h-4 tw-w-4 tw-text-red-600" />;
      default:
        return <CurrencyDollarIcon className="tw-h-4 tw-w-4 tw-text-gray-600" />;
    }
  };

  const getTransactionColor = (type: string) => {
    switch (type) {
      case "EARNINGS_CREDITED":
      case "PAYOUT_CANCELLED":
        return "tw-text-green-600";
      case "PAYOUT_REQUESTED":
      case "PAYOUT_COMPLETED":
        return "tw-text-blue-600";
      case "PAYOUT_FAILED":
        return "tw-text-red-600";
      default:
        return "tw-text-gray-600";
    }
  };

  const getPayoutStatusBadge = (status: string) => {
    switch (status) {
      case "PENDING":
        return <Badge variant="secondary" className="tw-bg-yellow-100 tw-text-yellow-800">Pending</Badge>;
      case "PROCESSING":
        return <Badge variant="secondary" className="tw-bg-blue-100 tw-text-blue-800">Processing</Badge>;
      case "COMPLETED":
        return <Badge variant="secondary" className="tw-bg-green-100 tw-text-green-800">Completed</Badge>;
      case "FAILED":
        return <Badge variant="destructive">Failed</Badge>;
      case "CANCELLED":
        return <Badge variant="outline">Cancelled</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <div className="tw-p-4 sm:tw-p-6 lg:tw-p-8 tw-space-y-6">
      {/* Header */}
      <div className="tw-flex tw-flex-col sm:tw-flex-row tw-justify-between tw-items-start sm:tw-items-center tw-gap-4">
        <div>
          <h1 className="tw-text-2xl tw-font-bold tw-text-aims-text-primary">Wallet</h1>
          <p className="tw-text-sm tw-text-aims-text-secondary">
            Manage your earnings and request payouts
          </p>
        </div>
        <Button
          onClick={() => setShowPayoutModal(true)}
          disabled={!wallet?.availableBalance || wallet.availableBalance < MINIMUM_PAYOUT || !stripeStatus?.hasAccount || stripeStatus?.status !== 'enabled'}
          className="tw-h-10 sm:tw-h-9"
        >
          Request Payout
        </Button>
      </div>

      {/* Balance Cards */}
      <div className="tw-grid tw-grid-cols-1 sm:tw-grid-cols-2 lg:tw-grid-cols-3 tw-gap-4 sm:tw-gap-6">
        <Card>
          <CardHeader className="tw-pb-2">
            <CardTitle className="tw-text-sm tw-font-medium tw-flex tw-items-center tw-gap-2">
              <CurrencyDollarIcon className="tw-h-4 tw-w-4 tw-text-green-600" />
              Available Balance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="tw-text-2xl tw-font-bold tw-text-green-600">
              ${wallet?.availableBalance?.toFixed(2) || '0.00'}
            </div>
            <p className="tw-text-xs tw-text-aims-text-secondary tw-mt-1">
              Ready for withdrawal
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="tw-pb-2">
            <CardTitle className="tw-text-sm tw-font-medium tw-flex tw-items-center tw-gap-2">
              <ClockIcon className="tw-h-4 tw-w-4 tw-text-yellow-600" />
              Pending Earnings
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="tw-text-2xl tw-font-bold tw-text-yellow-600">
              ${wallet?.pendingEarnings?.toFixed(2) || '0.00'}
            </div>
            <p className="tw-text-xs tw-text-aims-text-secondary tw-mt-1">
              From active contracts
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="tw-pb-2">
            <CardTitle className="tw-text-sm tw-font-medium tw-flex tw-items-center tw-gap-2">
              <BanknotesIcon className="tw-h-4 tw-w-4 tw-text-blue-600" />
              Total Earnings
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="tw-text-2xl tw-font-bold tw-text-blue-600">
              ${wallet?.totalEarnings?.toFixed(2) || '0.00'}
            </div>
            <p className="tw-text-xs tw-text-aims-text-secondary tw-mt-1">
              Lifetime earnings
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Stripe Connect Setup */}
      {(!stripeStatus?.hasAccount || stripeStatus?.status !== 'enabled') && (
        <StripeConnectSetup
          stripeStatus={stripeStatus}
          onSetupComplete={() => {
            refetchStripeStatus();
            refetchWallet();
          }}
        />
      )}

      {/* Recent Payout Requests */}
      {payoutRequests && payoutRequests.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="tw-text-lg">Recent Payout Requests</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="tw-space-y-3">
              {payoutRequests.slice(0, 5).map((request: any) => (
                <div key={request.id} className="tw-flex tw-items-center tw-justify-between tw-py-2 tw-border-b tw-border-aims-dark-3 last:tw-border-0">
                  <div className="tw-flex tw-items-center tw-gap-3">
                    <ArrowUpIcon className="tw-h-4 tw-w-4 tw-text-blue-600" />
                    <div>
                      <div className="tw-font-medium">${request.amount.toFixed(2)}</div>
                      <div className="tw-text-xs tw-text-aims-text-secondary">
                        {format(new Date(request.requestedAt), 'MMM dd, yyyy')}
                      </div>
                    </div>
                  </div>
                  {getPayoutStatusBadge(request.status)}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Transaction History */}
      <Card>
        <CardHeader>
          <CardTitle className="tw-text-lg">Transaction History</CardTitle>
        </CardHeader>
        <CardContent>
          {transactions.length === 0 ? (
            <div className="tw-text-center tw-py-8">
              <div className="tw-text-sm tw-text-aims-text-secondary">
                No transactions yet
              </div>
            </div>
          ) : (
            <div className="tw-space-y-3">
              {transactions.map((transaction: any) => (
                <div key={transaction.id} className="tw-flex tw-items-center tw-justify-between tw-py-3 tw-border-b tw-border-aims-dark-3 last:tw-border-0">
                  <div className="tw-flex tw-items-center tw-gap-3">
                    {getTransactionIcon(transaction.type)}
                    <div>
                      <div className="tw-font-medium tw-text-sm">
                        {transaction.description}
                      </div>
                      <div className="tw-text-xs tw-text-aims-text-secondary">
                        {format(new Date(transaction.createdAt), 'MMM dd, yyyy • h:mm a')}
                      </div>
                      {transaction.metadata?.contractNumber && (
                        <div className="tw-text-xs tw-text-aims-text-secondary">
                          Contract: {transaction.metadata.contractNumber}
                        </div>
                      )}
                    </div>
                  </div>
                  <div className={`tw-font-semibold ${getTransactionColor(transaction.type)}`}>
                    {transaction.amount >= 0 ? '+' : ''}${Math.abs(transaction.amount).toFixed(2)}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Payout Request Modal */}
      {showPayoutModal && (
        <PayoutRequestModal
          availableBalance={wallet?.availableBalance || 0}
          onClose={() => setShowPayoutModal(false)}
          onSuccess={handlePayoutSuccess}
        />
      )}
    </div>
  );
}
