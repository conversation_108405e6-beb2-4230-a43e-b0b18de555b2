import * as yup from "yup";
import { urlValidator } from "./shared";

export const brandProfileSchema = yup.object({
  companyName: yup.string().required("Company name is required"),
  industry: yup.string().required("Industry is required"),
  website: yup.string().test("is-url", "Must be a valid website", (value) => {
    if (!value) return true; // Allow empty values

    // Add https:// if no protocol specified
    const url = value.match(/^https?:\/\//) ? value : `https://${value}`;

    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }),
  location: yup.string().required("Location is required"),
  description: yup.string().required("Description is required"),
});

export const athleteProfileSchema = yup.object({
  name: yup.string().required("Name is required"),
  university: yup.string().required("College/University is required"),
  sport: yup.string().required("Sport is required"),
  yearInSchool: yup.string().required("Year in school is required"),
  position: yup.string(),
  birthDate: yup
    .date()
    .required("Birth date is required")
    .max(new Date(), "Birth date cannot be in the future")
    .test("age", "Must be at least 18 years old", function (value) {
      if (!value) return false;
      const age = new Date().getFullYear() - value.getFullYear();
      return age >= 18;
    }),
  gender: yup
    .string()
    .oneOf(["male", "female"], "Gender must be either male or female")
    .required("Gender is required"),
  bio: yup.string(),
  businessInterests: yup
    .array()
    .of(yup.string())
    .min(1, "At least one business interest is required")
    .required("Business interests are required"),
  socialMedia: yup.object({
    instagram: yup.string(),
    twitter: yup.string(),
    tiktok: yup.string(),
  }),
  minPayment: yup.object({
    shoot: yup
      .number()
      .min(0, "Minimum payment must be 0 or greater")
      .nullable(),
    inPerson: yup
      .number()
      .min(0, "Minimum payment must be 0 or greater")
      .nullable(),
    contentShare: yup
      .number()
      .min(0, "Minimum payment must be 0 or greater")
      .nullable(),
    contentCreation: yup
      .number()
      .min(0, "Minimum payment must be 0 or greater")
      .nullable(),
    giftedCollab: yup
      .number()
      .min(0, "Minimum payment must be 0 or greater")
      .nullable(),
    other: yup
      .number()
      .min(0, "Minimum payment must be 0 or greater")
      .nullable(),
  }),
  referralSource: yup.string(),
  referralName: yup.string().nullable(),
  referralVenmo: yup.string().nullable(),
  hometown: yup.string().nullable(),
});

const allowedImageTypes = ["image/jpeg", "image/png", "image/gif"];

export const uploadUrlSchema = yup.object({
  fileType: yup
    .string()
    .required("File type is required")
    .test({
      name: "valid-image-type",
      message: "Invalid file type. Only JPEG, PNG and GIF are allowed.",
      test: (value) => allowedImageTypes.includes(value),
    }),
});

export type UploadUrlResponse = {
  url: string;
  key: string;
  publicUrl: string;
};

export const profilePictureSchema = yup.object({
  key: yup.string().required("S3 key is required"),
  url: urlValidator.required("URL is required"),
});
