"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { FullPageLoadingSpinner } from "@/components/ui/LoadingSpinner";
import { InvitationBottomBar } from "@/components/ui/InvitationBottomBar";
import { useToast } from "@/components/ui/toast/use-toast";
import { trpc } from "@/lib/trpc/client";

import { getDeliverableTypeLabel} from "@repo/server/src/types/deliverable";
import { ChatType, MessageType } from "@repo/server/src/types/chat";
import Image from "next/image";


export default function PaymentPage() {
  const router = useRouter();
  const { toast } = useToast();
  const searchParams = useSearchParams();
  const [athleteIds, setAthleteIds] = useState<string[]>([]);
  const [campaignId, setCampaignId] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [deliverablePayments, setDeliverablePayments] = useState<
    Record<string, number | "">
  >({});

  useEffect(() => {
    const athletesParam = searchParams.get("athletes");
    const campaignParam = searchParams.get("campaign");
    if (!athletesParam || !campaignParam) {
      setError("Missing required parameters.");
      router.replace("/app/brand/athletes/invite");
      return;
    }
    try {
      const ids = JSON.parse(decodeURIComponent(athletesParam));
      setAthleteIds(ids);
      setCampaignId(campaignParam);
    } catch (e) {
      setError("Invalid athlete data in URL." + e);
      router.replace("/app/brand/athletes/invite");
    }
  }, [searchParams, router]);

  const { data: athletes, isLoading: loadingAthletes } =
    trpc.athlete.getAthletesByIds.useQuery(
      { athleteIds },
      { enabled: athleteIds.length > 0 },
    );
  const { data: campaign, isLoading: loadingCampaign } =
    trpc.campaign.getByCampaignId.useQuery(
      { campaignId: campaignId! },
      { enabled: !!campaignId },
    );

  const { data: brand } = trpc.brand.getProfile.useQuery();
  const inviteMutation = trpc.campaign.inviteAthletes.useMutation();
  const sendMessageMutation = trpc.chat.sendMessage.useMutation();
  const createChatMutation = trpc.chat.createChat.useMutation();

  const handleBack = () => {
    if (campaignId) {
      router.push(`/app/brand/athletes/invite?athletes=${encodeURIComponent(JSON.stringify(athleteIds))}`);
    } else {
      router.push("/app/brand/athletes/invite");
    }
  };

  const handleDeleteAthlete = (athleteIdToDelete: string) => {
    // Remove the athlete from the list
    const updatedAthleteIds = athleteIds.filter(id => id !== athleteIdToDelete);
    setAthleteIds(updatedAthleteIds);

    // Update the URL parameters to reflect the change
    if (updatedAthleteIds.length === 0) {
      // If no athletes left, redirect back to the athletes page
      router.replace("/app/brand/athletes/invite");
      return;
    }

    // Update the URL with the new athlete list
    const searchParams = new URLSearchParams();
    searchParams.set("athletes", encodeURIComponent(JSON.stringify(updatedAthleteIds)));
    if (campaignId) {
      searchParams.set("campaign", campaignId);
    }
    router.replace(`/app/brand/athletes/payment?${searchParams.toString()}`);
  };

  const handleSubmit = async () => {
    if (!campaignId || !brand || !campaign) return;

    try {
      setIsSubmitting(true);

      // First invite the athletes to the campaign with athlete-specific pricing
      await inviteMutation.mutateAsync({
        campaignId,
        athleteIds,
        deliverablePricing: deliverablePayments, // Pass the athlete-specific pricing
      });

      // Then send campaign invite messages to each athlete
      await Promise.all(
        athleteIds.map(async (athleteId) => {
          // Get the athlete to get their user ID
          const athlete = athletes?.find((a) => a._id === athleteId);
          if (!athlete?.userId) {
            console.error(`No user ID found for athlete ${athleteId}`);
            return;
          }

          // Create a direct chat between brand and athlete if it doesn't exist
          const chat = await createChatMutation.mutateAsync({
            participantIds: [athlete.userId._id], // Use the user ID instead of athlete ID
            type: ChatType.DIRECT,
          });

          // Send the campaign invite message with structured data
          const deliverableData = campaign.deliverables.map((d) => {
            const payment = deliverablePayments[d.id];
            const amount = payment === "" || payment === undefined ? d.minimumPayment : payment;
            return {
              ...d,
              minimumPayment: amount, // Override with the actual payment amount
            };
          });

          const messageContent = JSON.stringify({
            type: 'CAMPAIGN_INVITE_STRUCTURED',
            campaignName: campaign.name,
            campaignId: campaignId,
            deliverables: deliverableData,
            fallbackText: `You've been invited to participate in the campaign "${campaign.name}" with the following deliverables and payments:\n\n${deliverableData
              .map((d) => `- ${d.name}: $${d.minimumPayment}`)
              .join("\n")}`,
          });

          await sendMessageMutation.mutateAsync({
            chatId: chat.id,
            content: messageContent,
            type: MessageType.CAMPAIGN_INVITE,
            campaignId,
          });
        }),
      );

      toast({
        title: "Success",
        description: "Athletes have been invited and notified successfully!",
        variant: "success",
      });

      router.push("/app/brand/athletes");
    } catch (error) {
      console.error("Failed to invite athletes:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to invite athletes. Please try again.";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (error) return <div className="tw-text-red-500">{error}</div>;
  if (loadingAthletes || loadingCampaign || !athletes || !campaign)
    return <FullPageLoadingSpinner />;
  if (!athletes.length)
    return <div className="tw-text-red-500">No athletes found.</div>;

  // Initialize payment amounts for all deliverables
  if (
    Object.keys(deliverablePayments).length === 0 &&
    campaign.deliverables.length > 0
  ) {
    const initialPayments = campaign.deliverables.reduce(
      (acc, deliverable) => {
        acc[deliverable.id] = deliverable.minimumPayment;
        return acc;
      },
      {} as Record<string, number | "">,
    );
    setDeliverablePayments(initialPayments);
  }

  const plural = campaign.deliverables.length > 1 ? "s" : "";

  return (
    <div className="tw-p-4 tw-max-w-7xl tw-mx-auto tw-pb-32">
      <h1 className="tw-text-2xl tw-font-bold tw-mb-2 tw-text-center">
        Set Payment amount{plural} for deliverable{plural}
      </h1>
      <p className="tw-text-center tw-mb-4">
        Set the payment amount{plural} for each deliverable.
      </p>
      <div className="tw-flex tw-justify-between">
        {/* Left: Athletes */}
        <div className="tw-p-4 tw-pr-8 tw-w-1/3 tw-border-r tw-border-aims-dark-3">
          <h3 className="tw-font-bold tw-mb-4 tw-text-lg">
            {athletes.length} Participant{plural}
          </h3>
          <ul className="tw-space-y-4">
            {athletes.map((athlete, index: number) => (
              <li
                key={athlete._id || index}
                className="tw-flex tw-items-center tw-p-3 tw-border tw-border-aims-dark-3 tw-rounded-lg"
              >
                <Image
                  src={athlete.profilePicture?.url || "/no-profile-pic.jpg"}
                  alt={athlete.name || "Athlete"}
                  width={40}
                  height={40}
                  className="tw-w-10 tw-h-10 tw-rounded-full tw-mr-3"
                />
                <div className="tw-flex-1">
                  <div className="tw-font-semibold tw-text-aims-text-primary">
                    {athlete.name || athlete.userId?.name}
                  </div>
                  <div className="tw-text-xs tw-text-aims-text-secondary">
                    {athlete.hometown && `${athlete.hometown}`}
                  </div>
                </div>
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={() => handleDeleteAthlete(athlete._id)}
                >
                  Delete
                </Button>
              </li>
            ))}
          </ul>
        </div>
        {/* Right: Deliverables */}
        <div className="tw-p-4 tw-pl-8 tw-w-2/3">
          <h3 className="tw-font-bold tw-mb-4 tw-text-lg">
            {campaign.deliverables.length} Deliverable{plural}
          </h3>
          <div className="tw-grid tw-grid-cols-2 tw-gap-6">
            {campaign.deliverables.map((deliverable, index: number) => (
              <div
                key={deliverable.id || index}
                className="tw-bg-aims-dark-2 tw-rounded-lg tw-p-6 tw-flex tw-flex-col tw-gap-3"
              >
                <div className="tw-font-semibold tw-text-aims-text-primary tw-text-lg">
                  {deliverable.name}
                </div>
                <div className="tw-text-xs tw-text-aims-text-secondary">
                  Days: {deliverable.daysToComplete} | Type:{" "}
                  {getDeliverableTypeLabel(deliverable.type)}
                </div>
                <div className="tw-relative tw-mt-2">
                  <span className="tw-absolute tw-left-3 tw-top-1/2 tw-transform tw--translate-y-1/2 tw-text-aims-text-secondary tw-pointer-events-none">
                    $
                  </span>
                  <Input
                    type="number"
                    className="tw-bg-aims-dark-3 tw-rounded tw-px-3 tw-py-2 tw-pl-8 no-spinner"
                    placeholder="Set payment amount"
                    value={deliverablePayments[deliverable.id] ?? ""}
                    onChange={(e) => {
                      const value = e.target.value;
                      if (value === "") {
                        // Store empty string to maintain cleared state
                        setDeliverablePayments((prev) => ({
                          ...prev,
                          [deliverable.id]: "",
                        }));
                      } else {
                        // Parse the number, but handle invalid inputs gracefully
                        const numValue = parseFloat(value);
                        if (!isNaN(numValue) && numValue >= 0) {
                          setDeliverablePayments((prev) => ({
                            ...prev,
                            [deliverable.id]: numValue,
                          }));
                        }
                      }
                    }}
                  />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      <InvitationBottomBar
        onBack={handleBack}
        onNext={handleSubmit}
        backLabel="Back"
        nextLabel={isSubmitting ? "Sending Invites..." : `Send Invite${plural}`}
        isVisible={true}
        isLoading={isSubmitting}
      >
        <div className="tw-text-aims-text-primary tw-text-sm sm:tw-text-base">
          {athletes.length} athlete{plural} selected
          {campaign && (
            <span className="tw-block tw-text-aims-text-secondary tw-text-xs sm:tw-text-sm tw-mt-1">
              Campaign: {campaign.name}
            </span>
          )}
        </div>
      </InvitationBottomBar>
    </div>
  );
}
