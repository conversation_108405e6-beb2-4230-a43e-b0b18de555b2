import React from "react";

import { EmailButton } from "./EmailButton";
import { EmailTemplate } from "./EmailTemplate";

interface PasswordResetProps {
  rLink: string;
  email: string;
}

export const SendPasswordReset: React.FC<PasswordResetProps> = ({
  rLink,
  email,
}) => {
  return (
    <EmailTemplate email={email}>
      <div style={{ width: "auto", borderRadius: "10px" }}>
        <div style={{ width: "100%" }}>
          <h3>Password Reset</h3>
          <p style={{ color: "#64758b" }}>
            We received a request to reset the password for the AIMS account
            associated with:
          </p>
        </div>

        <div
          style={{
            width: "100%",
            textAlign: "center",
            borderRadius: "10px",
            borderWidth: "1px",
            borderStyle: "solid",
          }}
        >
          <p>
            <a rel="nofollow" style={{ color: "white" }}>{email}</a>
          </p>
        </div>

        <br />

        <EmailButton href={rLink}>Reset Password</EmailButton>

        <br />

        <div style={{ width: "100%" }}>
          <p style={{ color: "#64758b" }}>
            <strong>This link will expire within 24 hours.</strong>
          </p>
          <p style={{ color: "#64758b" }}>
            If you did not request to reset your password, you can safely
            disregard this email.
          </p>
        </div>
      </div>
    </EmailTemplate>
  );
};
