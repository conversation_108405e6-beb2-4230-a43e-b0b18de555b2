"use client";

import { ApplicationStatus } from "@repo/server/src/types/campaign";
import { User } from "@repo/server/src/models/user";
import { MessageInterface } from "@/types/chat";

import MessageItem from "./MessageItem";

interface MessageListProps {
  messages: MessageInterface[];
  user: User;
  lastMessageBySender: Map<string, MessageInterface>;
  isTyping: boolean;
  messagesEndRef: React.RefObject<HTMLDivElement>;
  updatingAppId: string | null;
  onAcceptApplication: (msg: MessageInterface) => Promise<void>;
  onRejectApplication: (msg: MessageInterface) => Promise<void>;
  onAcceptInvite?: (msg: MessageInterface) => Promise<void>;
  onRejectInvite?: (msg: MessageInterface) => Promise<void>;
  getAppStatus?: (msg: MessageInterface) => ApplicationStatus | undefined;
  getInviteStatus?: (msg: MessageInterface) => ApplicationStatus | undefined;
  unreadMessages?: MessageInterface[];
}

const MessageList = ({
  messages,
  user,
  lastMessageBySender,
  isTyping,
  messagesEndRef,
  updatingAppId,
  onAcceptApplication,
  onRejectApplication,
  onAcceptInvite,
  onRejectInvite,
  getAppStatus,
  getInviteStatus,
  unreadMessages = [],
}: MessageListProps) => {
  // Find the first unread message to show separator
  const firstUnreadIndex = messages?.findIndex(msg =>
    unreadMessages.some(unread => unread.id === msg.id)
  ) ?? -1;

  return (
    <>
      {messages?.map((msg, index) => {
        const isOwnMessage = msg.sender?.id === user?.id;
        const isLastMessageFromSender = Boolean(
          msg.sender?.id &&
          lastMessageBySender.get(msg.sender.id)?.id === msg.id
        );
        const appStatus: ApplicationStatus | undefined = getAppStatus?.(msg);
        const inviteStatus: ApplicationStatus | undefined = getInviteStatus?.(msg);

        // Show unread separator before first unread message
        const showUnreadSeparator = index === firstUnreadIndex && firstUnreadIndex > 0;

        return (
          <div key={msg.id}>
            {showUnreadSeparator && (
              <div className="tw-flex tw-items-center tw-my-4">
                <div className="tw-flex-1 tw-h-px tw-bg-aims-primary"></div>
                <span className="tw-px-3 tw-text-xs tw-text-aims-primary tw-bg-aims-dark-1 tw-font-medium">
                  New messages
                </span>
                <div className="tw-flex-1 tw-h-px tw-bg-aims-primary"></div>
              </div>
            )}
            <MessageItem
              msg={msg}
              isOwnMessage={isOwnMessage}
              isLastMessageFromSender={isLastMessageFromSender}
              appStatus={appStatus}
              user={user}
              updatingAppId={updatingAppId}
              onAcceptApplication={onAcceptApplication}
              onRejectApplication={onRejectApplication}
              onAcceptInvite={onAcceptInvite}
              onRejectInvite={onRejectInvite}
              inviteStatus={inviteStatus}
            />
          </div>
        );
      })}
      {isTyping && (
        <div className="tw-flex tw-items-center tw-gap-2">
          <div className="tw-w-8 tw-h-8 tw-rounded-full tw-bg-aims-dark-2 tw-flex tw-items-center tw-justify-center">
            <div className="tw-w-2 tw-h-2 tw-bg-aims-text-secondary tw-rounded-full tw-animate-bounce" />
          </div>
          <p className="tw-text-sm tw-text-aims-text-secondary">Typing...</p>
        </div>
      )}
      <div ref={messagesEndRef} />
    </>
  );
};

export default MessageList;
