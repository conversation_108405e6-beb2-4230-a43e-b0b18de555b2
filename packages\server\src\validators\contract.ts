import * as yup from "yup";

import { ContractStatus, ContractType } from "../types/contract";

// Contract generation schema
export const contractGenerationSchema = yup.object({
  campaignId: yup.string().required("Campaign ID is required"),
  applicationId: yup.string().required("Application ID is required"),
  customTerms: yup.object({
    additionalTerms: yup.array().of(yup.string()),
    cancellationPolicy: yup.string(),
    intellectualPropertyRights: yup.string(),
    confidentialityClause: yup.string(),
  }).optional(),
  paymentSchedule: yup.array().of(
    yup.object({
      description: yup.string().required("Payment description is required"),
      amount: yup.number().min(0, "Amount must be positive").required("Amount is required"),
      milestone: yup.string().optional(),
    })
  ).optional(),
  expirationDays: yup.number().min(1, "Expiration must be at least 1 day").max(365, "Expiration cannot exceed 365 days").optional(),
});

// Contract update schema
export const contractUpdateSchema = yup.object({
  contractId: yup.string().required("Contract ID is required"),
  title: yup.string().optional(),
  terms: yup.object({
    totalCompensation: yup.number().min(0, "Total compensation must be positive").optional(),
    paymentSchedule: yup.array().of(
      yup.object({
        description: yup.string().required("Payment description is required"),
        amount: yup.number().min(0, "Amount must be positive").required("Amount is required"),
        dueDate: yup.date().required("Due date is required"),
        milestone: yup.string().optional(),
      })
    ).optional(),
    deliverables: yup.array().of(
      yup.object({
        deliverableId: yup.string().required("Deliverable ID is required"),
        name: yup.string().required("Deliverable name is required"),
        description: yup.string().required("Deliverable description is required"),
        dueDate: yup.date().required("Due date is required"),
        compensation: yup.number().min(0, "Compensation must be positive").required("Compensation is required"),
        requirements: yup.array().of(yup.string().required()).required("Requirements are required"),
        type: yup.string().required("Deliverable type is required"),
      })
    ).optional(),
    campaignDuration: yup.object({
      startDate: yup.date().optional(),
      endDate: yup.date().optional(),
    }).optional().nullable(),
    additionalTerms: yup.array().of(yup.string().required()).optional(),
    cancellationPolicy: yup.string().optional(),
    intellectualPropertyRights: yup.string().optional(),
    confidentialityClause: yup.string().optional(),
  }).optional(),
  reason: yup.string().optional(),
});

// Contract status update schema
export const contractStatusUpdateSchema = yup.object({
  contractId: yup.string().required("Contract ID is required"),
  status: yup.string().oneOf(Object.values(ContractStatus), "Invalid contract status").required("Status is required"),
  reason: yup.string().optional(),
});

// Contract signature schema
export const contractSignatureSchema = yup.object({
  contractId: yup.string().required("Contract ID is required"),
  signatureData: yup.object({
    ipAddress: yup.string().required("IP address is required"),
    userAgent: yup.string().required("User agent is required"),
  }).required("Signature data is required"),
});

// Contract query schemas
export const getContractSchema = yup.object({
  contractId: yup.string().required("Contract ID is required"),
});

export const getContractsByCampaignSchema = yup.object({
  campaignId: yup.string().required("Campaign ID is required"),
});

export const getContractsByApplicationSchema = yup.object({
  applicationId: yup.string().required("Application ID is required"),
});

export const getBrandContractsSchema = yup.object({
  status: yup.string().oneOf(Object.values(ContractStatus), "Invalid contract status").optional(),
  search: yup.string().optional(),
  page: yup.number().min(1, "Page must be at least 1").optional(),
  limit: yup.number().min(1, "Limit must be at least 1").max(100, "Limit cannot exceed 100").optional(),
});

export const getAthleteContractsSchema = yup.object({
  status: yup.string().oneOf(Object.values(ContractStatus), "Invalid contract status").optional(),
  search: yup.string().optional(),
  page: yup.number().min(1, "Page must be at least 1").optional(),
  limit: yup.number().min(1, "Limit must be at least 1").max(100, "Limit cannot exceed 100").optional(),
});

// Contract PDF generation schema
export const generateContractPdfSchema = yup.object({
  contractId: yup.string().required("Contract ID is required"),
  regenerate: yup.boolean().optional(),
});

// Contract expiration extension schema
export const extendContractExpirationSchema = yup.object({
  contractId: yup.string().required("Contract ID is required"),
  additionalDays: yup.number().min(1, "Additional days must be at least 1").max(365, "Additional days cannot exceed 365").required("Additional days is required"),
  reason: yup.string().optional(),
});

// Contract cancellation schema
export const cancelContractSchema = yup.object({
  contractId: yup.string().required("Contract ID is required"),
  reason: yup.string().required("Cancellation reason is required"),
});

// Contract revision schema
export const createContractRevisionSchema = yup.object({
  contractId: yup.string().required("Contract ID is required"),
  changes: yup.array().of(yup.string().required("Change description is required")).min(1, "At least one change is required").required("Changes are required"),
  reason: yup.string().optional(),
  // Include the same fields as contractUpdateSchema for the actual updates
  title: yup.string().optional(),
  terms: yup.object({
    totalCompensation: yup.number().min(0, "Total compensation must be positive").optional(),
    paymentSchedule: yup.array().of(
      yup.object({
        description: yup.string().required("Payment description is required"),
        amount: yup.number().min(0, "Amount must be positive").required("Amount is required"),
        dueDate: yup.date().required("Due date is required"),
        milestone: yup.string().optional(),
      })
    ).optional(),
    deliverables: yup.array().of(
      yup.object({
        deliverableId: yup.string().required("Deliverable ID is required"),
        name: yup.string().required("Deliverable name is required"),
        description: yup.string().required("Deliverable description is required"),
        dueDate: yup.date().required("Due date is required"),
        compensation: yup.number().min(0, "Compensation must be positive").required("Compensation is required"),
        requirements: yup.array().of(yup.string().required()).required("Requirements are required"),
        type: yup.string().required("Deliverable type is required"),
      })
    ).optional(),
    campaignDuration: yup.object({
      startDate: yup.date().required("Start date is required"),
      endDate: yup.date().required("End date is required"),
    }).optional(),
    additionalTerms: yup.array().of(yup.string().required()).optional(),
    cancellationPolicy: yup.string().optional(),
    intellectualPropertyRights: yup.string().optional(),
    confidentialityClause: yup.string().optional(),
  }).optional(),
});
