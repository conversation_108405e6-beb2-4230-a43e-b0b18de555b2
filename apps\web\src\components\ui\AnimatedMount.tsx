interface AnimatedMountProps {
  show: boolean;
  children: React.ReactNode;
}

export function AnimatedMount({ show, children }: AnimatedMountProps) {
  return (
    <div
      className={`tw-grid tw-transition-all tw-duration-300 tw-ease-in-out ${
        show
          ? "tw-grid-rows-[1fr] tw-opacity-100 tw-transform-none"
          : "tw-grid-rows-[0fr] tw-opacity-0 tw-transform tw--translate-y-2"
      }`}
    >
      <div className="tw-overflow-hidden">{children}</div>
    </div>
  );
}
