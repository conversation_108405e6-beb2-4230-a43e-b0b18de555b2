import Image from "next/image";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { DeliverablesTable } from "@/components/ui/DeliverablesTable";
import { format } from "date-fns";
import { trpc } from "@/lib/trpc/client";
import { MessageInterface } from "@/types/chat";

import { ApplicationStatus } from "@repo/server/src/types/campaign";
import { MessageType } from "@repo/server/src/types/chat";
import { BaseDeliverable, DeliverableType, getDeliverableTypeLabel, GiftedCollaborationDeliverable } from "@repo/server/src/types/deliverable";
import { User } from "@repo/server/src/models/user";

interface MessageItemProps {
  msg: MessageInterface;
  isOwnMessage: boolean;
  isLastMessageFromSender: boolean;
  appStatus?: ApplicationStatus;
  user: User;
  updatingAppId: string | null;
  onAcceptApplication: (msg: MessageInterface) => Promise<void>;
  onRejectApplication: (msg: MessageInterface) => Promise<void>;
  onAcceptInvite?: (msg: MessageInterface) => Promise<void>;
  onRejectInvite?: (msg: MessageInterface) => Promise<void>;
  inviteStatus?: ApplicationStatus;
}

interface StructuredCampaignInvite {
  type: 'CAMPAIGN_INVITE_STRUCTURED';
  campaignName: string;
  campaignId: string;
  deliverables: BaseDeliverable[];
  fallbackText: string;
}

// Responsive Deliverables Component for Chat Messages
const ResponsiveDeliverablesTable = ({ deliverables }: { deliverables: BaseDeliverable[] }) => {
  return (
    <>
      {/* Desktop/Tablet View - Horizontal Scrollable Table */}
      <div className="tw-hidden md:tw-block tw-overflow-x-auto tw-max-w-full tw-scrollbar-thin tw-scrollbar-thumb-green-300 tw-scrollbar-track-green-100">
        <div className="tw-min-w-[600px] tw-pb-2">
          <DeliverablesTable deliverables={deliverables} className="tw-text-xs" />
        </div>
      </div>

      {/* Mobile/Small Tablet View - Stacked Cards */}
      <div className="tw-block md:tw-hidden tw-space-y-3 tw-p-3">
        {deliverables.map((deliverable, index) => (
          <div key={index} className="tw-bg-green-50 tw-border tw-border-green-200 tw-rounded-lg tw-p-3 tw-text-xs">
            <div className="tw-font-semibold tw-text-green-900 tw-mb-2 tw-text-sm">{deliverable.name}</div>

            <div className="tw-space-y-2">
              <div className="tw-flex tw-justify-between tw-items-center tw-min-h-[24px]">
                <span className="tw-text-green-700 tw-font-medium tw-text-xs">Type:</span>
                <span className="tw-text-green-800 tw-text-xs tw-text-right tw-flex-1 tw-ml-2">{getDeliverableTypeLabel(deliverable.type)}</span>
              </div>

              <div className="tw-flex tw-justify-between tw-items-center tw-min-h-[24px]">
                <span className="tw-text-green-700 tw-font-medium tw-text-xs">Payment:</span>
                <span className="tw-text-green-800 tw-font-semibold tw-text-xs tw-text-right tw-flex-1 tw-ml-2">
                  {deliverable.minimumPayment.toLocaleString("en-US", {
                    style: "currency",
                    currency: "USD",
                  })}
                </span>
              </div>

              <div className="tw-flex tw-justify-between tw-items-center tw-min-h-[24px]">
                <span className="tw-text-green-700 tw-font-medium tw-text-xs">Timeline:</span>
                <span className="tw-text-green-800 tw-text-xs tw-text-right tw-flex-1 tw-ml-2">{deliverable.daysToComplete} days</span>
              </div>

              {deliverable.description && (
                <div className="tw-mt-3 tw-pt-2 tw-border-t tw-border-green-200">
                  <span className="tw-text-green-700 tw-font-medium tw-block tw-mb-1 tw-text-xs">Description:</span>
                  <span className="tw-text-green-800 tw-text-xs tw-leading-relaxed tw-break-words">{deliverable.description}</span>
                </div>
              )}

              {deliverable.type === DeliverableType.GIFTED_COLLABORATION && (
                <div className="tw-mt-3 tw-pt-2 tw-border-t tw-border-green-200 tw-space-y-2">
                  <div className="tw-flex tw-justify-between tw-items-center tw-min-h-[24px]">
                    <span className="tw-text-green-700 tw-font-medium tw-text-xs">Product:</span>
                    <span className="tw-text-green-800 tw-text-xs tw-text-right tw-flex-1 tw-ml-2 tw-break-words">
                      {(deliverable as GiftedCollaborationDeliverable).productName}
                    </span>
                  </div>
                  {(deliverable as GiftedCollaborationDeliverable).productPrice && (
                    <div className="tw-flex tw-justify-between tw-items-center tw-min-h-[24px]">
                      <span className="tw-text-green-700 tw-font-medium tw-text-xs">Product Price:</span>
                      <span className="tw-text-green-800 tw-text-xs tw-text-right tw-flex-1 tw-ml-2">
                        {(deliverable as GiftedCollaborationDeliverable).productPrice?.toLocaleString("en-US", {
                          style: "currency",
                          currency: "USD",
                        })}
                      </span>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </>
  );
};

// Helper function to parse structured campaign invite content
const parseStructuredCampaignInvite = (content: string): StructuredCampaignInvite | null => {
  try {
    const parsed = JSON.parse(content);
    if (parsed.type === 'CAMPAIGN_INVITE_STRUCTURED') {
      return parsed as StructuredCampaignInvite;
    }
  } catch {
    // Silently handle parsing errors - this is expected for non-JSON content
    return null;
  }
  return null;
};

// Helper function for checking if content is structured (without logging errors)
const isStructuredCampaignInvite = (content: string): boolean => {
  try {
    const parsed = JSON.parse(content);
    return parsed.type === 'CAMPAIGN_INVITE_STRUCTURED';
  } catch {
    return false;
  }
};

// Component to display campaign name for accept/reject messages
const CampaignNameDisplay = ({ campaignId }: { campaignId: string }) => {
  const { data: campaign, isLoading } = trpc.campaign.getByCampaignId.useQuery(
    { campaignId },
    { enabled: !!campaignId }
  );

  if (isLoading) {
    return <span className="tw-text-xs tw-opacity-70">Loading campaign...</span>;
  }

  if (!campaign) {
    return null;
  }

  return (
    <div className="tw-mt-1">
      <span className="tw-text-xs tw-opacity-70">Campaign: </span>
      <Link
        href={`/app/campaign/${campaignId}`}
        className="tw-text-xs tw-underline hover:tw-no-underline"
      >
        {campaign.name}
      </Link>
    </div>
  );
};

// Component to display contract name for contract-related messages
const ContractNameDisplay = ({ contractId, userType }: { contractId: string; userType: string }) => {
  const { data: contract, isLoading } = trpc.contract.getById.useQuery(
    { contractId },
    { enabled: !!contractId }
  );

  if (isLoading) {
    return <span className="tw-text-xs tw-opacity-70">Loading contract...</span>;
  }

  if (!contract) {
    return null;
  }

  // Determine the correct URL based on user type
  const contractUrl = userType === "brand"
    ? `/app/brand/contracts/${contractId}`
    : `/app/athlete/contracts/${contractId}`;

  return (
    <div className="tw-mt-1">
      <span className="tw-text-xs tw-opacity-70">Contract: </span>
      <Link
        href={contractUrl}
        className="tw-text-xs tw-underline hover:tw-no-underline"
      >
        {contract.title} (#{contract.contractNumber})
      </Link>
    </div>
  );
};

const MessageItem = ({
  msg,
  isOwnMessage,
  isLastMessageFromSender,
  appStatus,
  user,
  updatingAppId,
  onAcceptApplication,
  onRejectApplication,
  onAcceptInvite,
  onRejectInvite,
  inviteStatus,
}: MessageItemProps) => {

  return (
    <div
      className={`tw-flex tw-items-end tw-gap-2 ${
        isOwnMessage ? "tw-justify-end" : "tw-justify-start"
      }`}
    >
      {!isOwnMessage && msg.sender && (
        <Link
          href={
            msg.sender.userType === "brand"
              ? `/app/brand/${msg.sender.brand?.id}`
              : `/app/athlete/${msg.sender.id}`
          }
        >
          <Image
            src={msg.sender.profilePicture?.url || "/no-profile-pic.jpg"}
            alt={msg.sender.name}
            className="tw-w-8 tw-h-8 tw-rounded-full tw-object-cover"
            width={100}
            height={100}
          />
        </Link>
      )}
      <div
        className={`${
          msg.type === MessageType.CAMPAIGN_INVITE && isStructuredCampaignInvite(msg.content)
            ? "tw-max-w-[90%]" // Wider for campaign invites with tables
            : "tw-max-w-[70%]"
        } tw-rounded-lg tw-p-3 tw-break-words tw-overflow-wrap-anywhere ${
          isOwnMessage
            ? "tw-bg-blue-600 tw-text-white"
            : "tw-bg-aims-dark-2 tw-text-aims-text-primary"
        } ${msg.isOptimistic ? "tw-opacity-70" : ""}`}
      >
        {!isOwnMessage && (
          <p className="tw-text-xs tw-font-medium tw-mb-1">
            {msg.sender?.name || "Unknown User"}
            {msg.sender?.brand?.name && (
              <span className="tw-text-aims-text-secondary">
                - {msg.sender.brand.name}
              </span>
            )}
          </p>
        )}
        {/* Render message content based on type */}
        {(() => {
          switch (msg.type) {
            case MessageType.TEXT:
              return <p className="tw-text-sm tw-whitespace-pre-wrap tw-break-words">{msg.content}</p>;
            case MessageType.CAMPAIGN_APPLICATION:
              return (
                <div className="tw-bg-yellow-100 tw-text-yellow-800 tw-p-2 tw-rounded">
                  <strong>Campaign Application:</strong> {msg.content}
                  {/* Status badge */}
                  {appStatus && appStatus !== ApplicationStatus.PENDING && (
                    <span
                      className={`tw-inline-block tw-ml-2 tw-px-2 tw-py-1 tw-rounded tw-text-xs tw-font-semibold ${
                        appStatus === ApplicationStatus.ACCEPTED
                          ? "tw-bg-green-200 tw-text-green-800"
                          : appStatus === ApplicationStatus.REJECTED
                            ? "tw-bg-red-200 tw-text-red-800"
                            : "tw-bg-gray-200 tw-text-gray-800"
                      }`}
                    >
                      {appStatus.charAt(0) + appStatus.slice(1).toLowerCase()}
                    </span>
                  )}
                  {/* Accept/Reject for brands */}
                  {user?.userType === "brand" &&
                    (!appStatus || appStatus === ApplicationStatus.PENDING) && (
                      <div className="tw-mt-2 tw-flex tw-gap-2">
                        <Button
                          size="sm"
                          variant="ghost"
                          className="tw-text-green-700 hover:tw-text-green-900"
                          disabled={updatingAppId !== null}
                          onClick={() => onAcceptApplication(msg)}
                        >
                          {updatingAppId ===
                          msg.campaignId + ":" + msg.sender?.id
                            ? "Accepting..."
                            : "Accept"}
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          className="tw-text-red-700 hover:tw-text-red-900"
                          disabled={updatingAppId !== null}
                          onClick={() => onRejectApplication(msg)}
                        >
                          {updatingAppId ===
                          msg.campaignId + ":" + msg.sender?.id
                            ? "Rejecting..."
                            : "Reject"}
                        </Button>
                      </div>
                    )}
                </div>
              );
            case MessageType.CAMPAIGN_INVITE:
              const structuredInvite = parseStructuredCampaignInvite(msg.content);

              if (structuredInvite) {
                // Enhanced structured campaign invite
                return (
                  <div className="tw-bg-green-50 tw-border tw-border-green-200 tw-rounded-lg tw-p-4 tw-max-w-none tw-overflow-hidden tw-w-full">
                    {/* Campaign Header */}
                    <div className="tw-mb-4">
                      <div className="tw-flex tw-items-center tw-gap-2 tw-mb-2">
                        <div className="tw-w-2 tw-h-2 tw-bg-green-500 tw-rounded-full"></div>
                        <span className="tw-text-green-800 tw-font-semibold tw-text-sm">Campaign Invitation</span>
                        {/* Status badge for invite response */}
                        {inviteStatus && inviteStatus !== ApplicationStatus.PENDING && (
                          <span
                            className={`tw-inline-block tw-px-2 tw-py-1 tw-rounded tw-text-xs tw-font-semibold ${
                              inviteStatus === ApplicationStatus.ACCEPTED
                                ? "tw-bg-green-200 tw-text-green-800"
                                : inviteStatus === ApplicationStatus.REJECTED
                                  ? "tw-bg-red-200 tw-text-red-800"
                                  : "tw-bg-gray-200 tw-text-gray-800"
                            }`}
                          >
                            {inviteStatus === ApplicationStatus.ACCEPTED
                              ? "Accepted"
                              : inviteStatus === ApplicationStatus.REJECTED
                                ? "Declined"
                                : inviteStatus.charAt(0) + inviteStatus.slice(1).toLowerCase()}
                          </span>
                        )}
                      </div>
                      <h3 className="tw-text-lg tw-font-bold tw-text-green-900 tw-mb-2">
                        {structuredInvite.campaignName}
                      </h3>
                      <Link
                        href={`/app/campaign/${structuredInvite.campaignId}`}
                        className="tw-text-green-700 hover:tw-text-green-900 tw-text-sm tw-underline tw-font-medium"
                      >
                        View full campaign details →
                      </Link>
                    </div>

                    {/* Deliverables Table */}
                    <div className="tw-mb-4">
                      <h4 className="tw-text-sm tw-font-semibold tw-text-green-800 tw-mb-3">
                        Deliverables & Payments
                      </h4>
                      <div className="tw-overflow-hidden tw-w-full tw-max-w-full">
                        <ResponsiveDeliverablesTable deliverables={structuredInvite.deliverables} />
                      </div>
                    </div>

                    {/* Accept/Reject for athletes receiving invites */}
                    {user?.userType === "athlete" &&
                     !isOwnMessage &&
                     onAcceptInvite &&
                     onRejectInvite &&
                     msg.campaignId &&
                     (inviteStatus === undefined || inviteStatus === ApplicationStatus.PENDING) && (
                      <div className="tw-flex tw-gap-2">
                        <Button
                          size="sm"
                          className="tw-bg-green-600 hover:tw-bg-green-700 tw-text-white"
                          disabled={updatingAppId !== null}
                          onClick={() => onAcceptInvite(msg)}
                        >
                          {updatingAppId === msg.campaignId + ":" + msg.id
                            ? "Accepting..."
                            : "Accept Invite"}
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          className="tw-border-red-300 tw-text-red-700 hover:tw-bg-red-50"
                          disabled={updatingAppId !== null}
                          onClick={() => onRejectInvite(msg)}
                        >
                          {updatingAppId === msg.campaignId + ":" + msg.id
                            ? "Rejecting..."
                            : "Decline"}
                        </Button>
                      </div>
                    )}
                  </div>
                );
              } else {
                // Fallback to original format for backward compatibility
                return (
                  <div className="tw-bg-green-100 tw-text-green-800 tw-p-2 tw-rounded">
                    <strong>Campaign Invite:</strong> {msg.content}
                    {/* Status badge for invite response */}
                    {inviteStatus && inviteStatus !== ApplicationStatus.PENDING && (
                      <span
                        className={`tw-inline-block tw-ml-2 tw-px-2 tw-py-1 tw-rounded tw-text-xs tw-font-semibold ${
                          inviteStatus === ApplicationStatus.ACCEPTED
                            ? "tw-bg-green-200 tw-text-green-800"
                            : inviteStatus === ApplicationStatus.REJECTED
                              ? "tw-bg-red-200 tw-text-red-800"
                              : "tw-bg-gray-200 tw-text-gray-800"
                        }`}
                      >
                        {inviteStatus === ApplicationStatus.ACCEPTED
                          ? "Accepted"
                          : inviteStatus === ApplicationStatus.REJECTED
                            ? "Declined"
                            : inviteStatus.charAt(0) + inviteStatus.slice(1).toLowerCase()}
                      </span>
                    )}
                    {/* Accept/Reject for athletes receiving invites */}
                    {user?.userType === "athlete" &&
                     !isOwnMessage &&
                     onAcceptInvite &&
                     onRejectInvite &&
                     msg.campaignId &&
                     (inviteStatus === undefined || inviteStatus === ApplicationStatus.PENDING) && (
                      <div className="tw-mt-2 tw-flex tw-gap-2">
                        <Button
                          size="sm"
                          variant="ghost"
                          className="tw-text-green-700 hover:tw-text-green-900"
                          disabled={updatingAppId !== null}
                          onClick={() => onAcceptInvite(msg)}
                        >
                          {updatingAppId === msg.campaignId + ":" + msg.id
                            ? "Accepting..."
                            : "Accept Invite"}
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          className="tw-text-red-700 hover:tw-text-red-900"
                          disabled={updatingAppId !== null}
                          onClick={() => onRejectInvite(msg)}
                        >
                          {updatingAppId === msg.campaignId + ":" + msg.id
                            ? "Rejecting..."
                            : "Reject Invite"}
                        </Button>
                      </div>
                    )}
                  </div>
                );
              }
            case MessageType.CAMPAIGN_ACCEPT:
              return (
                <div className="tw-bg-blue-100 tw-text-blue-800 tw-p-3 tw-rounded">
                  <div className="tw-flex tw-items-center tw-gap-2 tw-mb-1">
                    <div className="tw-w-2 tw-h-2 tw-bg-blue-500 tw-rounded-full"></div>
                    <strong>Invitation Accepted!</strong>
                  </div>
                  {msg.campaignId && <CampaignNameDisplay campaignId={msg.campaignId} />}
                  {user.userType === "brand" && msg.campaignId && (
                    <Link
                      className="tw-text-xs tw-font-medium tw-text-blue-700 hover:tw-text-blue-900 tw-underline tw-mt-2 tw-block"
                      href={`/app/campaign/${msg.campaignId}?brandView=true&tab=participant-management`}
                    >
                      Next: Generate Contract
                    </Link>
                  )}
                </div>
              );
            case MessageType.CAMPAIGN_REJECT:
              return (
                <div className="tw-bg-red-100 tw-text-red-800 tw-p-3 tw-rounded">
                  <div className="tw-flex tw-items-center tw-gap-2 tw-mb-1">
                    <div className="tw-w-2 tw-h-2 tw-bg-red-500 tw-rounded-full"></div>
                    <strong>Invitation Declined</strong>
                  </div>
                  <p className="tw-text-sm tw-mb-2">{msg.content}</p>
                  {msg.campaignId && <CampaignNameDisplay campaignId={msg.campaignId} />}
                </div>
              );
            case MessageType.APPLICATION_ACCEPTED:
              return (
                <div className="tw-bg-green-100 tw-text-green-800 tw-p-3 tw-rounded">
                  <div className="tw-flex tw-items-center tw-gap-2 tw-mb-1">
                    <div className="tw-w-2 tw-h-2 tw-bg-green-500 tw-rounded-full"></div>
                    <strong>Application Accepted!</strong>
                  </div>
                  {msg.campaignId && <CampaignNameDisplay campaignId={msg.campaignId} />}
                  {user.userType === "brand" && msg.campaignId && (
                    <Link
                      className="tw-text-xs tw-opacity-70 tw-underline tw-mt-2 tw-block"
                      href={`/app/campaign/${msg.campaignId}?brandView=true&tab=participant-management`}
                    >
                      Generate Contract
                    </Link>
                  )}
                </div>
              );
            case MessageType.APPLICATION_REJECTED:
              return (
                <div className="tw-bg-orange-100 tw-text-orange-800 tw-p-3 tw-rounded">
                  <div className="tw-flex tw-items-center tw-gap-2 tw-mb-1">
                    <div className="tw-w-2 tw-h-2 tw-bg-orange-500 tw-rounded-full"></div>
                    <strong>Application Not Selected</strong>
                  </div>
                  {msg.campaignId && <CampaignNameDisplay campaignId={msg.campaignId} />}
                </div>
              );
            case MessageType.CONTRACT_SENT:
              return (
                <div className="tw-bg-purple-100 tw-text-purple-800 tw-p-3 tw-rounded">
                  <div className="tw-flex tw-items-center tw-gap-2 tw-mb-1">
                    <div className="tw-w-2 tw-h-2 tw-bg-purple-500 tw-rounded-full"></div>
                    <strong>📄 Contract Sent</strong>
                  </div>
                  <p className="tw-text-sm tw-mb-2 tw-text-black">{msg.content}</p>
                  {msg.contractId && <ContractNameDisplay contractId={msg.contractId} userType={user.userType} />}
                </div>
              );
            case MessageType.CONTRACT_SIGNED:
              return (
                <div className="tw-bg-green-100 tw-text-green-800 tw-p-3 tw-rounded">
                  <div className="tw-flex tw-items-center tw-gap-2 tw-mb-1">
                    <div className="tw-w-2 tw-h-2 tw-bg-green-500 tw-rounded-full"></div>
                    <strong>✅ Contract Signed</strong>
                  </div>
                  <p className="tw-text-sm tw-mb-2 tw-text-black">{msg.content}</p>
                  {msg.contractId && <ContractNameDisplay contractId={msg.contractId} userType={user.userType} />}
                </div>
              );
            case MessageType.CONTRACT_REJECTED:
              return (
                <div className="tw-bg-red-100 tw-text-red-800 tw-p-3 tw-rounded">
                  <div className="tw-flex tw-items-center tw-gap-2 tw-mb-1">
                    <div className="tw-w-2 tw-h-2 tw-bg-red-500 tw-rounded-full"></div>
                    <strong>❌ Contract Rejected</strong>
                  </div>
                  <p className="tw-text-sm tw-mb-2 tw-text-black">{msg.content}</p>
                  {msg.contractId && <ContractNameDisplay contractId={msg.contractId} userType={user.userType} />}
                </div>
              );
            case MessageType.CONTRACT_UPDATED:
              return (
                <div className="tw-bg-blue-100 tw-text-blue-800 tw-p-3 tw-rounded">
                  <div className="tw-flex tw-items-center tw-gap-2 tw-mb-1">
                    <div className="tw-w-2 tw-h-2 tw-bg-blue-500 tw-rounded-full"></div>
                    <strong>🔄 Contract Updated</strong>
                  </div>
                  <p className="tw-text-sm tw-mb-2 tw-text-black">{msg.content}</p>
                  {msg.contractId && <ContractNameDisplay contractId={msg.contractId} userType={user.userType} />}
                </div>
              );
            case MessageType.CONTRACT_CANCELLED:
              return (
                <div className="tw-bg-gray-100 tw-text-gray-800 tw-p-3 tw-rounded">
                  <div className="tw-flex tw-items-center tw-gap-2 tw-mb-1">
                    <div className="tw-w-2 tw-h-2 tw-bg-gray-500 tw-rounded-full"></div>
                    <strong>🚫 Contract Cancelled</strong>
                  </div>
                  <p className="tw-text-sm tw-mb-2 tw-text-black">{msg.content}</p>
                  {msg.contractId && <ContractNameDisplay contractId={msg.contractId} userType={user.userType} />}
                </div>
              );
            // case MessageType.CONTRACT_PAYMENT_REQUIRED:
            //   return (
            //     <div className="tw-bg-orange-100 tw-text-orange-800 tw-p-3 tw-rounded">
            //       <div className="tw-flex tw-items-center tw-gap-2 tw-mb-1">
            //         <div className="tw-w-2 tw-h-2 tw-bg-orange-500 tw-rounded-full"></div>
            //         <strong>💳 Payment Required</strong>
            //       </div>
            //       <p className="tw-text-sm tw-mb-2 tw-text-black">{msg.content}</p>
            //       {msg.contractId && <ContractNameDisplay contractId={msg.contractId} userType={user.userType} />}
            //     </div>
            //   );
            case MessageType.CONTRACT_PAYMENT_COMPLETED:
              return (
                <div className="tw-bg-green-100 tw-text-green-800 tw-p-3 tw-rounded">
                  <div className="tw-flex tw-items-center tw-gap-2 tw-mb-1">
                    <div className="tw-w-2 tw-h-2 tw-bg-green-500 tw-rounded-full"></div>
                    <strong>✅ Payment Completed</strong>
                  </div>
                  <p className="tw-text-sm tw-mb-2 tw-text-black">{msg.content}</p>
                  {msg.contractId && <ContractNameDisplay contractId={msg.contractId} userType={user.userType} />}
                  <Link className="tw-text-xs tw-opacity-70 tw-underline" href={`/app/campaign/${msg.campaignId}?brandView=false`}>Submit Deliverables</Link>
                </div>
              );
            case MessageType.CONTRACT_PAYMENT_FAILED:
              return (
                <div className="tw-bg-red-100 tw-text-red-800 tw-p-3 tw-rounded">
                  <div className="tw-flex tw-items-center tw-gap-2 tw-mb-1">
                    <div className="tw-w-2 tw-h-2 tw-bg-red-500 tw-rounded-full"></div>
                    <strong>❌ Payment Failed</strong>
                  </div>
                  <p className="tw-text-sm tw-mb-2 tw-text-black">{msg.content}</p>
                  {msg.contractId && <ContractNameDisplay contractId={msg.contractId} userType={user.userType} />}
                </div>
              );
            case MessageType.CONTRACT_FULFILLED:
              return (
                <div className="tw-bg-emerald-100 tw-text-emerald-800 tw-p-3 tw-rounded">
                  <div className="tw-flex tw-items-center tw-gap-2 tw-mb-1">
                    <div className="tw-w-2 tw-h-2 tw-bg-emerald-500 tw-rounded-full"></div>
                    <strong>🎉 Contract Fulfilled</strong>
                  </div>
                  <p className="tw-text-sm tw-mb-2 tw-text-black">{msg.content}</p>
                  {msg.contractId && <ContractNameDisplay contractId={msg.contractId} userType={user.userType} />}
                </div>
              );
            case MessageType.CONTRACT_EXPIRED:
              return (
                <div className="tw-bg-yellow-100 tw-text-yellow-800 tw-p-3 tw-rounded">
                  <div className="tw-flex tw-items-center tw-gap-2 tw-mb-1">
                    <div className="tw-w-2 tw-h-2 tw-bg-yellow-500 tw-rounded-full"></div>
                    <strong>⏰ Contract Expired</strong>
                  </div>
                  <p className="tw-text-sm tw-mb-2">{msg.content}</p>
                  {msg.contractId && <ContractNameDisplay contractId={msg.contractId} userType={user.userType} />}
                </div>
              );
            case MessageType.DELIVERABLE_SUBMISSION:
              return (
                <div className="tw-bg-blue-100 tw-text-blue-800 tw-p-3 tw-rounded">
                  <div className="tw-flex tw-items-center tw-gap-2 tw-mb-1">
                    <div className="tw-w-2 tw-h-2 tw-bg-blue-500 tw-rounded-full"></div>
                    <strong>📤 Deliverable Submitted</strong>
                  </div>
                  <p className="tw-text-sm tw-mb-2 tw-text-black">{msg.content}</p>
                  {msg.campaignId && <CampaignNameDisplay campaignId={msg.campaignId} />}
                </div>
              );
            case MessageType.EARNINGS_CREDITED:
              return (
                <div className="tw-bg-green-100 tw-text-green-800 tw-p-3 tw-rounded">
                  <div className="tw-flex tw-items-center tw-gap-2 tw-mb-1">
                    <div className="tw-w-2 tw-h-2 tw-bg-green-500 tw-rounded-full"></div>
                    <strong>💰 Earnings Credited</strong>
                  </div>
                  <p className="tw-text-sm tw-text-black">{msg.content}</p>
                </div>
              );
            case MessageType.PAYOUT_COMPLETED:
              return (
                <div className="tw-bg-green-100 tw-text-green-800 tw-p-3 tw-rounded">
                  <div className="tw-flex tw-items-center tw-gap-2 tw-mb-1">
                    <div className="tw-w-2 tw-h-2 tw-bg-green-500 tw-rounded-full"></div>
                    <strong>✅ Payout Completed</strong>
                  </div>
                  <p className="tw-text-sm tw-text-black">{msg.content}</p>
                </div>
              );
            case MessageType.PAYOUT_FAILED:
              return (
                <div className="tw-bg-red-100 tw-text-red-800 tw-p-3 tw-rounded">
                  <div className="tw-flex tw-items-center tw-gap-2 tw-mb-1">
                    <div className="tw-w-2 tw-h-2 tw-bg-red-500 tw-rounded-full"></div>
                    <strong>❌ Payout Failed</strong>
                  </div>
                  <p className="tw-text-sm tw-text-black">{msg.content}</p>
                </div>
              );
            default:
              return <p className="tw-text-sm">{msg.content}</p>;
          }
        })()}
        <div className="tw-flex tw-justify-end tw-items-center tw-gap-2">
          <p className="tw-text-xs tw-opacity-70">
            {format(new Date(msg.createdAt), "MMM d, yyyy 'at' h:mm a")}
          </p>
          {msg.isOptimistic ? (
            <div className="tw-flex tw-items-center tw-gap-1">
              <div className="tw-w-2 tw-h-2 tw-bg-yellow-500 tw-rounded-full tw-animate-pulse" />
              <p className="tw-text-xs tw-opacity-70">Sending...</p>
            </div>
          ) : (
            isLastMessageFromSender && (
              <p className="tw-text-xs tw-opacity-70">
                {msg.readBy.length > 1 ? "Read" : "Delivered"}
              </p>
            )
          )}
        </div>
      </div>
    </div>
  );
};

export default MessageItem;
