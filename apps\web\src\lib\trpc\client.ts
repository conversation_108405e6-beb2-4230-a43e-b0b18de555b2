"use client";

import { createTR<PERSON>Client, httpLink } from "@trpc/client";
import type { AppRouter } from "@repo/server/src/router";
import { TokensStorage } from "../utils/tokens-storage";
import { createTRPCReact } from '@trpc/react-query';

// Environment configuration
export const getBaseUrl = () => {
  return process.env.NEXT_PUBLIC_SERVER_URL || "http://localhost:3000";
};

// Token management helper
const getTokens = () => {
  const tokens = TokensStorage.getTokens();
  return {
    access: tokens?.accessToken || "",
    refresh: tokens?.refreshToken || "",
  };
};

// Helper to refresh tokens
const refreshTokens = async (refreshToken: string) => {
  const res = await fetch(`${getBaseUrl()}/auth.refreshToken`, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ refreshToken }),
  });

  if (!res.ok) throw new Error("Failed to refresh token");

  const data = await res.json();
  return data.result.data.tokens; // { access, refresh }
};

// Create tRPC client with automatic token refresh
export const client = createTRPCClient<AppRouter>({
  links: [
    httpLink({
      url: `${getBaseUrl()}/trpc`,
      headers() {
        const tokens = getTokens();
        return {
          Authorization: `Bearer ${tokens.access}`,
        };
      },
      fetch: async (input, options) => {
        // Add timeout for long-running requests like PDF generation
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 300000); // 5 minutes

        const optionsWithTimeout = {
          ...options,
          signal: controller.signal,
        };
        const tokens = getTokens();
        let isRetryAttempted = false;

        const makeRequest = async (accessToken: string) => {
          try {
            return await fetch(input, {
              ...optionsWithTimeout,
              headers: {
                ...('headers' in optionsWithTimeout && optionsWithTimeout.headers ? optionsWithTimeout.headers as Record<string, string> : {}),
                Authorization: `Bearer ${accessToken}`,
              },
            });
          } catch (error) {
            clearTimeout(timeoutId);
            if (error instanceof Error && error.name === 'AbortError') {
              throw new Error('Request timeout: The operation took too long to complete. Please try again.');
            }
            throw error;
          }
        };

        let response;
        try {
          response = await makeRequest(tokens.access);
          clearTimeout(timeoutId);
        } catch (error) {
          clearTimeout(timeoutId);
          throw error;
        }

        if (response.status === 200) return response;

        // If not 200, check if we should try to refresh
        try {
          if (response.headers.get("content-type")?.includes("application/json")) {
            const errorData = await response.clone().json();
            if (errorData.error?.message.includes("Session Expired!") && !isRetryAttempted) {
              isRetryAttempted = true;
              const newTokens = await refreshTokens(tokens.refresh);

              TokensStorage.setTokens(newTokens.access, newTokens.refresh);

              // Retry original request with new access token
              try {
                const retryResponse = await makeRequest(newTokens.access);
                return retryResponse;
              } catch (retryError) {
                console.error("Failed to retry request after token refresh:", retryError);
                throw retryError;
              }
            }
          }
        } catch (err) {
          // Could not parse error or refresh failed
          console.error("Failed during token refresh:", err);
        }

        // If anything goes wrong, just return the original response
        return response;
      },
    }),
  ],
});

export const trpc = createTRPCReact<AppRouter>();
