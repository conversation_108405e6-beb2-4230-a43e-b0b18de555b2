export function FullPageLoadingSpinner() {
  return (
    <div className="tw-flex tw-justify-center tw-items-center tw-min-h-screen tw-bg-aims-bg">
      <div className="tw-flex tw-flex-col tw-items-center tw-gap-4">
        <div className="tw-animate-spin tw-h-8 tw-w-8 tw-border-4 tw-border-aims-primary tw-border-t-transparent tw-rounded-full"></div>
        <p className="tw-text-aims-text-secondary">Loading...</p>
      </div>
    </div>
  );
}

export function LoadingSpinner({ color = "aims-primary" }: { color?: string }) {
  const borderColorClass =
    color === "black" ? "tw-border-black" : `tw-border-${color}`;
  return (
    <div
      className={`tw-animate-spin tw-h-5 tw-w-5 tw-border-4 ${borderColorClass} tw-border-t-transparent tw-rounded-full`}
    ></div>
  );
}
