"use client";

import Link from "next/link";

export default function AuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="tw-flex tw-min-h-screen tw-flex-col md:tw-flex-row tw-p-4 md:tw-py-32 md:tw-px-16 lg:tw-px-32 xl:tw-px-64 md:tw-gap-8 lg:tw-gap-16 xl:tw-gap-32">
      {/* Left side - Branding and message */}
      <div className="tw-flex tw-flex-1 tw-flex-col tw-mb-8 md:tw-mb-0">
        <div className="tw-mb-6 md:tw-mb-8">
          <Link href="/">
            <h1 className="tw-text-aims-primary">AIMS</h1>
          </Link>
        </div>

        <h2 className="tw-mb-4 md:tw-mb-6 tw-text-xl md:tw-text-2xl">
          Where local businesses and college athletes team up for game-changing
          engagement!
        </h2>

        <p className="tw-text-base md:tw-text-lg tw-text-aims-text-secondary tw-max-w-2xl">
          We aim for you to have better ways of connecting you with Brands,
          Influencers, & athletes! Sign up to try now!
        </p>
      </div>

      {/* Right side - Auth content */}
      <div className="tw-bg-aims-dark-2 tw-p-6 md:tw-p-8 tw-rounded-xl tw-w-full md:tw-w-[480px] tw-h-fit">
        {children}
      </div>
    </div>
  );
}
