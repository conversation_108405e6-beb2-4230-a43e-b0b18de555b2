"use client";

import { trpc } from "@/lib/trpc/client";

import { Card, CardContent } from "../ui/card";
import { Button } from "../ui/button";
import { Badge } from "../ui/badge";

interface CampaignActionNotificationsProps {
  campaignId: string;
}

export function CampaignActionNotifications({
  campaignId,
}: CampaignActionNotificationsProps) {
  // Fetch contracts for this campaign that need attention
  const { data: contracts } = trpc.contract.getByCampaign.useQuery({
    campaignId,
  });

  if (!contracts || contracts.length === 0) {
    return null;
  }

  // Filter contracts that need brand attention
  const contractsNeedingReview = contracts.filter(
    c => c.status === "PENDING_BRAND_REVIEW"
  );
  const contractsNeedingApproval = contracts.filter(
    c => c.status === "PENDING_BRAND_APPROVAL"
  );
  const contractsExpiringSoon = contracts.filter(
    c => c.status === "PENDING_ATHLETE_SIGNATURE" && 
        c.expiresAt && 
        new Date(c.expiresAt) <= new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days
  );

  const totalActionsNeeded = contractsNeedingReview.length + 
                             contractsNeedingApproval.length + 
                             contractsExpiringSoon.length;

  if (totalActionsNeeded === 0) {
    return null;
  }

  return (
    <Card className="tw-border-blue-200 tw-bg-blue-50">
      <CardContent className="tw-pt-6">
        <div className="tw-flex tw-items-start tw-gap-4">
          <div className="tw-flex-shrink-0">
            <div className="tw-w-8 tw-h-8 tw-bg-blue-600 tw-text-white tw-rounded-full tw-flex tw-items-center tw-justify-center tw-text-sm tw-font-medium">
              !
            </div>
          </div>
          <div className="tw-flex-1">
            <h3 className="tw-font-medium tw-text-blue-900 tw-mb-2">
              Contract Actions Required
            </h3>
            <div className="tw-space-y-3">
              {contractsNeedingReview.length > 0 && (
                <div className="tw-flex tw-items-center tw-justify-between">
                  <div className="tw-flex tw-items-center tw-gap-2">
                    <Badge className="tw-bg-yellow-100 tw-text-yellow-800">
                      {contractsNeedingReview.length}
                    </Badge>
                    <span className="tw-text-sm tw-text-blue-800">
                      Contract{contractsNeedingReview.length > 1 ? 's' : ''} need{contractsNeedingReview.length > 1 ? '' : 's'} review
                    </span>
                  </div>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => window.open(`/app/brand/contracts?status=PENDING_BRAND_REVIEW`, '_blank')}
                  >
                    Review
                  </Button>
                </div>
              )}

              {contractsNeedingApproval.length > 0 && (
                <div className="tw-flex tw-items-center tw-justify-between">
                  <div className="tw-flex tw-items-center tw-gap-2">
                    <Badge className="tw-bg-blue-100 tw-text-blue-800">
                      {contractsNeedingApproval.length}
                    </Badge>
                    <span className="tw-text-sm tw-text-blue-800">
                      Contract{contractsNeedingApproval.length > 1 ? 's' : ''} need{contractsNeedingApproval.length > 1 ? '' : 's'} approval
                    </span>
                  </div>
                  <Button
                    size="sm"
                    onClick={() => window.open(`/app/brand/contracts?status=PENDING_BRAND_APPROVAL`, '_blank')}
                    className="tw-bg-green-600 hover:tw-bg-green-700"
                  >
                    Approve
                  </Button>
                </div>
              )}

              {contractsExpiringSoon.length > 0 && (
                <div className="tw-flex tw-items-center tw-justify-between">
                  <div className="tw-flex tw-items-center tw-gap-2">
                    <Badge className="tw-bg-orange-100 tw-text-orange-800">
                      {contractsExpiringSoon.length}
                    </Badge>
                    <span className="tw-text-sm tw-text-blue-800">
                      Contract{contractsExpiringSoon.length > 1 ? 's' : ''} expiring soon
                    </span>
                  </div>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => window.open(`/app/brand/contracts?status=PENDING_ATHLETE_SIGNATURE`, '_blank')}
                  >
                    View
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
