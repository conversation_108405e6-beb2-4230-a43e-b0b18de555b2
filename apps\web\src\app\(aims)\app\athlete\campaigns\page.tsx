"use client";

import { useCallback, useEffect, useState } from "react";
import CampaignCard from "@/components/ui/CampaignCard";
import { DeliverableFilter } from "@/components/ui/DeliverableFilter";
import { Input } from "@/components/ui/input";
import { FullPageLoadingSpinner } from "@/components/ui/LoadingSpinner";
import { PriceFilter } from "@/components/ui/PriceFilter";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useDebounce } from "@/hooks/use-debounce";
import { client } from "@/lib/trpc/client";
import { MagnifyingGlassIcon } from "@heroicons/react/24/outline";

import { SerializedCampaign, ApplicationStatus } from "@repo/server/src/types/campaign";

// Type for the actual serialized campaign application data returned by the API
interface AthleteApplication {
  id: string;
  campaignId: string;
  athleteId: string | {
    _id: string;
    userId: {
      _id: string;
      name: string;
    };
    profilePicture?: {
      url: string;
    };
  };
  status: ApplicationStatus;
  appliedAt: string;
  updatedAt: string;
  message?: string;
  compensation?: number;
  deliverables?: any[];
  initiatedBy: "brand" | "athlete";
}

const daysToCompleteOptions = [
  { label: "Any duration", value: "any" },
  { label: "1 day", value: "1" },
  { label: "2-6 days", value: "2-6" },
  { label: "7-13 days", value: "7-13" },
  { label: "2 weeks +", value: "14+" },
] as const;

const applicationStatusOptions = [
  { label: "All campaigns", value: "all" },
  { label: "Not applied", value: "not_applied" },
  { label: "Pending", value: ApplicationStatus.PENDING },
  { label: "Accepted", value: ApplicationStatus.ACCEPTED },
  { label: "Rejected", value: ApplicationStatus.REJECTED },
  // { label: "Withdrawn", value: ApplicationStatus.WITHDRAWN },
] as const;

export default function AthleteCampaignsPage() {
  const [campaigns, setCampaigns] = useState<SerializedCampaign[]>([]);
  const [loading, setLoading] = useState(true);
  const [applications, setApplications] = useState<AthleteApplication[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [daysFilter, setDaysFilter] =
    useState<(typeof daysToCompleteOptions)[number]["value"]>("any");
  const [applicationStatusFilter, setApplicationStatusFilter] =
    useState<(typeof applicationStatusOptions)[number]["value"]>("all");
  const [deliverablesFilter, setDeliverablesFilter] = useState<{
    min?: number;
    max?: number;
  }>();
  const [priceFilter, setPriceFilter] = useState<{
    min?: number;
    max?: number;
  }>();
  const debouncedSearch = useDebounce(searchTerm, 300);

  const getDaysToCompleteFilter = useCallback((value: typeof daysFilter) => {
    switch (value) {
      case "1":
        return { min: 0, max: 1 };
      case "2-6":
        return { min: 2, max: 6 };
      case "7-13":
        return { min: 7, max: 13 };
      case "14+":
        return { min: 14 };
      default:
        return undefined;
    }
  }, []);

  const fetchCampaigns = useCallback(async () => {
    try {
      setLoading(true);
      const data = await client.campaign.getActiveCampaigns.query({
        page: 1,
        limit: 10,
        filters: {
          ...(debouncedSearch ? { search: debouncedSearch } : {}),
          ...(daysFilter !== "any"
            ? { daysToComplete: getDaysToCompleteFilter(daysFilter) }
            : {}),
          ...(deliverablesFilter ? { deliverables: deliverablesFilter } : {}),
          ...(priceFilter
            ? { minPrice: priceFilter.min, maxPrice: priceFilter.max }
            : {}),
        },
      });
      // Filter out campaigns where the brand no longer exists
      const campaignResults = await Promise.all(
        data.campaigns.map(async (campaign) => {
          try {
            await client.brand.getBrand.query(campaign.brandId);
            return campaign;
          } catch (error) {
            console.warn(`Campaign ${campaign.id} has invalid brand ${campaign.brandId}`, error);
            return null;
          }
        }),
      );
      const validCampaigns = campaignResults.filter(
        (campaign): campaign is SerializedCampaign => campaign !== null,
      );
      setCampaigns(validCampaigns);
    } catch (e) {
      console.error("Failed to fetch campaigns:", e);
    } finally {
      setLoading(false);
    }
  }, [debouncedSearch, daysFilter, deliverablesFilter, priceFilter, getDaysToCompleteFilter]);

  const fetchApplications = useCallback(async () => {
    try {
      setLoading(true);
      const data = await client.campaign.getAthleteApplications.query();
      setApplications(data);
    } catch (e) {
      console.error("Failed to fetch applications:", e);
      // Add more detailed error logging
      if (e instanceof Error) {
        console.error("Error details:", {
          message: e.message,
          stack: e.stack,
        });
      }
    } finally {
      setLoading(false);
    }
  }, []);

  // Filter campaigns based on application status
  const filteredCampaigns = campaigns.filter((campaign) => {
    if (applicationStatusFilter === "all") {
      return true;
    }

    const application = applications.find((app) => app.campaignId === campaign.id);

    if (applicationStatusFilter === "not_applied") {
      return !application;
    }

    return application?.status === applicationStatusFilter;
  });

  useEffect(() => {
    fetchCampaigns();
  }, [fetchCampaigns]);

  useEffect(() => {
    fetchApplications();
  }, [fetchApplications]);

  return (
    <div className="tw-space-y-4 tw-p-4 sm:tw-p-6">
      {/* Search Bar - Full width on mobile */}
      <div className="tw-relative">
        <MagnifyingGlassIcon className="tw-absolute tw-left-3 tw-top-1/2 -tw-translate-y-1/2 tw-h-5 tw-w-5 tw-text-aims-text-secondary" />
        <Input
          type="text"
          placeholder="Search campaigns..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="tw-pl-10 tw-bg-aims-dark-2 tw-border-aims-dark-3 tw-text-aims-text-primary placeholder:tw-text-aims-text-secondary tw-h-12 sm:tw-h-10 tw-text-base"
        />
      </div>

      {/* Filters - Horizontal scroll on mobile, flex on desktop */}
      <div className="tw-flex tw-gap-3 tw-overflow-x-auto tw-pb-2 sm:tw-overflow-x-visible sm:tw-pb-0 sm:tw-gap-4 tw-px-1 sm:tw-px-0">
        <div className="tw-flex-shrink-0">
          <Select
            value={applicationStatusFilter}
            onValueChange={(value: typeof applicationStatusFilter) => setApplicationStatusFilter(value)}
          >
            <SelectTrigger className="tw-w-[140px] sm:tw-w-[160px] tw-bg-aims-dark-2 tw-border-aims-dark-3 tw-text-aims-text-primary tw-h-12 sm:tw-h-10">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              {applicationStatusOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="tw-flex-shrink-0">
          <Select
            value={daysFilter}
            onValueChange={(value: typeof daysFilter) => setDaysFilter(value)}
          >
            <SelectTrigger className="tw-w-[160px] sm:tw-w-[180px] tw-bg-aims-dark-2 tw-border-aims-dark-3 tw-text-aims-text-primary tw-h-12 sm:tw-h-10">
              <SelectValue placeholder="Duration" />
            </SelectTrigger>
            <SelectContent>
              {daysToCompleteOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="tw-flex-shrink-0">
          <DeliverableFilter onChange={setDeliverablesFilter} />
        </div>
        <div className="tw-flex-shrink-0">
          <PriceFilter onChange={setPriceFilter} />
        </div>
      </div>

      {loading ? (
        <FullPageLoadingSpinner />
      ) : filteredCampaigns.length === 0 ? (
        <div className="tw-flex tw-justify-center tw-items-center tw-min-h-[200px] tw-px-4">
          <div className="tw-text-aims-text-secondary tw-text-center tw-text-sm sm:tw-text-base">
            {searchTerm || daysFilter !== "any" || applicationStatusFilter !== "all"
              ? "No campaigns found matching your filters"
              : "No active campaigns available"}
          </div>
        </div>
      ) : (
        <div className="tw-grid tw-grid-cols-1 sm:tw-grid-cols-2 lg:tw-grid-cols-3 tw-gap-4 sm:tw-gap-6">
          {filteredCampaigns.map((campaign) => {
            const application = applications.find((app) => app.campaignId === campaign.id);
            return (
              <CampaignCard
                key={campaign.id}
                campaign={campaign}
                applicationStatus={application?.status}
                applicationInitiatedBy={application?.initiatedBy}
                refetchCampaigns={fetchCampaigns}
                refetchApplications={fetchApplications}
              />
            );
          })}
        </div>
      )}
    </div>
  );
}
