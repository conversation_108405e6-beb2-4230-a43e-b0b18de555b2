import "dotenv/config";
import "./db/index";

import { createServer } from "http";
import type { Application } from "express";
import * as trpcExpress from "@trpc/server/adapters/express";
import cors from "cors";
import express from "express";

import { initializeSocket } from "./lib/socket";
import { createContext } from "./lib/trpc";
import { appRouter } from "./router";
import stripeRouter from "./router/stripe";
import { initializeS3Bucket } from "./utils/s3";

const app: Application = express();
const httpServer = createServer(app);

// Configure timeouts for long-running requests (like PDF generation)
httpServer.timeout = 300000; // 5 minutes
httpServer.keepAliveTimeout = 65000; // 65 seconds
httpServer.headersTimeout = 66000; // 66 seconds

app.use(cors());
app.use(express.static("src/public"));
app.use("/stripe", stripeRouter);
app.use(
  trpcExpress.createExpressMiddleware({
    router: appRouter,
    createContext: createContext,
  }),
);
app.use(express.json());

app.use(function (req, res, next) {
  console.log(req.method, req.url);
  next();
} as express.RequestHandler);

// Initialize Socket.IO
initializeSocket(httpServer);

const PORT: number = Number(process.env.PORT) || 3000;

// Initialize S3 bucket configurations before starting the server
initializeS3Bucket()
  .then(() => {
    httpServer.listen(PORT, () => {
      console.log(`🚀 Server running on Port: ${PORT}`);
    });
  })
  .catch((error) => {
    console.error("Failed to initialize S3 bucket:", error);
    process.exit(1);
  });
