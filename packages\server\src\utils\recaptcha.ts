import { ExtendedTRPCError } from "./trpc";

export async function verifyCaptcha(token: string) {
    try {
        const response = await fetch('https://www.google.com/recaptcha/api/siteverify', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `secret=${process.env.RECAPTCHA_SECRET_KEY}&response=${token}`,
        });

        const data = await response.json();

        if (!data.success) {
            throw new ExtendedTRPCError("BAD_REQUEST", "CAPTCHA verification failed");
        }

        return data.success;
    } catch (error) {
        throw new ExtendedTRPCError("INTERNAL_SERVER_ERROR", "Failed to verify CAPTCHA");
    }
} 