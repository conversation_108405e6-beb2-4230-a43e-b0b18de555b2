import type { Request, Response } from "express";
import jwt from 'jsonwebtoken';
import UserModel, { User, SerializedAthleteProfile } from "../models/user";
import AthleteModel from "../models/athlete";
import BrandModel from "../models/brand";
import { SerializedBrandProfile } from "../types/brand";

import pkg from 'jsonwebtoken';
import { ExtendedTRPCError } from "../utils/trpc";
const { TokenExpiredError } = pkg;

declare global {
    namespace Express {
        interface Request {
            user: User & {
                athlete?: SerializedAthleteProfile;
                brand?: SerializedBrandProfile;
            }
        }
    }
}

export const isAuth = async (req: Request, res: Response) => {
    try {
        const authToken = req.headers.authorization;
        if (!authToken) throw new ExtendedTRPCError("UNAUTHORIZED", "Unauthorized Access! - missing token");

        if (!authToken.startsWith("Bearer ")) {
            throw new ExtendedTRPCError("UNAUTHORIZED", "Invalid token format");
        }

        const token = authToken.split("Bearer ")[1];
        if (!token) {
            throw new ExtendedTRPCError("UNAUTHORIZED", "Token not provided");
        }

        if (!process.env.JWT_SECRET) {
            throw new ExtendedTRPCError("INTERNAL_SERVER_ERROR", "JWT secret is not configured");
        }

        const payload = jwt.verify(token, process.env.JWT_SECRET!) as unknown as { id: string, exp: number };
        if (!payload.id) {
            throw new ExtendedTRPCError("UNAUTHORIZED", "Invalid token payload");
        }

        const user = await UserModel.findById(payload.id);
        if (!user) throw new ExtendedTRPCError("UNAUTHORIZED", "User not found");
        

        // Get athlete profile if user is an athlete
        let athleteProfile: SerializedAthleteProfile | undefined = undefined;
        if (user.userType === 'athlete') {
            const profile = await AthleteModel.findOne({ userId: user._id });
            if (profile) {
                const { userId, yearInSchool, birthDate, profilePicture, ...rest } = profile.toObject();
                athleteProfile = {
                    ...rest,
                    userId: userId.toString(),
                    yearInSchool: yearInSchool || null,
                    birthDate: birthDate?.toISOString() || null,
                    profilePicture: {
                        ...profilePicture,
                        uploadedAt: profilePicture?.uploadedAt?.toISOString() || null
                    }
                };
            }
        }

        // Get brand profile if user is a brand
        let brandProfile: SerializedBrandProfile | undefined = undefined;
        if (user.userType === 'brand') {
            const profile = await BrandModel.findOne({ userId: user._id });
            if (profile) {
                const { userId, logo, ...rest } = profile.toObject();
                brandProfile = {
                    ...rest,
                    _id: rest._id.toString(),
                    userId: userId.toString(),
                    logo: {
                        ...logo,
                        uploadedAt: logo?.uploadedAt?.toISOString() || null
                    }
                };
            }
        }

        req.user = {
            id: user._id.toString(),
            email: user.email,
            name: user.name,
            userType: user.userType,
            verified: user.verified,
            roles: user.roles,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
            athlete: athleteProfile,
            brand: brandProfile
        }

    } catch (error) {
        if (error instanceof TokenExpiredError) {
            throw new ExtendedTRPCError("UNAUTHORIZED", "Session Expired!");
        }
        throw new ExtendedTRPCError("UNAUTHORIZED", "Unauthorized Access!");
    }
}