"use client";

import { useEffect, useState } from "react";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { LoadingSpinner } from "@/components/ui/LoadingSpinner";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/toast/use-toast";
import { useAuth } from "@/hooks/use-auth";
import { client } from "@/lib/trpc/client";
import { PlusIcon } from "@heroicons/react/24/outline";

import type { SerializedBrandProfile } from "@repo/server/src/types/brand";
import { useRouter } from "next/navigation";

interface BrandProfile {
  companyName: string;
  industry: string;
  website?: string;
  location: string;
  description: string;
  logo?: {
    url: string | null;
    key: string | null;
    uploadedAt: string | null;
  };
}

export default function Profile() {
  const { getProfile, user } = useAuth();
  const router = useRouter();
  const { toast } = useToast();
  const [profile, setProfile] = useState<BrandProfile>({
    companyName: "",
    industry: "",
    website: "",
    location: "",
    description: "",
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState("");

  useEffect(() => {
    const fetchProfile = async () => {
      try {
        const data =
          (await client.brand.getProfile.query()) as SerializedBrandProfile;
        setProfile({
          companyName: data.companyName || "",
          industry: data.industry || "",
          location: data.location || "",
          description: data.description || "",
          logo: {
            url: data.logo?.url || null,
            key: data.logo?.key || null,
            uploadedAt: data.logo?.uploadedAt || null,
          },
          website: data.website || "",
        });
      } catch (error) {
        console.error("Failed to fetch profile:", error);
        setError("Failed to load profile data. Please refresh the page.");
      }
    };

    fetchProfile();
  }, []);

  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validate file size (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
      setUploadError("File size must be less than 10MB");
      return;
    }

    try {
      setIsUploading(true);
      setUploadError("");

      // Get presigned URL
      const { url, key, publicUrl } = await client.brand.getUploadUrl.mutate({
        fileType: file.type,
      });

      // Upload to S3
      const uploadResponse = await fetch(url, {
        method: "PUT",
        body: file,
        headers: {
          "Content-Type": file.type,
        },
      });

      if (!uploadResponse.ok) throw new Error("Failed to upload image");

      // Update profile with new image
      await client.brand.updateLogo.mutate({
        key,
        url: publicUrl,
      });

      // Update local state with current timestamp
      const now = new Date().toISOString();
      setProfile((prev) => ({
        ...prev,
        logo: {
          url: publicUrl,
          key,
          uploadedAt: now,
        },
      }));

      // Update global auth state
      await getProfile();
    } catch (error) {
      setUploadError(
        error instanceof Error ? error.message : "Failed to upload image",
      );
    } finally {
      setIsUploading(false);
    }
  };

  const validateForm = () => {
    if (!profile.companyName) {
      toast({
        title: "Validation Error",
        description: "Company name is required",
        variant: "destructive",
      });
      return false;
    }
    if (!profile.industry) {
      toast({
        title: "Validation Error",
        description: "Industry is required",
        variant: "destructive",
      });
      return false;
    }
    if (!profile.description) {
      toast({
        title: "Validation Error",
        description: "Description is required",
        variant: "destructive",
      });
      return false;
    }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    if (!validateForm()) {
      setLoading(false);
      return;
    }

    try {
      await client.brand.updateBrandProfile.mutate({
        companyName: profile.companyName,
        industry: profile.industry,
        website: profile.website,
        location: profile.location,
        description: profile.description,
      });
      toast({
        title: "Success",
        description: "Profile updated successfully!",
        variant: "success",
      });
      setTimeout(() => {
        getProfile(true);
      }, 500); // 1 second, adjust as needed
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update profile. Please try again.",
        variant: "destructive",
      });
      console.error("Failed to update profile:", error);
    } finally {
      setLoading(false);
      router.push(`/app/brand/${user?.brand?._id}`);
    }
  };

  return (
    <div className="tw-p-8">
      <div className="tw-mx-auto tw-max-w-4xl">
        <h1 className="tw-mb-8 tw-text-2xl tw-font-bold">
          Brand Profile Settings
        </h1>

        {error && (
          <div className="tw-mb-4 tw-rounded-md tw-bg-red-50 tw-p-4 tw-text-red-700">
            {error}
          </div>
        )}

        <div className="tw-grid tw-grid-cols-2 tw-gap-6">
          {/* Left Column - Form Fields */}
          <div className="tw-space-y-6">
            <form onSubmit={handleSubmit}>
              <div className="tw-space-y-6">
                <div className="tw-grid tw-grid-cols-2 tw-gap-4">
                  <div className="tw-space-y-2">
                    <Label required>Company Name</Label>
                    <Input
                      type="text"
                      value={profile.companyName}
                      onChange={(e) =>
                        setProfile((prev) => ({
                          ...prev,
                          companyName: e.target.value,
                        }))
                      }
                    />
                  </div>

                  <div className="tw-space-y-2">
                    <Label required>Industry</Label>
                    <Input
                      type="text"
                      value={profile.industry}
                      onChange={(e) =>
                        setProfile((prev) => ({
                          ...prev,
                          industry: e.target.value,
                        }))
                      }
                    />
                  </div>

                  <div className="tw-space-y-2">
                    <Label>Website</Label>
                    <Input
                      type="url"
                      value={profile.website || ""}
                      onChange={(e) =>
                        setProfile((prev) => ({
                          ...prev,
                          website: e.target.value,
                        }))
                      }
                      placeholder="https://www.example.com"
                    />
                  </div>

                  <div className="tw-space-y-2">
                    <Label>Location</Label>
                    <Input
                      type="text"
                      value={profile.location}
                      onChange={(e) =>
                        setProfile((prev) => ({
                          ...prev,
                          location: e.target.value,
                        }))
                      }
                    />
                  </div>
                </div>

                <div className="tw-space-y-2">
                  <Label required>Description</Label>
                  <Textarea
                    value={profile.description}
                    onChange={(e) =>
                      setProfile((prev) => ({
                        ...prev,
                        description: e.target.value,
                      }))
                    }
                    rows={4}
                  />
                </div>

                <div className="tw-pt-4">
                  <Button type="submit" disabled={loading}>
                    {loading ? "Saving..." : "Save Changes"}
                  </Button>
                </div>
              </div>
            </form>
          </div>

          {/* Right Column - Logo Upload */}
          <div className="tw-flex tw-justify-center">
            <div
              className="tw-cursor-pointer tw-h-[300px] tw-w-[300px] tw-bg-aims-dark-3 tw-rounded-full tw-flex tw-flex-col tw-items-center tw-justify-center tw-border-2 tw-border-dashed tw-border-gray-600 tw-relative tw-overflow-hidden"
              onClick={() =>
                !isUploading && document.getElementById("logo-upload")?.click()
              }
            >
              {isUploading ? (
                <div className="tw-flex tw-items-center tw-justify-center tw-w-full tw-h-full">
                  <LoadingSpinner />
                </div>
              ) : profile.logo?.url ? (
                <div className="tw-w-full tw-h-full tw-relative">
                  <Image
                    src={profile.logo.url}
                    alt="Company Logo"
                    fill
                    sizes="300px"
                    className="tw-object-cover tw-rounded-full"
                    priority
                  />
                  <div className="tw-absolute tw-inset-0 tw-bg-black tw-bg-opacity-50 tw-flex tw-items-center tw-justify-center tw-opacity-0 hover:tw-opacity-100 tw-transition-opacity tw-rounded-full">
                    <p className="tw-text-aims-text-primary tw-text-sm">
                      Change logo
                    </p>
                  </div>
                </div>
              ) : (
                <div className="tw-text-center tw-p-6">
                  <PlusIcon className="tw-mx-auto tw-h-12 tw-w-12 tw-text-gray-400" />
                  <p className="tw-mt-2 tw-text-sm tw-text-gray-400">
                    Click to upload
                  </p>
                  <p className="tw-text-xs tw-text-gray-500">
                    PNG, JPG, GIF up to 10MB
                  </p>
                </div>
              )}
              <input
                id="logo-upload"
                type="file"
                className="tw-hidden"
                accept="image/jpeg,image/png,image/gif"
                onChange={handleFileUpload}
                disabled={isUploading}
              />
            </div>
          </div>
        </div>

        {uploadError && (
          <p className="tw-mt-2 tw-text-red-500 tw-text-sm tw-text-center">
            {uploadError}
          </p>
        )}
      </div>
    </div>
  );
}
