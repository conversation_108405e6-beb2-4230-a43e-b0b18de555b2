import { Server as HttpServer } from "http";
import { Types } from "mongoose";
import { Server } from "socket.io";

import AthleteModel from "../models/athlete";
import BrandModel from "../models/brand";
import ChatModel from "../models/chat";
import MessageModel from "../models/message";
import { verifyToken } from "./jwt";

interface SocketUser {
  userId: string;
  socketId: string;
}

interface PopulatedParticipant {
  _id: Types.ObjectId;
  name: string;
  userType: string;
  athlete?: {
    profilePicture?: {
      url: string;
      key: string;
      uploadedAt: Date;
    };
  };
  brand?: {
    logo?: {
      url: string;
      key: string;
      uploadedAt: Date;
    };
  };
}

export class SocketManager {
  private io: Server;
  private users: SocketUser[] = [];

  constructor(server: HttpServer) {
    this.io = new Server(server, {
      cors: {
        origin: process.env.CLIENT_URL,
        methods: ["GET", "POST"],
        credentials: true,
      },
      pingTimeout: 60000,
      pingInterval: 25000,
      connectTimeout: 45000,
      allowEIO3: true,
      transports: ["websocket"],
    });

    this.io.use(async (socket, next) => {
      try {
        const token = socket.handshake.auth.token;
        console.log("Socket auth attempt - token present:", !!token);
        console.log("Socket auth attempt - token preview:", token ? `${token.substring(0, 20)}...` : 'none');

        if (!token) {
          console.log("Socket auth failed - no token provided");
          return next(new Error("Authentication error: No token provided"));
        }

        const decoded = await verifyToken(token);
        console.log("Socket auth successful for user:", decoded.id);
        socket.data.userId = decoded.id;
        next();
      } catch (error) {
        console.error("Socket auth error details:", error);
        if (error instanceof Error) {
          console.error("Socket auth error message:", error.message);
          return next(new Error(`Authentication error: ${error.message}`));
        }
        next(new Error("Authentication error: Unknown error"));
      }
    });

    this.io.on("connection", this.handleConnection.bind(this));
  }

  private handleConnection(socket: any) {
    console.log("User connected:", socket.data.userId);
    this.users.push({ userId: socket.data.userId, socketId: socket.id });

    // Join user's chats
    this.joinUserChats(socket);

    socket.on("disconnect", () => {
      console.log("User disconnected:", socket.data.userId);
      this.users = this.users.filter((user) => user.socketId !== socket.id);
    });

    socket.on("joinChat", (chatId: string) => {
      socket.join(chatId);
    });

    socket.on("leaveChat", (chatId: string) => {
      socket.leave(chatId);
    });

    socket.on(
      "sendMessage",
      async (data: { chatId: string; content: string; type: string }) => {
        try {
          const chat = await ChatModel.findById(
            new Types.ObjectId(data.chatId),
          );
          if (!chat) return;

          const message = await MessageModel.create({
            chatId: chat._id,
            senderId: new Types.ObjectId(socket.data.userId),
            content: data.content,
            type: data.type,
            readBy: [new Types.ObjectId(socket.data.userId)],
          });

          // Update chat's last message
          chat.lastMessage = message._id;
          await chat.save();

          // Populate the message with sender information
          const populatedMessage = await MessageModel.findById(message._id)
            .populate<{
              senderId: PopulatedParticipant;
            }>("senderId", "name userType")
            .lean();

          if (!populatedMessage) return;

          // Fetch sender profile information
          const senderId = populatedMessage.senderId._id.toString();
          const [athleteProfile, brandProfile] = await Promise.all([
            AthleteModel.findOne({
              userId: new Types.ObjectId(senderId),
            }).lean(),
            BrandModel.findOne({ userId: new Types.ObjectId(senderId) }).lean(),
          ]);

          // Use athlete's profile name if available, otherwise fall back to user name
          const displayName =
            populatedMessage.senderId.userType === "athlete" &&
            athleteProfile?.name
              ? athleteProfile.name
              : populatedMessage.senderId.name;

          const profilePicture =
            populatedMessage.senderId.userType === "athlete"
              ? athleteProfile?.profilePicture
              : brandProfile?.logo;

          // Emit to all participants in the chat with properly formatted message
          this.io.to(data.chatId).emit("newMessage", {
            id: message._id.toString(),
            chatId: data.chatId,
            content: data.content,
            type: data.type,
            campaignId: message.campaignId?.toString(),
            sender: {
              id: senderId,
              name: displayName,
              userType: populatedMessage.senderId.userType,
              profilePicture: profilePicture || null,
              brand: brandProfile
                ? {
                    id: brandProfile._id.toString(),
                    name: brandProfile.companyName,
                  }
                : null,
            },
            readBy: message.readBy.map((id) => id.toString()),
            createdAt: message.createdAt.toISOString(),
            updatedAt: message.updatedAt.toISOString(),
          });
        } catch (error) {
          console.error("Error sending message:", error);
        }
      },
    );

    socket.on("typing", (data: { chatId: string; isTyping: boolean }) => {
      socket.to(data.chatId).emit("userTyping", {
        chatId: data.chatId,
        userId: socket.data.userId,
        isTyping: data.isTyping,
      });
    });

    socket.on("markAsRead", async (data: { chatId: string }) => {
      try {
        await MessageModel.updateMany(
          {
            chatId: new Types.ObjectId(data.chatId),
            readBy: { $ne: new Types.ObjectId(socket.data.userId) },
          },
          {
            $addToSet: { readBy: new Types.ObjectId(socket.data.userId) },
          },
        );

        this.io.to(data.chatId).emit("messagesRead", {
          chatId: data.chatId,
          userId: socket.data.userId,
        });
      } catch (error) {
        console.error("Error marking messages as read:", error);
      }
    });
  }

  private async joinUserChats(socket: any) {
    try {
      const chats = await ChatModel.find({
        participants: new Types.ObjectId(socket.data.userId),
      });

      chats.forEach((chat) => {
        socket.join(chat._id.toString());
      });
    } catch (error) {
      console.error("Error joining user chats:", error);
    }
  }

  // Method to emit events to a specific user
  public emitToUser(userId: string, event: string, data: any) {
    const userSockets = this.users.filter(user => user.userId === userId);
    userSockets.forEach(user => {
      this.io.to(user.socketId).emit(event, data);
    });
    console.log(`Emitted ${event} to user ${userId} (${userSockets.length} sockets)`);
  }
}

let socketManagerInstance: SocketManager | null = null;

export function initializeSocket(server: HttpServer) {
  socketManagerInstance = new SocketManager(server);
  return socketManagerInstance;
}

export function getSocketManager(): SocketManager | null {
  return socketManagerInstance;
}

export function getIO(): Server | null {
  return socketManagerInstance ? (socketManagerInstance as any).io : null;
}
