"use client";

import { ContractStatus, PaymentStatus } from "@repo/server/src/types/contract";
import { Badge } from "../ui/badge";

interface PaymentStatusBadgeProps {
  contractStatus: ContractStatus;
  paymentStatus?: PaymentStatus;
  className?: string;
}

export function PaymentStatusBadge({ 
  contractStatus, 
  paymentStatus, 
  className 
}: PaymentStatusBadgeProps) {
  const getPaymentStatusInfo = () => {
    switch (contractStatus) {
      case ContractStatus.PENDING_PAYMENT:
        return {
          label: "Payment Required",
          variant: "destructive" as const,
          icon: "💳",
        };
      case ContractStatus.PAID:
        return {
          label: "Paid",
          variant: "default" as const,
          icon: "✅",
        };
      case ContractStatus.AWAITING_DELIVERABLES:
        return {
          label: "Working on Deliverables",
          variant: "default" as const,
          icon: "📋",
        };
      case ContractStatus.AWAITING_BRAND_APPROVAL:
        return {
          label: "Awaiting Brand Review",
          variant: "default" as const,
          icon: "👀",
        };
      case ContractStatus.FULFILLED:
        return {
          label: "Completed",
          variant: "secondary" as const,
          icon: "🎉",
        };
      default:
        // For other statuses, show based on payment status if available
        if (paymentStatus) {
          switch (paymentStatus) {
            case PaymentStatus.PENDING:
              return {
                label: "Payment Pending",
                variant: "outline" as const,
                icon: "⏳",
              };
            case PaymentStatus.PROCESSING:
              return {
                label: "Processing Payment",
                variant: "outline" as const,
                icon: "🔄",
              };
            case PaymentStatus.SUCCEEDED:
              return {
                label: "Payment Successful",
                variant: "default" as const,
                icon: "✅",
              };
            case PaymentStatus.FAILED:
              return {
                label: "Payment Failed",
                variant: "destructive" as const,
                icon: "❌",
              };
            case PaymentStatus.REQUIRES_ACTION:
              return {
                label: "Action Required",
                variant: "destructive" as const,
                icon: "⚠️",
              };
            default:
              return null;
          }
        }
        return null;
    }
  };

  const statusInfo = getPaymentStatusInfo();

  if (!statusInfo) {
    return null;
  }

  return (
    <Badge variant={statusInfo.variant} className={className}>
      <span className="tw-mr-1">{statusInfo.icon}</span>
      {statusInfo.label}
    </Badge>
  );
}

interface PaymentActionIndicatorProps {
  contractStatus: ContractStatus;
  paymentStatus?: PaymentStatus;
  userType: "brand" | "athlete";
  className?: string;
}

export function PaymentActionIndicator({ 
  contractStatus, 
  paymentStatus, 
  userType,
  className 
}: PaymentActionIndicatorProps) {
  const getActionInfo = () => {
    // Only brands can make payments
    if (userType !== "brand") {
      return null;
    }

    switch (contractStatus) {
      case ContractStatus.PENDING_PAYMENT:
        if (paymentStatus === PaymentStatus.FAILED) {
          return {
            message: "Payment failed - retry required",
            type: "error" as const,
            icon: "❌",
          };
        }
        return {
          message: "Payment in progress",
          type: "info" as const,
          icon: "⏳",
        };
      case ContractStatus.PAID:
        return {
          message: "Payment completed - contract active",
          type: "success" as const,
          icon: "✅",
        };
      case ContractStatus.AWAITING_DELIVERABLES:
        return {
          message: "Payment completed - athlete working on deliverables",
          type: "success" as const,
          icon: "📋",
        };
      case ContractStatus.AWAITING_BRAND_APPROVAL:
        return {
          message: "All deliverables submitted - awaiting brand review",
          type: "info" as const,
          icon: "👀",
        };
      default:
        return null;
    }
  };

  const actionInfo = getActionInfo();

  if (!actionInfo) {
    return null;
  }

  const bgColor = {
    action: "tw-bg-blue-50 tw-text-blue-700 tw-border-blue-200",
    error: "tw-bg-red-50 tw-text-red-700 tw-border-red-200",
    info: "tw-bg-yellow-50 tw-text-yellow-700 tw-border-yellow-200",
    success: "tw-bg-green-50 tw-text-green-700 tw-border-green-200",
  }[actionInfo.type];

  return (
    <div className={`tw-px-4 tw-py-2 tw-rounded-lg tw-border ${bgColor} ${className}`}>
      <span className="tw-mr-2">{actionInfo.icon}</span>
      <span className="tw-font-medium">{actionInfo.message}</span>
    </div>
  );
}
