{"name": "@repo/server", "private": true, "version": "1.0.0", "description": "This is the api built for AIMS", "main": "src/index.ts", "author": "<PERSON>", "license": "MIT", "scripts": {"withenv": "dotenv -e ../../.env --", "clean-port": "kill-port 3000", "dev": "pnpm run clean-port && pnpm withenv ts-node-dev ./src/index.ts", "seed": "pnpm withenv ts-node src/scripts/seed.ts", "migrate:paid-contracts": "pnpm withenv ts-node src/scripts/migrate-paid-contracts.ts", "cron:complete-campaigns": "pnpm withenv ts-node src/scripts/complete-expired-campaigns.ts", "cron:process-payouts": "pnpm withenv ts-node src/scripts/process-pending-payouts.ts", "test:puppeteer": "pnpm withenv ts-node src/scripts/test-puppeteer.ts", "start": "pnpm withenv node ./dist/index.js", "build": "pnpm withenv tsup src/index.ts --format esm,cjs", "lint": "eslint", "compose": "docker build -t server . && docker run -p 3000:3000 -it server"}, "keywords": [], "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/prettier-config": "workspace:*", "@repo/tsconfig": "workspace:*", "@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.13", "@types/express": "^4.17.21", "@types/express-fileupload": "^1.5.1", "@types/formidable": "^3.4.5", "@types/jsonwebtoken": "^9.0.6", "@types/node": "20.17.6", "@types/nodemailer": "^6.4.15", "@types/pdfmake": "^0.2.11", "@types/yup": "^0.32.0", "kill-port": "^2.0.1", "prisma": "^4.9.0", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "typescript": "5.7.3"}, "dependencies": {"@aws-sdk/client-s3": "^3.0.0", "@aws-sdk/s3-request-presigner": "^3.0.0", "@faker-js/faker": "^9.3.0", "@prisma/client": "^4.9.0", "@trpc/server": "11.0.0-rc.730", "bcrypt": "^5.1.1", "bullmq": "^5.25.6", "cloudinary": "^2.2.0", "cors": "^2.8.5", "date-fns": "^3.6.0", "dotenv": "^16.4.5", "dotenv-cli": "^7.4.2", "express": "^4.18.2", "express-async-errors": "^3.1.1", "formidable": "^3.5.1", "google": "link:@next/third-parties/google", "google-auth-library": "^9.15.0", "html-pdf-node": "^1.0.8", "html2canvas": "^1.4.1", "ipaddr.js": "^2.2.0", "jsonwebtoken": "^9.0.2", "jspdf": "^2.5.1", "mongoose": "^8.4.1", "node-fetch": "2", "nodemailer": "^6.9.13", "pdfmake": "^0.2.20", "playwright": "^1.40.0", "puppeteer": "^24.10.2", "react": "18.3.1", "react-dom": "18.3.1", "socket.io": "^4.8.1", "stripe": "^16.12.0", "tsc-alias": "^1.8.10", "tsconfig-paths": "^4.2.0", "tsup": "^8.1.0", "yup": "^1.4.0"}}