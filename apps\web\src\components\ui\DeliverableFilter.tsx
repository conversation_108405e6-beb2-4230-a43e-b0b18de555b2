import { KeyboardEvent, useState } from "react";

import { Input } from "./input";
import {
  Select,
  SelectContent,
  SelectTrigger,
} from "./select";

interface DeliverableFilterProps {
  onChange: (value: { min?: number; max?: number } | undefined) => void;
}

export function DeliverableFilter({ onChange }: DeliverableFilterProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [inputMin, setInputMin] = useState<string>("");
  const [inputMax, setInputMax] = useState<string>("");
  const [displayMin, setDisplayMin] = useState<string>("");
  const [displayMax, setDisplayMax] = useState<string>("");

  const handleApplyValues = () => {
    // Only update if values have changed
    if (inputMin !== displayMin || inputMax !== displayMax) {
      setDisplayMin(inputMin);
      setDisplayMax(inputMax);

      const minNum = inputMin ? parseInt(inputMin) : undefined;
      const maxNum = inputMax ? parseInt(inputMax) : undefined;

      if (!minNum && !maxNum) {
        onChange(undefined);
      } else {
        onChange({ min: minNum, max: maxNum });
      }
    }
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      handleApplyValues();
      setIsOpen(false);
    }
  };

  const displayText =
    displayMin || displayMax
      ? `${displayMin || "0"} - ${displayMax || "∞"} Deliverables`
      : "# Deliverables";

  return (
    <Select
      open={isOpen}
      onOpenChange={(open) => {
        setIsOpen(open);
        if (!open) {
          handleApplyValues();
        }
      }}
    >
      <SelectTrigger className="tw-w-[160px] sm:tw-w-[180px] tw-bg-aims-dark-2 tw-border-aims-dark-3 tw-text-aims-text-primary tw-h-12 sm:tw-h-10">
        <span className="tw-text-aims-text-primary tw-text-sm sm:tw-text-base">{displayText}</span>
      </SelectTrigger>
      <SelectContent>
        <div className="tw-p-2 tw-space-y-4">
          <div className="tw-flex tw-items-center tw-gap-2">
            <Input
              type="number"
              min={0}
              placeholder="Min"
              value={inputMin}
              onChange={(e) => setInputMin(e.target.value)}
              onBlur={handleApplyValues}
              onKeyDown={handleKeyDown}
              className="tw-w-20 tw-bg-aims-dark-2 tw-border-aims-dark-3 tw-text-aims-text-primary [&::-webkit-outer-spin-button]:tw-appearance-none [&::-webkit-inner-spin-button]:tw-appearance-none [appearance:textfield]"
            />
            <span className="tw-text-aims-text-primary">to</span>
            <Input
              type="number"
              min={0}
              placeholder="Max"
              value={inputMax}
              onChange={(e) => setInputMax(e.target.value)}
              onBlur={handleApplyValues}
              onKeyDown={handleKeyDown}
              className="tw-w-20 tw-bg-aims-dark-2 tw-border-aims-dark-3 tw-text-aims-text-primary [&::-webkit-outer-spin-button]:tw-appearance-none [&::-webkit-inner-spin-button]:tw-appearance-none [appearance:textfield]"
            />
          </div>
        </div>
      </SelectContent>
    </Select>
  );
}
