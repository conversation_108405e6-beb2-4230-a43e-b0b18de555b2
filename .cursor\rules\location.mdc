---
description: location
globs: 
alwaysApply: false
---
# Google Places API Integration Guide

## Overview
This guide covers implementing location autocomplete using the Google Places API across React platforms (Web/Next.js and React Native). The Places API provides rich location data, including addresses, cities, and geographic coordinates.

## API Key Setup

1. **Get API Key**:
   ```bash
   1. Go to Google Cloud Console
   2. Create/Select a project
   3. Enable the Places API
   4. Create credentials (API key)
   5. Restrict the key to Places API only
   ```

2. **Environment Variables**:
   ```env
   # .env
   NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your_api_key    # For Next.js
   EXPO_PUBLIC_GOOGLE_MAPS_API_KEY=your_api_key    # For React Native
   ```

## Implementation

### 1. Place Autocomplete

#### Web/Next.js Implementation:
```typescript
interface PlaceSuggestion {
    description: string;
    place_id: string;
}

async function getPlaceSuggestions(query: string): Promise<PlaceSuggestion[]> {
    if (!query.trim()) return [];

    try {
        const response = await fetch(
            `https://maps.googleapis.com/maps/api/place/autocomplete/json?` +
            new URLSearchParams({
                input: query,
                types: '(cities)',  // or '(regions)' or 'address' based on needs
                key: process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY!
            })
        );
        
        const data = await response.json();
        return data.predictions.map((prediction: any) => ({
            description: prediction.description,
            place_id: prediction.place_id
        }));
    } catch (error) {
        console.error('Error fetching place suggestions:', error);
        return [];
    }
}
```

#### React Native Implementation:
```typescript
interface PlaceSuggestion {
    description: string;
    place_id: string;
}

async function getPlaceSuggestions(query: string): Promise<PlaceSuggestion[]> {
    if (!query.trim()) return [];

    try {
        const response = await fetch(
            `https://maps.googleapis.com/maps/api/place/autocomplete/json?` +
            `input=${encodeURIComponent(query)}` +
            `&types=(cities)` +
            `&key=${process.env.EXPO_PUBLIC_GOOGLE_MAPS_API_KEY}`
        );
        
        const data = await response.json();
        return data.predictions.map((prediction: any) => ({
            description: prediction.description,
            place_id: prediction.place_id
        }));
    } catch (error) {
        console.error('Error fetching place suggestions:', error);
        return [];
    }
}
```

### 2. Place Details

```typescript
interface PlaceDetails {
    coordinates: {
        lat: number;
        lng: number;
    };
    address_components: {
        city?: string;
        state?: string;
        country?: string;
        postal_code?: string;
    };
}

async function getPlaceDetails(placeId: string): Promise<PlaceDetails | null> {
    try {
        const response = await fetch(
            `https://maps.googleapis.com/maps/api/geocode/json?` +
            `place_id=${placeId}` +
            `&key=${process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || process.env.EXPO_PUBLIC_GOOGLE_MAPS_API_KEY}`
        );

        const data = await response.json();
        if (!data.results?.[0]) return null;

        const result = data.results[0];
        const addressComponents: { [key: string]: string } = {};

        // Parse address components
        result.address_components.forEach((component: any) => {
            if (component.types.includes('locality')) {
                addressComponents.city = component.long_name;
            }
            if (component.types.includes('administrative_area_level_1')) {
                addressComponents.state = component.short_name;
            }
            if (component.types.includes('country')) {
                addressComponents.country = component.long_name;
            }
            if (component.types.includes('postal_code')) {
                addressComponents.postal_code = component.long_name;
            }
        });

        return {
            coordinates: result.geometry.location,
            address_components: addressComponents
        };
    } catch (error) {
        console.error('Error fetching place details:', error);
        return null;
    }
}
```

### 3. Debounced Search Implementation

```typescript
import { useEffect, useRef, useState } from 'react';

function useDebounce<T>(value: T, delay: number = 300): T {
    const [debouncedValue, setDebouncedValue] = useState<T>(value);

    useEffect(() => {
        const timer = setTimeout(() => {
            setDebouncedValue(value);
        }, delay);

        return () => {
            clearTimeout(timer);
        };
    }, [value, delay]);

    return debouncedValue;
}

// Usage Example
function LocationSearch() {
    const [query, setQuery] = useState('');
    const [suggestions, setSuggestions] = useState<PlaceSuggestion[]>([]);
    const debouncedQuery = useDebounce(query, 300);

    useEffect(() => {
        if (debouncedQuery) {
            getPlaceSuggestions(debouncedQuery)
                .then(setSuggestions);
        } else {
            setSuggestions([]);
        }
    }, [debouncedQuery]);

    // ... rest of component
}
```

## Best Practices

### 1. Error Handling
```typescript
function handlePlaceError(error: any): void {
    if (error.status === 'ZERO_RESULTS') {
        // Handle no results found
        console.log('No locations found');
    } else if (error.status === 'OVER_QUERY_LIMIT') {
        // Handle API quota exceeded
        console.error('API quota exceeded');
    } else {
        // Handle other errors
        console.error('Error:', error.message);
    }
}
```

### 2. Type Validation
```typescript
interface GooglePlacePrediction {
    description: string;
    place_id: string;
    structured_formatting: {
        main_text: string;
        secondary_text: string;
    };
    types: string[];
}

function isValidPrediction(data: any): data is GooglePlacePrediction {
    return (
        typeof data.description === 'string' &&
        typeof data.place_id === 'string' &&
        data.structured_formatting &&
        typeof data.structured_formatting.main_text === 'string'
    );
}
```

### 3. Rate Limiting
```typescript
class RateLimiter {
    private queue: Array<() => Promise<any>> = [];
    private processing = false;
    private requestsPerSecond: number;

    constructor(requestsPerSecond: number = 10) {
        this.requestsPerSecond = requestsPerSecond;
    }

    async add<T>(fn: () => Promise<T>): Promise<T> {
        return new Promise((resolve, reject) => {
            this.queue.push(async () => {
                try {
                    const result = await fn();
                    resolve(result);
                } catch (error) {
                    reject(error);
                }
            });

            if (!this.processing) {
                this.process();
            }
        });
    }

    private async process(): Promise<void> {
        this.processing = true;
        while (this.queue.length > 0) {
            const fn = this.queue.shift();
            if (fn) {
                await fn();
                await new Promise(resolve => 
                    setTimeout(resolve, 1000 / this.requestsPerSecond)
                );
            }
        }
        this.processing = false;
    }
}

// Usage
const rateLimiter = new RateLimiter(10); // 10 requests per second

async function searchWithRateLimit(query: string) {
    return rateLimiter.add(() => getPlaceSuggestions(query));
}
```

## Security Considerations

1. **API Key Protection**:
   - Always use environment variables
   - Set up API key restrictions in Google Cloud Console
   - Implement HTTP referrer restrictions
   - Use separate keys for development and production

2. **Data Validation**:
   - Validate all API responses
   - Sanitize user input before making API calls
   - Implement proper error handling

3. **Rate Limiting**:
   - Implement client-side rate limiting
   - Monitor API usage
   - Set up usage alerts in Google Cloud Console

## Performance Optimization

1. **Caching**:
```typescript
const locationCache = new Map<string, PlaceSuggestion[]>();

async function getCachedSuggestions(query: string): Promise<PlaceSuggestion[]> {
    const cacheKey = query.toLowerCase();
    
    if (locationCache.has(cacheKey)) {
        return locationCache.get(cacheKey)!;
    }

    const suggestions = await getPlaceSuggestions(query);
    locationCache.set(cacheKey, suggestions);
    
    // Clear cache after 1 hour
    setTimeout(() => {
        locationCache.delete(cacheKey);
    }, 3600000);

    return suggestions;
}
```

2. **Debouncing**:
```typescript
let searchTimeout: NodeJS.Timeout;

function debouncedSearch(query: string, callback: (results: PlaceSuggestion[]) => void) {
    if (searchTimeout) {
        clearTimeout(searchTimeout);
    }
    
    searchTimeout = setTimeout(async () => {
        const results = await getPlaceSuggestions(query);
        callback(results);
    }, 300);
}
```

## Testing

1. **Unit Tests**:
```typescript
describe('Location Service', () => {
    it('should return suggestions for valid query', async () => {
        const suggestions = await getPlaceSuggestions('New York');
        expect(suggestions).toBeInstanceOf(Array);
        expect(suggestions.length).toBeGreaterThan(0);
    });

    it('should handle empty query', async () => {
        const suggestions = await getPlaceSuggestions('');
        expect(suggestions).toEqual([]);
    });
});
```

2. **Mock Implementation**:
```typescript
const mockPlacesService = {
    async getPlaceSuggestions(query: string): Promise<PlaceSuggestion[]> {
        return [
            { description: 'New York, NY, USA', place_id: 'mock_ny' },
            { description: 'Los Angeles, CA, USA', place_id: 'mock_la' }
        ];
    }
};
```

## Common Issues and Solutions

1. **CORS Issues**:
   - Use a proxy server for web applications
   - Implement proper API key restrictions
   - Handle CORS errors gracefully

2. **Rate Limiting**:
   - Implement exponential backoff
   - Cache frequently requested locations
   - Monitor API usage

3. **Mobile-Specific**:
   - Handle offline scenarios
   - Implement proper keyboard handling
   - Consider device location services integration

## Additional Resources

- [Google Places API Documentation](mdc:https:/developers.google.com/maps/documentation/places/web-service/overview)
- [Google Maps Platform Console](mdc:https:/console.cloud.google.com/google/maps-apis)
- [API Pricing Information](mdc:https:/developers.google.com/maps/billing/understanding-cost-of-use) 