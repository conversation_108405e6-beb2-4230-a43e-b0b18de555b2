import UserModel from "../models/user";
import BrandModel from "../models/brand";
import AthleteModel from "../models/athlete";
import { ExtendedTRPCError } from "../utils/trpc";

export async function getAllUsers() {
    try {
        const users = await UserModel.find({}, {
            password: 0, // Exclude password field
            roles: 0,    // Exclude roles for this endpoint
        });

        const usersWithProfiles = await Promise.all(users.map(async (user) => {
            const baseUser = user.toClient();
            let profile = null;
            
            if (user.userType === 'brand') {
                profile = await BrandModel.findOne({ userId: user._id });
            } else {
                profile = await AthleteModel.findOne({ userId: user._id });
            }

            return {
                ...baseUser,
                profile
            };
        }));

        return usersWithProfiles;
    } catch (error) {
        console.error("Error fetching users:", error);
        throw new ExtendedTRPCError("INTERNAL_SERVER_ERROR", "Failed to fetch users");
    }
}

export async function getUserById(id: string) {
    try {
        const user = await UserModel.findById(id, {
            password: 0,
            roles: 0,
        });

        if (!user) {
            throw new ExtendedTRPCError("NOT_FOUND", "User not found");
        }

        const baseUser = user.toClient();
        let profile = null;

        if (user.userType === 'brand') {
            profile = await BrandModel.findOne({ userId: user._id });
        } else {
            profile = await AthleteModel.findOne({ userId: user._id });
        }

        return {
            ...baseUser,
            profile
        };
    } catch (error) {
        console.error("Error fetching user:", error);
        throw new ExtendedTRPCError("INTERNAL_SERVER_ERROR", "Failed to fetch user");
    }
} 