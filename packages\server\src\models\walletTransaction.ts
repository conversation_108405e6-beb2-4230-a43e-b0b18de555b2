import { Document, model, Schema } from "mongoose";
import { WalletTransaction, TransactionType } from "../types/athlete";

// Document interface for MongoDB
export interface WalletTransactionDocument extends Document {
  _id: string;
  athleteId: Schema.Types.ObjectId;
  type: TransactionType;
  amount: number;
  description: string;
  contractId?: Schema.Types.ObjectId;
  payoutRequestId?: Schema.Types.ObjectId;
  metadata?: {
    contractNumber?: string;
    campaignName?: string;
    deliverableNames?: string[];
  };
  createdAt: Date;
  toClient(): WalletTransaction;
}

// Mongoose schema
const walletTransactionSchema = new Schema<WalletTransactionDocument>(
  {
    athleteId: {
      type: Schema.Types.ObjectId,
      ref: "Athletes",
      required: true,
    },
    type: {
      type: String,
      enum: Object.values(TransactionType),
      required: true,
    },
    amount: {
      type: Number,
      required: true,
    },
    description: {
      type: String,
      required: true,
    },
    contractId: {
      type: Schema.Types.ObjectId,
      ref: "Contracts",
    },
    payoutRequestId: {
      type: Schema.Types.ObjectId,
      ref: "AthletePayoutRequest",
    },
    metadata: {
      contractNumber: String,
      campaignName: String,
      deliverableNames: [String],
    },
  },
  {
    timestamps: { createdAt: true, updatedAt: false }, // Only track creation time
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Indexes for performance
walletTransactionSchema.index({ athleteId: 1, createdAt: -1 });
walletTransactionSchema.index({ type: 1 });
walletTransactionSchema.index({ contractId: 1 }, { sparse: true });
walletTransactionSchema.index({ payoutRequestId: 1 }, { sparse: true });

// Instance method to convert to client format
walletTransactionSchema.methods.toClient = function(): WalletTransaction {
  return {
    id: this._id.toString(),
    athleteId: this.athleteId,
    type: this.type,
    amount: this.amount,
    description: this.description,
    contractId: this.contractId,
    payoutRequestId: this.payoutRequestId,
    metadata: this.metadata,
    createdAt: this.createdAt,
  };
};

const WalletTransactionModel = model<WalletTransactionDocument>("WalletTransaction", walletTransactionSchema);
export default WalletTransactionModel;
