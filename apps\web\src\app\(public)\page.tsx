"use client";

import Script from "next/script";
import Brands from "@/components/landing/Brands";
import Top from "@/components/landing/Top";
import WhyAims from "@/components/landing/WhyAims";
import { motion } from "framer-motion";

export default function Home() {
  return (
    <>
      <Script
        id="json-ld"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Organization",
            name: "AIMS - Athlete & Influencer Marketing Solutions",
            description:
              "Connect with college athletes and influencers for authentic brand partnerships. AIMS helps local businesses and athletes create meaningful marketing collaborations.",
            url: `${process.env.NEXT_PUBLIC_APP_URL}`,
            logo: `${process.env.NEXT_PUBLIC_APP_URL}/landing-page/nav-logo.png`,
            sameAs: [
              "https://www.linkedin.com/company/athlete-influencer-marketing-solutions-llc/",
            ],
            offers: {
              "@type": "Offer",
              name: "Athlete Marketing Services",
              description:
                "Professional athlete marketing and influencer partnerships for businesses of all sizes.",
            },
          }),
        }}
      />
      <motion.div
        className="tw-relative tw-overflow-hidden"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        <main>
          <section aria-label="Hero section">
            <Top />
          </section>

          <section aria-label="Featured brands">
            <Brands />
          </section>

          <section aria-label="Why choose AIMS">
            <WhyAims />
          </section>
        </main>
      </motion.div>
    </>
  );
}
