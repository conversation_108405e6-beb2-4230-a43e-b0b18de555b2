"use client";

import { useParams } from "next/navigation";
import { trpc } from "@/lib/trpc/client";

import { BrandContractView } from "@/components/contract/BrandContractView";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { LoadingSpinner } from "@/components/ui/LoadingSpinner";
import Link from "next/link";

function BackToContractsButton() {
  return (
    <Button
      variant="outline"
      asChild
      className="tw-flex tw-items-center tw-gap-2 tw-text-aims-text-primary"
    >
      <Link href="/app/brand/contracts">
        ← Back to Contracts
      </Link>
    </Button>
  );
}

export default function BrandContractPage() {
  const params = useParams();
  const contractId = params.id as string;

  // Fetch contract details
  const {
    data: contract,
    isLoading,
    error,
    refetch,
  } = trpc.contract.getById.useQuery({
    contractId,
  });

  const handleContractUpdated = async () => {
    // Refetch contract data to get updated status
    await refetch();
    // Note: Reduced polling reliance - real-time updates via socket events
    // should handle most status changes automatically
  };

  if (isLoading) {
    return (
      <div className="tw-container tw-mx-auto tw-px-4 sm:tw-px-6 lg:tw-px-8 tw-py-8">
        <div className="tw-flex tw-justify-center tw-items-center tw-min-h-[400px]">
          <LoadingSpinner />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="tw-container tw-mx-auto tw-px-4 sm:tw-px-6 lg:tw-px-8 tw-py-8">
        <Card>
          <CardContent className="tw-py-12 tw-text-center">
            <h2 className="tw-text-xl tw-font-semibold tw-text-gray-900 tw-mb-2">
              Error Loading Contract
            </h2>
            <p className="tw-text-gray-600 tw-mb-4">
              {error.message || "Failed to load contract details. Please try again."}
            </p>
            <div className="tw-flex tw-justify-center tw-gap-3">
              <BackToContractsButton />
              <Button onClick={() => refetch()}>
                Try Again
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!contract) {
    return (
      <div className="tw-container tw-mx-auto tw-px-4 sm:tw-px-6 lg:tw-px-8 tw-py-8">
        <Card>
          <CardContent className="tw-py-12 tw-text-center">
            <h2 className="tw-text-xl tw-font-semibold tw-text-gray-900 tw-mb-2">
              Contract Not Found
            </h2>
            <p className="tw-text-gray-600 tw-mb-4">
              The contract you&amp;re looking for doesn&amp;t exist or you don&amp;t have permission to view it.
            </p>
            <BackToContractsButton />
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="tw-container tw-mx-auto tw-px-4 sm:tw-px-6 lg:tw-px-8 tw-py-8 tw-space-y-6">
      {/* Navigation */}
      <div className="tw-flex tw-flex-col sm:tw-flex-row tw-items-start sm:tw-items-center tw-justify-between tw-gap-4">
        <BackToContractsButton />
        
        {/* Status-specific action prompts */}
        {contract.status === "PENDING_BRAND_REVIEW" && (
          <div className="tw-bg-yellow-50 tw-text-yellow-700 tw-px-4 tw-py-2 tw-rounded-lg tw-border tw-border-yellow-200">
            <span className="tw-font-medium">⚡ Action Required:</span> This contract needs your review
          </div>
        )}
        
        {contract.status === "PENDING_BRAND_APPROVAL" && (
          <div className="tw-bg-orange-50 tw-text-orange-700 tw-px-4 tw-py-2 tw-rounded-lg tw-border tw-border-orange-200">
            <span className="tw-font-medium">⚡ Ready for Approval:</span> This contract is ready to be sent to the athlete
          </div>
        )}
        
        {contract.status === "PENDING_ATHLETE_SIGNATURE" && (
          <div className="tw-bg-blue-50 tw-text-blue-700 tw-px-4 tw-py-2 tw-rounded-lg tw-border tw-border-blue-200">
            <span className="tw-font-medium">⏳ Waiting:</span> Contract sent to athlete for signature
          </div>
        )}
        
        {contract.status === "ATHLETE_SIGNED" && (
          <div className="tw-bg-green-50 tw-text-green-700 tw-px-4 tw-py-2 tw-rounded-lg tw-border tw-border-green-200">
            <span className="tw-font-medium">✓ Complete:</span> Contract has been signed {contract.athleteSignedAt && `on ${new Date(contract.athleteSignedAt).toLocaleDateString()}`}
          </div>
        )}
      </div>

      {/* Contract View */}
      <BrandContractView
        contract={contract}
        onContractUpdated={handleContractUpdated}
      />
    </div>
  );
}
