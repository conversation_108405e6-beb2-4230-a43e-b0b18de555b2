"use client";

import Interests from "@/components/campaign/Interests";
import { setCurrentStep } from "@/store/slices/campaign";
import { useEffect } from "react";
import { useDispatch } from "react-redux";

export default function NewCampaignInterestsPage() {
  const dispatch = useDispatch();
  useEffect(() => {
    dispatch(setCurrentStep(2));
  }, [dispatch]);
  return <Interests isEditMode={false} />;
}
