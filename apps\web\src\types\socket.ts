export enum SocketConnectionStatus {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting',
  TOKEN_REFRESHING = 'token_refreshing',
  AUTH_ERROR = 'auth_error',
  CONNECTION_ERROR = 'connection_error'
}

export interface SocketConnectionState {
  status: SocketConnectionStatus;
  isConnected: boolean;
  isReconnecting: boolean;
  isTokenRefreshing: boolean;
  lastError?: string;
  reconnectAttempts: number;
  maxReconnectAttempts: number;
}

export interface SocketError {
  type: 'auth' | 'connection' | 'token' | 'unknown';
  message: string;
  isRetryable: boolean;
  requiresTokenRefresh: boolean;
}

export interface SocketHookReturn {
  socket: any;
  connectionState: SocketConnectionState;
  joinChat: (chatId: string) => void;
  leaveChat: (chatId: string) => void;
  sendMessage: (chatId: string, content: string, type?: string) => void;
  sendTypingStatus: (chatId: string, isTyping: boolean) => void;
  markAsRead: (chatId: string) => void;
  forceReconnect: () => void;
  clearError: () => void;
}
