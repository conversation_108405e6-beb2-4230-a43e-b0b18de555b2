"use client";

import { useCallback, useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import AthleteCard from "@/components/ui/AthleteCard";
import AthleteFilters from "@/components/ui/AthleteFilters";
import { FullPageLoadingSpinner } from "@/components/ui/LoadingSpinner";
import { InvitationBottomBar } from "@/components/ui/InvitationBottomBar";
import { useDebounce } from "@/hooks/use-debounce";
import { client } from "@/lib/trpc/client";

import { SerializedAthleteProfile } from "@repo/server/src/models/user";

type AthleteWithUser = Omit<SerializedAthleteProfile, "userId"> & {
  userId: {
    _id: string;
    name: string;
  };
  _id: string;
};

export default function BrandAthletesPage() {
  const router = useRouter();
  const [athletes, setAthletes] = useState<AthleteWithUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [university, setUniversity] = useState("any");
  const [sport, setSport] = useState("any");
  const [yearInSchool, setYearInSchool] = useState("any");
  const [selectedAthletes, setSelectedAthletes] = useState<Set<string>>(
    new Set(),
  );
  const debouncedSearch = useDebounce(searchTerm, 1000);

  const handleAthleteSelect = (athleteId: string) => {
    setSelectedAthletes((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(athleteId)) {
        newSet.delete(athleteId);
      } else {
        newSet.add(athleteId);
      }
      return newSet;
    });
  };

  const handleInviteSelected = () => {
    if (selectedAthletes.size === 0) return;

    // Convert Set to Array and encode it for URL
    const selectedAthletesArray = Array.from(selectedAthletes);
    const encodedAthletes = encodeURIComponent(
      JSON.stringify(selectedAthletesArray),
    );

    // Navigate to invite page with selected athletes in URL state
    router.push(`/app/brand/athletes/invite?athletes=${encodedAthletes}`);
  };

  const fetchAthletes = useCallback(async () => {
    try {
      setLoading(true);
      const data = await client.athlete.getAthletes.query({
        filters: {
          ...(debouncedSearch ? { search: debouncedSearch } : {}),
          ...(university !== "any" ? { university } : {}),
          ...(sport !== "any" ? { sport } : {}),
          ...(yearInSchool !== "any" ? { yearInSchool } : {}),
        },
      });
      setAthletes(data);
    } catch (e) {
      console.error("Failed to fetch athletes:", e);
    } finally {
      setLoading(false);
    }
  }, [debouncedSearch, university, sport, yearInSchool]);

  useEffect(() => {
    fetchAthletes();
  }, [fetchAthletes]);

  return (
    <div className="tw-space-y-4 sm:tw-space-y-6 tw-p-4 sm:tw-p-6">
      <AthleteFilters
        searchTerm={searchTerm}
        onSearchChange={setSearchTerm}
        university={university}
        onUniversityChange={setUniversity}
        sport={sport}
        onSportChange={setSport}
        yearInSchool={yearInSchool}
        onYearInSchoolChange={setYearInSchool}
      />
      {athletes.length > 0 && (
        <h3 className="tw-text-lg tw-font-bold tw-mb-2">Select Athletes to invite them to campaigns</h3>
      )}

      <InvitationBottomBar
        selectedCount={selectedAthletes.size}
        selectedLabel="athlete"
        onNext={handleInviteSelected}
        nextLabel="Select Campaign"
        isVisible={selectedAthletes.size > 0}
        isNextDisabled={selectedAthletes.size === 0}
      />

      {loading ? (
        <FullPageLoadingSpinner />
      ) : athletes.length === 0 ? (
        <div className="tw-flex tw-justify-center tw-items-center tw-min-h-[200px] tw-px-4">
          <div className="tw-text-aims-text-secondary tw-text-center tw-text-sm sm:tw-text-base">
            {searchTerm ||
            university !== "any" ||
            sport !== "any" ||
            yearInSchool !== "any"
              ? "No athletes found matching your filters"
              : "No athletes available"}
          </div>
        </div>
      ) : (
        <div className="tw-grid tw-grid-cols-1 sm:tw-grid-cols-2 lg:tw-grid-cols-3 tw-gap-4 sm:tw-gap-6 tw-pb-32">
          {athletes.map((athlete) => (
            <AthleteCard
              key={athlete._id}
              athlete={athlete}
              isSelected={selectedAthletes.has(athlete._id)}
              onSelect={handleAthleteSelect}
            />
          ))}
        </div>
      )}
    </div>
  );
}
