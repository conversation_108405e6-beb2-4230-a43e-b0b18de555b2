"use client";

import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { LoadingSpinner } from "@/components/ui/LoadingSpinner";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/toast/use-toast";
import { useAuth } from "@/hooks/use-auth";
import { client } from "@/lib/trpc/client";
import { INTERESTS, SPORTS, UNIVERSITIES } from "@/lib/utils";
import { PlusIcon } from "@heroicons/react/24/outline";

import type { AthleteProfile as ServerAthleteProfile } from "@repo/server/src/models/user";
import { Label } from "@/components/ui/label";

interface AthleteProfile {
  university: string;
  sport: string;
  yearInSchool: string;
  position: string;
  birthDate: string;
  gender: "male" | "female";
  businessInterests: string[];
  socialMedia: {
    instagram?: string;
    twitter?: string;
    tiktok?: string;
  };
  profilePictureUrl?: string;
  bio?: string;
  minPayment: {
    shoot: number | null;
    inPerson: number | null;
    contentShare: number | null;
    contentCreation: number | null;
    giftedCollab: number | null;
    other: number | null;
  };
}

interface SocialMedia {
  instagram?: string;
  twitter?: string;
  tiktok?: string;
}

interface SocialMediaInputsProps {
  socialMedia: SocialMedia;
  onChange: (newSocialMedia: SocialMedia) => void;
}

interface ProfilePictureUploadProps {
  profilePictureUrl?: string;
  isUploading: boolean;
  onUpload: (e: React.ChangeEvent<HTMLInputElement>) => void;
  uploadError?: string;
}

const validateSocialMediaUrl = (
  platform: string,
  url: string,
): string | null => {
  if (!url) return null;

  try {
    const urlObj = new URL(url);

    switch (platform) {
      case "instagram":
        if (!urlObj.hostname.includes("instagram.com")) {
          return "Please enter a valid Instagram URL";
        }
        break;
      case "twitter":
        if (
          !urlObj.hostname.includes("twitter.com") &&
          !urlObj.hostname.includes("x.com")
        ) {
          return "Please enter a valid Twitter/X URL";
        }
        break;
      case "tiktok":
        if (!urlObj.hostname.includes("tiktok.com")) {
          return "Please enter a valid TikTok URL";
        }
        break;
    }

    return null;
  } catch {
    return "Please enter a valid URL";
  }
};

const SocialMediaInputs: React.FC<SocialMediaInputsProps> = ({
  socialMedia,
  onChange,
}) => {
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleChange = useCallback(
    (platform: string, value: string) => {
      // Always update the social media state to allow typing
      onChange({
        ...socialMedia,
        [platform]: value,
      });

      // Only show validation errors if the field is not empty
      if (value) {
        const error = validateSocialMediaUrl(platform, value);
        setErrors((prev) => ({
          ...prev,
          [platform]: error || "",
        }));
      } else {
        // Clear error if field is empty
        setErrors((prev) => ({
          ...prev,
          [platform]: "",
        }));
      }
    },
    [socialMedia, onChange],
  );

  return (
    <div className="tw-space-y-4">
      <h3 className="tw-text-lg tw-font-medium">Social Media</h3>
      <div className="tw-grid tw-grid-cols-2 tw-gap-4">
        <div>
          <label className="tw-block tw-text-sm tw-font-medium tw-text-aims-text-primary">
            Instagram
          </label>
          <Input
            type="url"
            value={socialMedia.instagram || ""}
            onChange={(e) => handleChange("instagram", e.target.value)}
            placeholder="https://instagram.com/username"
          />
          {errors.instagram && (
            <p className="tw-mt-1 tw-text-sm tw-text-red-500">
              {errors.instagram}
            </p>
          )}
        </div>
        <div>
          <label className="tw-block tw-text-sm tw-font-medium tw-text-aims-text-primary">
            Twitter/X
          </label>
          <Input
            type="url"
            value={socialMedia.twitter || ""}
            onChange={(e) => handleChange("twitter", e.target.value)}
            placeholder="https://twitter.com/username"
          />
          {errors.twitter && (
            <p className="tw-mt-1 tw-text-sm tw-text-red-500">
              {errors.twitter}
            </p>
          )}
        </div>
        <div>
          <label className="tw-block tw-text-sm tw-font-medium tw-text-aims-text-primary">
            TikTok
          </label>
          <Input
            type="url"
            value={socialMedia.tiktok || ""}
            onChange={(e) => handleChange("tiktok", e.target.value)}
            placeholder="https://tiktok.com/@username"
          />
          {errors.tiktok && (
            <p className="tw-mt-1 tw-text-sm tw-text-red-500">
              {errors.tiktok}
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

const ProfilePictureUpload: React.FC<ProfilePictureUploadProps> = ({
  profilePictureUrl,
  isUploading,
  onUpload,
  uploadError,
}) => {
  return (
    <div className="tw-flex tw-justify-center">
      <div
        className="tw-cursor-pointer tw-h-[200px] sm:tw-h-[250px] md:tw-h-[300px] tw-w-[200px] sm:tw-w-[250px] md:tw-w-[300px] tw-bg-aims-dark-3 tw-rounded-full tw-flex tw-flex-col tw-items-center tw-justify-center tw-border-2 tw-border-dashed tw-border-gray-600 tw-relative tw-overflow-hidden"
        onClick={() =>
          !isUploading && document.getElementById("profile-upload")?.click()
        }
      >
        {isUploading ? (
          <div className="tw-flex tw-items-center tw-justify-center tw-w-full tw-h-full">
            <LoadingSpinner />
          </div>
        ) : profilePictureUrl ? (
          <div className="tw-w-full tw-h-full tw-relative">
            <Image
              src={profilePictureUrl}
              alt="Profile"
              fill
              sizes="(max-width: 640px) 200px, (max-width: 768px) 250px, 300px"
              priority
              className="tw-object-cover tw-rounded-full"
            />
            <div className="tw-absolute tw-inset-0 tw-bg-black tw-bg-opacity-50 tw-flex tw-items-center tw-justify-center tw-opacity-0 hover:tw-opacity-100 tw-transition-opacity tw-rounded-full">
              <p className="tw-text-aims-text-primary tw-text-sm">
                Change photo
              </p>
            </div>
          </div>
        ) : (
          <div className="tw-text-center tw-p-4 sm:tw-p-6">
            <PlusIcon className="tw-mx-auto tw-h-8 sm:tw-h-12 tw-w-8 sm:tw-w-12 tw-text-gray-400" />
            <p className="tw-mt-2 tw-text-xs sm:tw-text-sm tw-text-gray-400">
              Click to upload
            </p>
            <p className="tw-text-xs tw-text-gray-500">
              PNG, JPG, GIF up to 10MB
            </p>
          </div>
        )}
        <input
          id="profile-upload"
          type="file"
          className="tw-hidden"
          accept="image/jpeg,image/png,image/gif"
          onChange={onUpload}
          disabled={isUploading}
        />
      </div>
      {uploadError && (
        <p className="tw-mt-2 tw-text-red-500 tw-text-sm tw-text-center">
          {uploadError}
        </p>
      )}
    </div>
  );
};

export default function EditAthleteProfilePage() {
  const { getProfile, user } = useAuth();
  const { toast } = useToast();
  const router = useRouter();
  const [name, setName] = useState("");
  const [university, setUniversity] = useState("");
  const [sport, setSport] = useState("");
  const [yearInSchool, setYearInSchool] = useState("");
  const [position, setPosition] = useState("");
  const [birthDate, setBirthDate] = useState("");
  const [gender, setGender] = useState<"male" | "female">("male");
  const [businessInterests, setBusinessInterests] = useState<string[]>([]);
  const [bio, setBio] = useState("");
  const [socialMedia, setSocialMedia] = useState<SocialMedia>({});
  const [profilePictureUrl, setProfilePictureUrl] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState("");
  const [minPayment, setMinPayment] = useState<AthleteProfile["minPayment"]>({
    shoot: null,
    inPerson: null,
    contentShare: null,
    contentCreation: null,
    giftedCollab: null,
    other: null,
  });
  const [hometown, setHometown] = useState("");
  const [input, setInput] = useState("");
  const [isExpanded, setIsExpanded] = useState(false);
  const universityOptions = useMemo(
    () =>
      UNIVERSITIES.map((university) => (
        <SelectItem key={university} value={university}>
          {university}
        </SelectItem>
      )),
    [],
  );

  const sportOptions = useMemo(
    () =>
      SPORTS.map((sport) => (
        <SelectItem key={sport} value={sport}>
          {sport}
        </SelectItem>
      )),
    [],
  );

  const yearOptions = useMemo(
    () => [
      <SelectItem key="freshman" value="freshman">
        Freshman
      </SelectItem>,
      <SelectItem key="sophomore" value="sophomore">
        Sophomore
      </SelectItem>,
      <SelectItem key="junior" value="junior">
        Junior
      </SelectItem>,
      <SelectItem key="senior" value="senior">
        Senior
      </SelectItem>,
      <SelectItem key="graduate" value="graduate">
        Graduate
      </SelectItem>,
    ],
    [],
  );

  const handleSocialMediaChange = useCallback((newSocialMedia: SocialMedia) => {
    setSocialMedia(newSocialMedia);
  }, []);

  const handlePositionChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setPosition(e.target.value);
    },
    [],
  );

  const handleBirthDateChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setBirthDate(e.target.value);
    },
    [],
  );

  const handleBioChange = useCallback(
    (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      setBio(e.target.value);
    },
    [],
  );

  const handleGenderChange = useCallback((value: "male" | "female") => {
    setGender(value);
  }, []);

  const handleMinPaymentChange = useCallback(
    (field: keyof AthleteProfile["minPayment"], value: string) => {
      setMinPayment((prev) => ({
        ...prev,
        [field]: value === "" ? null : Number(value),
      }));
    },
    [],
  );

  const handleHometownChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setHometown(e.target.value);
    },
    [],
  );

  const handleAddInterest = (interest: string) => {
    if (!businessInterests.includes(interest)) {
      setBusinessInterests([...businessInterests, interest]);
    }
    setInput("");
  };

  const handleRemoveInterest = (interest: string) => {
    setBusinessInterests(businessInterests.filter((i) => i !== interest));
  };

  const handleInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" && input.trim()) {
      handleAddInterest(input.trim());
    }
  };

  // Filter interests based on input and not already selected
  const filteredInterests = INTERESTS.filter(
    (interest) =>
      interest.toLowerCase().includes(input.toLowerCase()) &&
      !businessInterests.includes(interest),
  );

  // Get visible interests based on expanded state
  const visibleInterests = isExpanded
    ? filteredInterests
    : filteredInterests.slice(0, 8);

  useEffect(() => {
    const fetchProfile = async () => {
      try {
        const data =
          (await client.athlete.getProfile.query()) as ServerAthleteProfile;
        setName(data.name || user?.name || "");
        setUniversity(data.university || "");
        setSport(data.sport || "");
        setYearInSchool(data.yearInSchool || "");
        setPosition(data.position || "");
        setBirthDate(
          data.birthDate
            ? new Date(data.birthDate).toISOString().split("T")[0]
            : "",
        );
        setGender(data.gender || "male");
        setBusinessInterests(data.businessInterests || []);
        setSocialMedia(data.socialMedia || {});
        setProfilePictureUrl(data.profilePicture?.url || "");
        setBio(data.bio || "");
        setMinPayment(
          data.minPayment || {
            shoot: null,
            inPerson: null,
            contentShare: null,
            contentCreation: null,
            giftedCollab: null,
            other: null,
          },
        );
        setHometown(data.hometown || "");
      } catch (error) {
        console.error("Failed to fetch profile:", error);
        setError("Failed to load profile data. Please refresh the page.");
      }
    };

    fetchProfile();
  }, [user]);

  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validate file size (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
      setUploadError("File size must be less than 10MB");
      return;
    }

    try {
      setIsUploading(true);
      setUploadError("");

      // Get presigned URL
      const { url, key, publicUrl } = await client.athlete.getUploadUrl.mutate({
        fileType: file.type,
      });

      // Upload to S3
      const uploadResponse = await fetch(url, {
        method: "PUT",
        body: file,
        headers: {
          "Content-Type": file.type,
        },
      });

      if (!uploadResponse.ok) throw new Error("Failed to upload image");

      // Update profile with new image
      await client.athlete.updateProfilePicture.mutate({
        key,
        url: publicUrl,
      });

      // Update local state
      setProfilePictureUrl(publicUrl);

      // Update global auth state
      await getProfile(true);
    } catch (error) {
      setUploadError(
        error instanceof Error ? error.message : "Failed to upload image",
      );
    } finally {
      setIsUploading(false);
    }
  };

  const validateForm = () => {
    if (name.trim() === "") {
      toast({
        title: "Validation Error",
        description: "Name is required",
        variant: "destructive",
      });
      return false;
    }
    if (university.trim() === "") {
      toast({
        title: "Validation Error",
        description: "College/University is required",
        variant: "destructive",
      });
      return false;
    }
    if (sport.trim() === "") {
      toast({
        title: "Validation Error",
        description: "Sport is required",
        variant: "destructive",
      });
      return false;
    }
    if (yearInSchool.trim() === "") {
      toast({
        title: "Validation Error",
        description: "Year in school is required",
        variant: "destructive",
      });
      return false;
    }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate business interests
    if (businessInterests.length === 0) {
      toast({
        title: "Validation Error",
        description:
          "Please select at least one business interest before saving.",
        variant: "destructive",
      });
      return;
    }

    if (!validateForm()) {
      return;
    }

    setLoading(true);
    setError("");

    try {
      await client.athlete.updateAthleteProfile.mutate({
        name: name,
        university: university,
        sport: sport,
        yearInSchool: yearInSchool,
        position: position,
        birthDate: new Date(birthDate),
        gender: gender,
        businessInterests: businessInterests,
        socialMedia: socialMedia,
        bio: bio,
        minPayment: minPayment,
        hometown: hometown,
      });
      toast({
        title: "Success",
        description: "Profile updated successfully!",
        variant: "success",
      });
      await getProfile(true);
      router.push(`/app/athlete/${user?.id}`);
    } catch (error) {
      setError("Failed to update profile. Please try again.");
      console.error("Failed to update profile:", error);
    } finally {
      setLoading(false);
      router.push(`/app/athlete/${user?.id}`);
    }
  };

  return (
    <div className="tw-p-4 md:tw-p-8">
      <div className="tw-mx-auto tw-max-w-7xl">
        <h1 className="tw-mb-4 md:tw-mb-8 tw-text-xl md:tw-text-2xl tw-font-bold">
          Edit Athlete Profile
        </h1>

        {error && (
          <div className="tw-mb-4 tw-rounded-md tw-bg-red-50 tw-p-4 tw-text-red-700">
            {error}
          </div>
        )}

        <div className="tw-grid tw-grid-cols-1 lg:tw-grid-cols-2 tw-gap-8">
          {/* Left Column - Form Fields */}
          <div className="tw-space-y-6">
            <form onSubmit={handleSubmit}>
              <div className="tw-space-y-6">
                <div className="tw-space-y-2">
                  <Label required>
                    Display Name
                  </Label>
                  <Input
                    type="text"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                  />
                </div>
                <div className="tw-space-y-2">
                  <Label required>
                    College/University
                  </Label>
                  <Select value={university} onValueChange={setUniversity}>
                    <SelectTrigger className="!tw-bg-aims-dark-3 tw-border-aims-dark-3 tw-text-aims-text-primary">
                      <SelectValue placeholder="Select College/University" />
                    </SelectTrigger>
                    <SelectContent>{universityOptions}</SelectContent>
                  </Select>
                </div>
                <div className="tw-space-y-2">
                  <Label required>
                    Sport
                  </Label>
                  <Select value={sport} onValueChange={setSport}>
                    <SelectTrigger className="!tw-bg-aims-dark-3 tw-border-aims-dark-3 tw-text-aims-text-primary">
                      <SelectValue placeholder="Select Sport" />
                    </SelectTrigger>
                    <SelectContent>{sportOptions}</SelectContent>
                  </Select>
                </div>
                <div className="tw-space-y-2">
                  <Label required>
                    Year In School
                  </Label>
                  <Select value={yearInSchool} onValueChange={setYearInSchool}>
                    <SelectTrigger className="!tw-bg-aims-dark-3 tw-border-aims-dark-3 tw-text-aims-text-primary">
                      <SelectValue placeholder="Select Year" />
                    </SelectTrigger>
                    <SelectContent>{yearOptions}</SelectContent>
                  </Select>
                </div>
                <div className="tw-space-y-2">
                  <Label>
                    Position/Event
                  </Label>
                  <Input
                    type="text"
                    value={position}
                    onChange={handlePositionChange}
                  />
                </div>
                <div className="tw-space-y-2">
                  <Label>
                    Hometown
                  </Label>
                  <Input
                    type="text"
                    value={hometown}
                    onChange={handleHometownChange}
                  />
                </div>
                <div className="tw-space-y-2">
                  <Label required>
                    Birthday
                  </Label>
                  <Input
                    type="date"
                    required
                    value={birthDate}
                    onChange={handleBirthDateChange}
                  />
                </div>
                <div className="tw-space-y-2">
                  <Label required>
                    Gender
                  </Label>
                  <div className="tw-flex tw-gap-4">
                    <Label className="tw-flex tw-items-center tw-gap-2">
                      <input
                        type="radio"
                        name="gender"
                        value="male"
                        checked={gender === "male"}
                        onChange={() => handleGenderChange("male")}
                        className="tw-text-aims-primary tw-bg-aims-dark-3"
                      />
                      Male
                    </Label>
                    <Label className="tw-flex tw-items-center tw-gap-2">
                      <input
                        type="radio"
                        name="gender"
                        value="female"
                        checked={gender === "female"}
                        onChange={() => handleGenderChange("female")}
                        className="tw-text-aims-primary tw-bg-aims-dark-3"
                      />
                      Female
                    </Label>
                  </div>
                </div>
                <div className="tw-space-y-2">
                  <Label>
                    Bio
                  </Label>
                  <Textarea value={bio} onChange={handleBioChange} />
                </div>
                <div className="tw-space-y-4">
                  <h3 className="tw-text-lg tw-font-medium">
                    Minimum Payment Requirements
                  </h3>
                  <div className="tw-grid tw-grid-cols-1 sm:tw-grid-cols-2 tw-gap-4">
                    <div>
                      <Label>
                        Photo or Video Shoot
                      </Label>
                      <div className="tw-relative">
                        <span className="tw-absolute tw-left-3 tw-top-1/2 -tw-translate-y-1/2 tw-text-aims-text-primary">
                          $
                        </span>
                        <Input
                          type="number"
                          min="0"
                          value={minPayment.shoot || ""}
                          onChange={(e) =>
                            handleMinPaymentChange("shoot", e.target.value)
                          }
                          placeholder="Enter minimum amount"
                          className="tw-pl-7"
                        />
                      </div>
                    </div>
                    <div>
                      <Label>
                        In-Person Appearance
                      </Label>
                      <div className="tw-relative">
                        <span className="tw-absolute tw-left-3 tw-top-1/2 -tw-translate-y-1/2 tw-text-aims-text-primary">
                          $
                        </span>
                        <Input
                          type="number"
                          min="0"
                          value={minPayment.inPerson || ""}
                          onChange={(e) =>
                            handleMinPaymentChange("inPerson", e.target.value)
                          }
                          placeholder="Enter minimum amount"
                          className="tw-pl-7"
                        />
                      </div>
                    </div>
                    <div>
                      <Label>
                        Content Share
                      </Label>
                      <div className="tw-relative">
                        <span className="tw-absolute tw-left-3 tw-top-1/2 -tw-translate-y-1/2 tw-text-aims-text-primary">
                          $
                        </span>
                        <Input
                          type="number"
                          min="0"
                          value={minPayment.contentShare || ""}
                          onChange={(e) =>
                            handleMinPaymentChange(
                              "contentShare",
                              e.target.value,
                            )
                          }
                          placeholder="Enter minimum amount"
                          className="tw-pl-7"
                        />
                      </div>
                    </div>
                    <div>
                      <Label>
                        Content Creation
                      </Label>
                      <div className="tw-relative">
                        <span className="tw-absolute tw-left-3 tw-top-1/2 -tw-translate-y-1/2 tw-text-aims-text-primary">
                          $
                        </span>
                        <Input
                          type="number"
                          min="0"
                          value={minPayment.contentCreation || ""}
                          onChange={(e) =>
                            handleMinPaymentChange(
                              "contentCreation",
                              e.target.value,
                            )
                          }
                          placeholder="Enter minimum amount"
                          className="tw-pl-7"
                        />
                      </div>
                    </div>
                    <div>
                      <Label>
                        Gifted Collaboration
                      </Label>
                      <div className="tw-relative">
                        <span className="tw-absolute tw-left-3 tw-top-1/2 -tw-translate-y-1/2 tw-text-aims-text-primary">
                          $
                        </span>
                        <Input
                          type="number"
                          min="0"
                          value={minPayment.giftedCollab || ""}
                          onChange={(e) =>
                            handleMinPaymentChange(
                              "giftedCollab",
                              e.target.value,
                            )
                          }
                          placeholder="Enter minimum amount"
                          className="tw-pl-7"
                        />
                      </div>
                    </div>
                    <div>
                      <Label>
                        Custom
                      </Label>
                      <div className="tw-relative">
                        <span className="tw-absolute tw-left-3 tw-top-1/2 -tw-translate-y-1/2 tw-text-aims-text-primary">
                          $
                        </span>
                        <Input
                          type="number"
                          min="0"
                          value={minPayment.other || ""}
                          onChange={(e) =>
                            handleMinPaymentChange("other", e.target.value)
                          }
                          placeholder="Enter minimum amount"
                          className="tw-pl-7"
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <SocialMediaInputs
                  socialMedia={socialMedia}
                  onChange={handleSocialMediaChange}
                />
                <div className="tw-space-y-4">
                  <h3 className="tw-text-lg tw-font-medium">
                    Business Interests{" "}
                    <span className="tw-text-red-500">*</span>
                  </h3>
                  <div>
                    <h4 className="tw-text-sm tw-font-medium tw-mb-2">
                      Selected Interests
                    </h4>
                    <div className="tw-flex tw-flex-wrap tw-gap-2">
                      {businessInterests.map((interest) => (
                        <span
                          key={interest}
                          className="tw-px-2 sm:tw-px-3 tw-py-1 tw-rounded-full tw-text-xs sm:tw-text-sm tw-font-semibold tw-bg-aims-primary tw-text-black tw-flex tw-items-center tw-gap-2"
                        >
                          {interest}
                          <button
                            type="button"
                            onClick={() => handleRemoveInterest(interest)}
                            className="tw-text-black hover:tw-text-gray-700"
                          >
                            ×
                          </button>
                        </span>
                      ))}
                    </div>
                  </div>

                  <div className="tw-space-y-4">
                    <div>
                      <Input
                        type="text"
                        value={input}
                        onChange={(e) => setInput(e.target.value)}
                        onKeyDown={handleInputKeyDown}
                        placeholder="Search or type to add interests..."
                        className="tw-mb-4"
                      />
                      <div className="tw-flex tw-flex-wrap tw-gap-2">
                        {visibleInterests.map((interest) => (
                          <button
                            key={interest}
                            type="button"
                            onClick={() => handleAddInterest(interest)}
                            className="tw-px-2 sm:tw-px-3 tw-py-1 tw-rounded-full tw-text-xs sm:tw-text-sm tw-font-semibold tw-border tw-border-gray-600 tw-bg-gray-700 tw-text-gray-200 hover:tw-bg-aims-primary hover:tw-text-black"
                          >
                            {interest}
                          </button>
                        ))}
                      </div>
                      {filteredInterests.length > 8 && (
                        <button
                          type="button"
                          onClick={() => setIsExpanded(!isExpanded)}
                          className="tw-mt-2 tw-text-sm tw-text-aims-primary hover:tw-text-aims-primary/80"
                        >
                          {isExpanded
                            ? "Show less"
                            : `Show ${filteredInterests.length - 8} more`}
                        </button>
                      )}
                    </div>
                  </div>
                </div>
                <div className="tw-pt-4">
                  <Button
                    type="submit"
                    disabled={loading}
                    className="tw-w-full sm:tw-w-auto"
                  >
                    {loading ? "Saving..." : "Save Changes"}
                  </Button>
                </div>
              </div>
            </form>
          </div>

          {/* Right Column - Profile Picture Upload */}
          <div className="tw-order-first lg:tw-order-last">
            <div className="tw-flex tw-justify-center lg:tw-justify-start">
              <div className="tw-w-full tw-max-w-[300px]">
                <ProfilePictureUpload
                  profilePictureUrl={profilePictureUrl}
                  isUploading={isUploading}
                  onUpload={handleFileUpload}
                  uploadError={uploadError}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
