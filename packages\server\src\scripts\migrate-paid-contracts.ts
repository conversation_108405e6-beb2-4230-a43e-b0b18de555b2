import { connect, disconnect } from "mongoose";

import { ContractModel } from "../models/contract";
import { ContractStatus } from "../types/contract";

import "dotenv/config";

/**
 * Migration script to update existing contracts from PAID status to AWAITING_DELIVERABLES status
 * This ensures consistency with the new contract status flow where PAID automatically transitions
 * to AWAITING_DELIVERABLES after payment completion.
 */
async function migratePaidContracts() {
  console.log("Starting migration of PAID contracts to AWAITING_DELIVERABLES...");
  
  await connect(process.env.DB_URL!);
  
  try {
    // Find all contracts currently in PAID status
    const paidContracts = await ContractModel.find({
      status: ContractStatus.PAID
    });
    
    console.log(`Found ${paidContracts.length} contracts in PAID status`);
    
    if (paidContracts.length === 0) {
      console.log("No contracts to migrate.");
      return;
    }
    
    // Update all PAID contracts to AWAITING_DELIVERABLES
    const result = await ContractModel.updateMany(
      { status: ContractStatus.PAID },
      { 
        $set: { 
          status: ContractStatus.AWAITING_DELIVERABLES,
          // Add a migration timestamp for tracking
          migratedAt: new Date()
        } 
      }
    );
    
    console.log(`Successfully migrated ${result.modifiedCount} contracts from PAID to AWAITING_DELIVERABLES`);
    
    // Log details of migrated contracts for verification
    const migratedContracts = await ContractModel.find({
      status: ContractStatus.AWAITING_DELIVERABLES,
      migratedAt: { $exists: true }
    }).select('contractNumber title campaignId brandId athleteId paymentCompletedAt');
    
    console.log("\nMigrated contracts:");
    migratedContracts.forEach(contract => {
      console.log(`- Contract ${contract.contractNumber}: ${contract.title}`);
      console.log(`  Payment completed: ${contract.paymentCompletedAt || 'N/A'}`);
    });
    
  } catch (error) {
    console.error("Error during migration:", error);
    throw error;
  } finally {
    await disconnect();
  }
}

// Run the migration if this script is executed directly
if (require.main === module) {
  migratePaidContracts()
    .then(() => {
      console.log("\nMigration completed successfully!");
      process.exit(0);
    })
    .catch((err) => {
      console.error("Migration failed:", err);
      process.exit(1);
    });
}

export { migratePaidContracts };
