import { compare, genSalt, hash } from "bcrypt";
import { Document, model, Schema, Types } from "mongoose";

import { SerializedBrandProfile } from "../types/brand";
import AthleteModel from "./athlete";
import BrandModel from "./brand";

export enum AppRoles {
  ADMIN = "Admin",
  STANDARD = "Standard",
}

export type UserType = "brand" | "athlete";

export type Token = {
  token: string;
  location: string;
  createdAt: string;
};

// Base Document interface
export interface UserDocument extends Document {
  _id: string;
  email: string;
  password: string;
  name: string;
  userType: UserType;
  verified: boolean;
  tokens: Token[];
  roles: AppRoles[];
  createdAt: string;
  updatedAt: string;
  toClient(): Promise<User>;
  comparePassword(password: string): Promise<boolean>;
}

// Base User interface
export interface User {
  id: string;
  email: string;
  name: string;
  userType: UserType;
  verified: boolean;
  roles: AppRoles[];
  createdAt: string;
  updatedAt: string;
  athlete?: SerializedAthleteProfile;
  brand?: SerializedBrandProfile;
}

// Brand-specific interface

// Athlete-specific interface
export interface AthleteProfile {
  userId: Types.ObjectId;
  name: string;
  university: string | null;
  sport: string | null;
  yearInSchool: string | null;
  position: string | null;
  birthDate: Date | null;
  gender: "male" | "female" | null;
  bio: string | null;
  profilePicture: {
    url: string | null;
    key: string | null;
    uploadedAt: Date | null;
  };
  businessInterests: string[];
  socialMedia: {
    instagram?: string;
    twitter?: string;
    tiktok?: string;
  };
  minPayment: {
    shoot: number | null;
    inPerson: number | null;
    contentShare: number | null;
    contentCreation: number | null;
    giftedCollab: number | null;
    other: number | null;
  };
  referralSource: string;
  referralName: string;
  referralVenmo: string;
  hometown: string | null;
}

// Serialized version for API responses
export interface SerializedAthleteProfile
  extends Omit<
    AthleteProfile,
    "userId" | "practiceStartDate" | "birthDate" | "profilePicture"
  > {
  name: string;
  userId: string;
  yearInSchool: string | null;
  birthDate: string | null;
  profilePicture: {
    url: string | null;
    key: string | null;
    uploadedAt: string | null;
  };
}

export interface UserWithLogs extends User {
  authTokens: Token[];
}

export interface AuthTokens {
  access: string;
  refresh: string;
}

export interface AuthResponse {
  profile: User;
  tokens: AuthTokens;
}

export interface AuthState {
  profile: User | null;
  tokens: AuthTokens | null;
  loading: boolean;
  error: string | null;
}

const userSchema = new Schema<UserDocument>(
  {
    email: {
      type: String,
      required: true,
      unique: true,
      lowercase: true,
    },
    password: {
      type: String,
      required: true,
    },
    name: {
      type: String,
      required: true,
    },
    userType: {
      type: String,
      enum: ["brand", "athlete"],
      required: true,
    },
    verified: {
      type: Boolean,
      default: false,
    },
    tokens: {
      type: [
        {
          token: String,
          location: String,
          createdAt: String,
        },
      ],
      default: [],
    },
    roles: {
      type: [String],
      enum: Object.values(AppRoles),
      default: [AppRoles.STANDARD],
    },
  },
  { timestamps: true },
);

// Add toClient method to transform document to client format
userSchema.methods.toClient = async function (): Promise<User> {
  const baseUser: User = {
    id: this._id.toString(),
    email: this.email,
    name: this.name,
    userType: this.userType,
    verified: this.verified,
    roles: this.roles,
    createdAt: this.createdAt,
    updatedAt: this.updatedAt,
  };

  // If user is an athlete, fetch and include athlete profile
  if (this.userType === "athlete") {
    const profile = await AthleteModel.findOne({ userId: this._id });
    if (profile) {
      const { userId, name, yearInSchool, birthDate, profilePicture, ...rest } =
        profile.toObject();
      baseUser.athlete = {
        name,
        ...rest,
        userId: userId.toString(),
        yearInSchool: yearInSchool || null,
        birthDate: birthDate?.toISOString() || null,
        profilePicture: {
          ...profilePicture,
          uploadedAt: profilePicture?.uploadedAt?.toISOString() || null,
        },
      };
    }
  }

  // If user is a brand, fetch and include brand profile
  if (this.userType === "brand") {
    const profile = await BrandModel.findOne({ userId: this._id });
    if (profile) {
      const { userId, logo, ...rest } = profile.toObject();
      baseUser.brand = {
        ...rest,
        _id: rest._id.toString(),
        userId: userId.toString(),
        logo: {
          ...logo,
          uploadedAt: logo?.uploadedAt?.toISOString() || null,
        },
      };
    }
  }

  return baseUser;
};

userSchema.pre("save", async function (next) {
  if (this.isModified("password")) {
    const salt = await genSalt(10);
    this.password = await hash(this.password, salt);
  }
  next();
});

userSchema.methods.comparePassword = async function (password: string) {
  return await compare(password, this.password);
};

const UserModel = model("Users", userSchema);

export default UserModel;
