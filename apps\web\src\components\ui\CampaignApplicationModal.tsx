"use client";

import { useState } from "react";
import { trpc } from "@/lib/trpc/client";
import { getAuthState } from "@/store/slices/auth";
import { useSelector } from "react-redux";

import { SerializedCampaign } from "@repo/server/src/types/campaign";

import { Button } from "./button";
import { DeliverablesTable } from "./DeliverablesTable";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogOverlay,
  DialogPortal,
  DialogTitle,
} from "./dialog";
import { Textarea } from "./textarea";
import { useToast } from "./toast/use-toast";
import { ChatType, MessageType } from "@repo/server/src/types/chat";

interface Props {
  campaign: SerializedCampaign;
  isOpen: boolean;
  onClose: () => void;
  refetchCampaigns: () => void;
  refetchApplications: () => void;
}

export function CampaignApplicationModal({
  campaign,
  isOpen,
  onClose,
  refetchCampaigns,
  refetchApplications,
}: Props) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [message, setMessage] = useState("");
  const authState = useSelector(getAuthState);
  const userId = authState.profile?.id;

  // Fetch brand profile to get brand userId
  const { data: brand, isLoading: brandLoading } = trpc.brand.getBrand.useQuery(
    campaign.brandId,
    { enabled: !!campaign.brandId },
  );

  // Create or get chat between athlete and brand
  const createChatMutation = trpc.chat.createChat.useMutation();
  const sendMessageMutation = trpc.chat.sendMessage.useMutation();
  const submitApplicationMutation =
    trpc.campaign.submitApplication.useMutation();

  const handleSubmit = async () => {
    if (!userId || !brand || brandLoading) {
      toast({
        title: "Error",
        description: "Unable to send message. Please try again later.",
        variant: "destructive",
      });
      return;
    }
    setIsSubmitting(true);
    try {
      // Create or get the chat between athlete and brand
      const chat = await createChatMutation.mutateAsync({
        participantIds: [brand.userId],
        type: ChatType.DIRECT,
        campaignId: campaign.id,
      });
      // Send the message to the chat
      await sendMessageMutation.mutateAsync({
        chatId: chat.id,
        content: message.trim(),
        type: MessageType.CAMPAIGN_APPLICATION,
        campaignId: campaign.id,
      });
      // Create the CampaignApplication
      await submitApplicationMutation.mutateAsync({
        campaignId: campaign.id,
        message: message.trim(),
        deliverables: campaign.deliverables,
      });
      toast({
        title: "Application submitted",
        description:
          "Your application message has been sent and your application has been recorded.",
        variant: "success",
      });
      refetchCampaigns();
      refetchApplications();
      onClose();
    } catch (error) {
      toast({
        title: "Failed to apply",
        description: error instanceof Error ? error.message : "Something went wrong. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogPortal>
        <DialogOverlay className="tw-fixed tw-inset-0 tw-bg-black/80 tw-backdrop-blur-sm tw-z-[100] tw-transition-opacity tw-duration-200" />
        <DialogContent className="tw-fixed tw-left-[50%] tw-top-[50%] tw-z-[101] tw-w-full tw-max-w-2xl tw-translate-x-[-50%] tw-translate-y-[-50%] tw-bg-aims-dark-2 tw-border tw-border-aims-dark-4 tw-rounded-2xl tw-shadow-2xl tw-p-6 tw-max-h-[90vh] tw-overflow-y-auto">
          <DialogHeader className="tw-space-y-2">
            <DialogTitle className="tw-text-2xl tw-font-bold tw-text-aims-text-primary tw-tracking-tight">
              Apply to {campaign.name}
            </DialogTitle>
            <DialogDescription className="tw-text-base tw-text-aims-text-secondary tw-leading-relaxed">
              Submit your application for{" "}
              <span className="tw-text-aims-text-primary tw-font-medium">
                {campaign.name}
              </span>
              . Please include any relevant information about why you would be a
              good fit for this campaign.
            </DialogDescription>
          </DialogHeader>
          <div className="tw-space-y-4 tw-py-4">
            <div className="tw-flex tw-flex-col tw-gap-2">
              <label
                htmlFor="message"
                className="tw-text-sm tw-font-medium tw-text-aims-text-primary"
              >
                Application Message
              </label>
              <Textarea
                id="message"
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                placeholder="Tell the brand why you're interested in this campaign and what makes you a great fit..."
                className="tw-min-h-[100px] !tw-bg-aims-dark-2 tw-border-aims-dark-4 tw-rounded-xl tw-p-3 tw-text-sm placeholder:tw-text-aims-text-secondary"
              />
            </div>
            <div className="tw-space-y-2">
              <h4 className="tw-text-sm tw-font-semibold tw-text-aims-text-primary">
                Campaign Deliverables
              </h4>
              <div className="tw-overflow-hidden tw-rounded-xl tw-ring-1 tw-ring-aims-dark-4">
                <DeliverablesTable deliverables={campaign.deliverables} />
              </div>
            </div>
          </div>
          <DialogFooter className="tw-space-x-3 tw-pt-2">
            <Button
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
              className="tw-bg-aims-dark-3 tw-text-aims-text-primary hover:tw-bg-aims-dark-4 tw-border-aims-dark-4 tw-px-4 tw-h-10 tw-rounded-xl tw-transition-all tw-duration-200"
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={isSubmitting || brandLoading}
              className="tw-bg-aims-primary hover:tw-bg-aims-primary/90 tw-min-w-[120px] tw-h-10 tw-rounded-xl tw-transition-all tw-duration-200 tw-font-medium"
            >
              {isSubmitting ? "Submitting..." : "Submit Application"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </DialogPortal>
    </Dialog>
  );
}
