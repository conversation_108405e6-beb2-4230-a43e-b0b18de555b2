import { Types } from "mongoose";

export interface BrandProfile {
  userId: Types.ObjectId;
  companyName: string;
  industry: string;
  website?: string;
  location: string;
  description: string;
  logo: {
    url: string | null;
    key: string | null;
    uploadedAt: Date | null;
  };
  subscriptionActive: boolean;
  stripeSubscriptionId: string | null;
}

export interface SerializedBrandProfile
  extends Omit<BrandProfile, "userId" | "logo"> {
  _id: string;
  userId: string;
  logo: {
    url: string | null;
    key: string | null;
    uploadedAt: string | null;
  };
}
