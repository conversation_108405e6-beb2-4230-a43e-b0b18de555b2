"use client";

import ReviewCampaign from "@/components/campaign/Review";
import { setCurrentStep } from "@/store/slices/editCampaign";

import { useDispatch } from "react-redux";
import { useEffect } from "react";
export default function CampaignReviewPage() {
  const dispatch = useDispatch();
  useEffect(() => {
    dispatch(setCurrentStep(5));
  }, [dispatch]);
  return <ReviewCampaign isEditing={true} />;
}
