import { TokensStorage } from "./tokens-storage";
import { store } from "../../store";
import { setAuthState } from "../../store/slices/auth";

export interface SocketTokens {
  access: string;
  refresh: string;
}

export interface TokenRefreshResult {
  success: boolean;
  tokens?: SocketTokens;
  error?: string;
}

export class SocketTokenManager {
  private static refreshInProgress = false;
  private static refreshPromise: Promise<TokenRefreshResult> | null = null;
  private static lastRefreshedToken: string | null = null;
  private static refreshAttempts = 0;
  private static maxRefreshAttempts = 3;

  /**
   * Get current tokens from storage with fallback to Redux state
   */
  static getCurrentTokens(): SocketTokens | null {
    const storageTokens = TokensStorage.getTokens();
    
    if (!storageTokens?.accessToken || !storageTokens?.refreshToken) {
      return null;
    }

    return {
      access: storageTokens.accessToken,
      refresh: storageTokens.refreshToken,
    };
  }

  /**
   * Refresh tokens using the existing tRPC refresh mechanism
   */
  static async refreshTokens(currentExpiredToken?: string): Promise<TokenRefreshResult> {
    console.log("🔄 SocketTokenManager.refreshTokens called", {
      refreshInProgress: this.refreshInProgress,
      refreshAttempts: this.refreshAttempts,
      maxRefreshAttempts: this.maxRefreshAttempts,
      currentExpiredToken: currentExpiredToken ? `${currentExpiredToken.substring(0, 20)}...` : 'none'
    });

    // Circuit breaker: prevent infinite refresh attempts
    if (this.refreshAttempts >= this.maxRefreshAttempts) {
      console.error("🚫 Max refresh attempts reached, aborting");
      return {
        success: false,
        error: `Maximum refresh attempts (${this.maxRefreshAttempts}) exceeded`,
      };
    }

    // Prevent multiple simultaneous refresh attempts
    if (this.refreshInProgress && this.refreshPromise) {
      console.log("⏳ Refresh already in progress, waiting for existing promise");
      return this.refreshPromise;
    }

    // Validate that we're not trying to refresh the same token repeatedly
    if (currentExpiredToken && currentExpiredToken === this.lastRefreshedToken) {
      console.error("🔁 Attempting to refresh the same token again, aborting to prevent loop");
      return {
        success: false,
        error: "Attempting to refresh the same token repeatedly",
      };
    }

    this.refreshInProgress = true;
    this.refreshAttempts++;
    this.refreshPromise = this.performTokenRefresh(currentExpiredToken);

    try {
      const result = await this.refreshPromise;

      if (result.success) {
        // Reset attempts on successful refresh
        this.refreshAttempts = 0;
        this.lastRefreshedToken = result.tokens?.access || null;
        console.log("✅ Token refresh successful", {
          newToken: result.tokens?.access ? `${result.tokens.access.substring(0, 20)}...` : 'none'
        });
      } else {
        console.error("❌ Token refresh failed", result.error);
      }

      return result;
    } finally {
      this.refreshInProgress = false;
      this.refreshPromise = null;
    }
  }

  private static async performTokenRefresh(currentExpiredToken?: string): Promise<TokenRefreshResult> {
    try {
      const currentTokens = this.getCurrentTokens();
      
      if (!currentTokens?.refresh) {
        return {
          success: false,
          error: "No refresh token available",
        };
      }

      const baseUrl = process.env.NEXT_PUBLIC_SERVER_URL || "http://localhost:3000";
      
      const response = await fetch(`${baseUrl}/auth.refreshToken`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ refreshToken: currentTokens.refresh }),
      });

      if (!response.ok) {
        throw new Error(`Token refresh failed: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.error) {
        throw new Error(data.error.message || "Token refresh failed");
      }

      const newTokens = data.result.data.tokens;
      
      if (!newTokens?.access || !newTokens?.refresh) {
        throw new Error("Invalid token response format");
      }

      // Validate that the new token is different from the expired one
      if (currentExpiredToken && newTokens.access === currentExpiredToken) {
        throw new Error("Received the same token from refresh endpoint");
      }

      // Additional validation: check if we've seen this token before
      if (this.lastRefreshedToken && newTokens.access === this.lastRefreshedToken) {
        console.warn("⚠️ Received a token we've already refreshed to, potential server issue");
      }

      // Store the new tokens in localStorage
      TokensStorage.setTokens(newTokens.access, newTokens.refresh);

      // Update Redux state to keep it in sync
      try {
        const currentState = store.getState();
        if (currentState.auth.profile) {
          // Only update Redux if user is still authenticated
          store.dispatch(setAuthState({
            profile: currentState.auth.profile,
            tokens: {
              access: newTokens.access,
              refresh: newTokens.refresh
            }
          }));
          console.log("✅ Redux state updated with new tokens");
        }
      } catch (reduxError) {
        console.warn("⚠️ Failed to update Redux state, but tokens stored in localStorage:", reduxError);
      }

      console.log("💾 New tokens stored successfully", {
        oldToken: currentExpiredToken ? `${currentExpiredToken.substring(0, 20)}...` : 'none',
        newToken: `${newTokens.access.substring(0, 20)}...`,
        tokenChanged: currentExpiredToken !== newTokens.access
      });

      return {
        success: true,
        tokens: {
          access: newTokens.access,
          refresh: newTokens.refresh,
        },
      };
    } catch (error) {
      console.error("Token refresh failed:", error);
      
      // Clear invalid tokens
      TokensStorage.clearTokens();
      
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error during token refresh",
      };
    }
  }

  /**
   * Check if an error is related to token expiration
   */
  static isTokenExpirationError(error: any): boolean {
    if (!error) return false;

    const errorMessage = typeof error === 'string' ? error : error.message || '';

    return (
      errorMessage.includes('Token has expired') ||
      errorMessage.includes('Session Expired') ||
      errorMessage.includes('Authentication error') ||
      errorMessage.includes('jwt expired') ||
      errorMessage.includes('token expired')
    );
  }

  /**
   * Reset the token refresh state - useful for breaking infinite loops
   */
  static resetRefreshState(): void {
    console.log("🔄 Resetting token refresh state");
    this.refreshInProgress = false;
    this.refreshPromise = null;
    this.refreshAttempts = 0;
    this.lastRefreshedToken = null;
  }

  /**
   * Get current refresh state for debugging
   */
  static getRefreshState() {
    return {
      refreshInProgress: this.refreshInProgress,
      refreshAttempts: this.refreshAttempts,
      maxRefreshAttempts: this.maxRefreshAttempts,
      hasLastRefreshedToken: !!this.lastRefreshedToken
    };
  }

  /**
   * Check if an error is related to authentication
   */
  static isAuthenticationError(error: any): boolean {
    if (!error) return false;
    
    const errorMessage = typeof error === 'string' ? error : error.message || '';
    
    return (
      this.isTokenExpirationError(error) ||
      errorMessage.includes('Unauthorized') ||
      errorMessage.includes('Authentication failed') ||
      errorMessage.includes('Invalid token') ||
      errorMessage.includes('No token provided')
    );
  }

  /**
   * Clear all tokens and reset state
   */
  static clearTokens(): void {
    TokensStorage.clearTokens();
    this.refreshInProgress = false;
    this.refreshPromise = null;
    this.lastRefreshedToken = null;
    this.refreshAttempts = 0;
  }

  /**
   * Reset refresh attempts (useful for testing or manual reset)
   */
  static resetRefreshAttempts(): void {
    this.refreshAttempts = 0;
    console.log("🔄 Refresh attempts reset to 0");
  }
}
