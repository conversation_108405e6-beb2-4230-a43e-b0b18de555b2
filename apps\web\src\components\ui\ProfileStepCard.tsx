import Link from "next/link";

export default function ProfileStepCard({
  title,
  description,
  buttonText,
  completed,
  href,
}: {
  title: string;
  description: string;
  buttonText: string;
  completed: boolean;
  href: string;
}) {
  if (completed) return null;
  return (
    <div className="tw-rounded-lg tw-p-5 tw-shadow tw-flex tw-flex-col tw-gap-2 tw-bg-aims-dark-3">
      <div className="tw-font-semibold tw-text-aims-text-primary">{title}</div>
      <div className="tw-text-xs tw-text-aims-text-secondary">
        {description}
      </div>
      <Link
        className="tw-text-sm tw-text-left tw-text-aims-primary hover:tw-underline tw-mt-2"
        href={href}
      >
        {buttonText}
      </Link>
    </div>
  );
}
