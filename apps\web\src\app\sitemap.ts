import { MetadataRoute } from "next";

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || "https://aimsmarketing.ai";

  // Static pages
  const staticPages = [
    {
      url: baseUrl,
      lastModified: new Date(),
      changeFrequency: "weekly" as const,
      priority: 1,
    },
    {
      url: `${baseUrl}/about`,
      lastModified: new Date(),
      changeFrequency: "monthly" as const,
      priority: 0.8,
    },
    {
      url: `${baseUrl}/subscribe`,
      lastModified: new Date(),
      changeFrequency: "monthly" as const,
      priority: 0.8,
    },
  ];

  // Dynamic content (uncomment and implement as needed)
  // const campaigns = await fetchCampaigns() // Implement this function
  // const campaignPages = campaigns.map((campaign) => ({
  //   url: `${baseUrl}/campaign/${campaign.id}`,
  //   lastModified: new Date(campaign.updatedAt),
  //   changeFrequency: 'daily' as const,
  //   priority: 0.7,
  // }))

  // const athletes = await fetchAthletes() // Implement this function
  // const athletePages = athletes.map((athlete) => ({
  //   url: `${baseUrl}/athlete/${athlete.id}`,
  //   lastModified: new Date(athlete.updatedAt),
  //   changeFrequency: 'weekly' as const,
  //   priority: 0.6,
  // }))

  return [
    ...staticPages,
    // ...campaignPages,
    // ...athletePages,
  ];
}
