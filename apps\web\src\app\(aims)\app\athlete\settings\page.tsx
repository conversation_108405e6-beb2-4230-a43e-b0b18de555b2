"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { FormInput } from "@/components/ui/auth/FormInput";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/components/ui/toast/use-toast";
import { useAuth } from "@/hooks/use-auth";
import { client } from "@/lib/trpc/client";
import { Label } from "@/components/ui/label";

// Type guard to check if error has a message property
const isErrorWithMessage = (error: unknown): error is { message: string } => {
  return (
    typeof error === "object" &&
    error !== null &&
    "message" in error &&
    typeof (error as { message: unknown }).message === "string"
  );
};

// Helper function to get error message safely
const getErrorMessage = (error: unknown): string => {
  if (isErrorWithMessage(error)) {
    return error.message;
  }
  return "An unexpected error occurred";
};

export default function Settings() {
  const { user } = useAuth();
  const { toast } = useToast();
  const router = useRouter();
  const [showPasswordForm, setShowPasswordForm] = useState(false);
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  const handlePasswordChange = async () => {
    if (!currentPassword || !newPassword || !confirmPassword) {
      toast({
        title: "Error",
        description: "Please fill in all fields",
        variant: "destructive",
      });
      return;
    }

    if (newPassword !== confirmPassword) {
      toast({
        title: "Error",
        description: "New passwords do not match",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    try {
      await client.auth.updatePasswordWhenLoggedIn.mutate({
        currentPassword,
        newPassword,
      });

      toast({
        title: "Success",
        description: "Password updated successfully",
        variant: "success",
      });

      setShowPasswordForm(false);
      setCurrentPassword("");
      setNewPassword("");
      setConfirmPassword("");
    } catch (error: unknown) {
      toast({
        title: "Error",
        description: getErrorMessage(error) || "Failed to update password",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteAccount = async () => {
    setIsDeleting(true);
    try {
      await client.auth.deleteAccount.mutate();
      toast({
        title: "Success",
        description: "Your account has been deleted successfully",
        variant: "success",
      });
      router.push("/auth/login");
    } catch (error: unknown) {
      toast({
        title: "Error",
        description: getErrorMessage(error) || "Failed to delete account",
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <div className="tw-p-8">
      <div className="tw-mx-auto tw-max-w-3xl">
        <div className="tw-rounded-lg tw-bg-aims-dark-2 tw-p-6 tw-shadow-sm">
          <div className="tw-space-y-8 tw-divide-y tw-divide-gray-200">
            {/* Profile section */}
            <div className="tw-space-y-6">
              <div>
                <h3 className="tw-text-lg tw-font-medium tw-leading-6 tw-text-aims-text-primary">
                  Profile
                </h3>
                <p className="tw-mt-1 tw-text-sm tw-text-aims-text-secondary">
                  Manage your account settings and preferences.
                </p>
              </div>

              <div className="tw-grid tw-grid-cols-1 tw-gap-y-6 sm:tw-grid-cols-6">
                <div className="sm:tw-col-span-4">
                  <Label
                    htmlFor="email"
                  >
                    Email
                  </Label>
                  <div className="tw-mt-1">
                    <Input
                      type="email"
                      name="email"
                      id="email"
                      value={user?.email || ""}
                      readOnly
                      disabled
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Password section */}
            <div className="tw-pt-8">
              <div>
                <h3 className="tw-text-lg tw-font-medium tw-leading-6 tw-text-aims-text-primary">
                  Password
                </h3>
                <p className="tw-mt-1 tw-text-sm tw-text-aims-text-secondary">
                  Update your password to keep your account secure.
                </p>
              </div>

              <div className="tw-mt-6">
                {!showPasswordForm ? (
                  <Button onClick={() => setShowPasswordForm(true)}>
                    Change password
                  </Button>
                ) : null}
                <div
                  className={`tw-overflow-hidden tw-transition-all tw-duration-300 tw-ease-in-out ${
                    showPasswordForm
                      ? "tw-max-h-[500px] tw-opacity-100 tw-mt-4"
                      : "tw-max-h-0 tw-opacity-0"
                  }`}
                >
                  <div className="tw-space-y-4">
                    <FormInput
                      id="currentPassword"
                      label="Current Password"
                      type="password"
                      value={currentPassword}
                      onChange={setCurrentPassword}
                      required
                      disabled={isSubmitting}
                      isPassword
                      placeholder="Enter your current password"
                    />

                    <FormInput
                      id="newPassword"
                      label="New Password"
                      type="password"
                      value={newPassword}
                      onChange={setNewPassword}
                      required
                      disabled={isSubmitting}
                      isPassword
                      placeholder="Enter new password"
                    />

                    <FormInput
                      id="confirmPassword"
                      label="Confirm New Password"
                      type="password"
                      value={confirmPassword}
                      onChange={setConfirmPassword}
                      required
                      disabled={isSubmitting}
                      isPassword
                      placeholder="Confirm new password"
                    />

                    <div className="tw-flex tw-gap-4">
                      <Button
                        variant="outline"
                        onClick={() => {
                          setShowPasswordForm(false);
                          setCurrentPassword("");
                          setNewPassword("");
                          setConfirmPassword("");
                        }}
                        disabled={isSubmitting}
                        className="tw-bg-aims-dark-3 tw-text-aims-text-primary hover:tw-bg-aims-dark-4 tw-border-aims-dark-4"
                      >
                        Cancel
                      </Button>
                      <Button
                        onClick={handlePasswordChange}
                        disabled={isSubmitting}
                        className="tw-bg-aims-primary hover:tw-bg-aims-primary/90 tw-mb-2"
                      >
                        {isSubmitting ? "Updating..." : "Update Password"}
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Danger Zone */}
            <div className="tw-pt-8">
              <div>
                <h3 className="tw-text-lg tw-font-medium tw-leading-6 tw-text-red-600">
                  Danger Zone
                </h3>
                <p className="tw-mt-1 tw-text-sm tw-text-aims-text-secondary">
                  Once you delete your account, there is no going back. Please
                  be certain.
                </p>
              </div>

              <div className="tw-mt-6">
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button variant="destructive" disabled={isDeleting}>
                      {isDeleting ? "Deleting..." : "Delete account"}
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>
                        Are you absolutely sure?
                      </AlertDialogTitle>
                      <AlertDialogDescription>
                        This action cannot be undone. This will permanently
                        delete your account and remove all your data from our
                        servers.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction
                        onClick={handleDeleteAccount}
                        className="tw-bg-red-600 hover:tw-bg-red-700"
                      >
                        Delete Account
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
