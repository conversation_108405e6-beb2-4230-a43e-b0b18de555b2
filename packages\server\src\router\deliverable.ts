import * as yup from "yup";
import { privateProcedure, trpc } from "../lib/trpc";
import { getDeliverablesByIds } from "../controllers/deliverable";

export const deliverableRouter = trpc.router({
  // Get deliverables by their IDs
  getByIds: privateProcedure
    .input(
      yup.object({
        deliverableIds: yup.array().of(yup.string().required()).required("Deliverable IDs are required"),
      }),
    )
    .query(async ({ input }) => {
      return getDeliverablesByIds(input.deliverableIds);
    }),
});
