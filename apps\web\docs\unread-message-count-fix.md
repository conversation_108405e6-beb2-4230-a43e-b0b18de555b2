# Unread Message Count Fix

## Problem Description

The live updating message count (unread message indicator) in the top navigation bar stopped working and was no longer displaying real-time updates. This functionality was working previously but was broken during the recent Socket.io infinite loop fixes.

## Root Cause Analysis

The issue was caused by incorrect property access in components that use the `useSocket` hook. During the Socket.io infinite loop fixes, the hook's return structure was updated to return `connectionState` (which contains `isConnected`) instead of returning `isConnected` directly.

### Specific Issues Found:

1. **`useUnreadMessages` Hook** (`apps/web/src/hooks/use-unread-messages.ts`):
   - Line 9: Trying to destructure `isConnected` directly from `useSocket()`
   - Line 36: Using `isConnected` in the useEffect dependency array

2. **`ChatList` Component** (`apps/web/src/components/chat/ChatList.tsx`):
   - Line 24: Trying to destructure `isConnected` directly from `useSocket()`
   - Line 49: Using `isConnected` in the useEffect dependency array

### The Problem:

```typescript
// BROKEN - useSocket doesn't return isConnected directly
const { socket, isConnected } = useSocket();

// CORRECT - useSocket returns connectionState which contains isConnected
const { socket, connectionState } = useSocket();
```

## Solution Implementation

### 1. Fixed `useUnreadMessages` Hook

**Before:**
```typescript
const { socket, isConnected } = useSocket();

useEffect(() => {
  if (!socket || !user?.id || !isConnected) {
    return;
  }
  // ... event listeners
}, [socket, user?.id, isConnected, utils]);
```

**After:**
```typescript
const { socket, connectionState } = useSocket();

useEffect(() => {
  if (!socket || !user?.id || !connectionState.isConnected) {
    return;
  }
  // ... event listeners
}, [socket, user?.id, connectionState.isConnected, utils]);
```

### 2. Fixed `ChatList` Component

**Before:**
```typescript
const { socket, isConnected, markAsRead } = useSocket();

useEffect(() => {
  // ... event listeners
}, [socket, isConnected, user?.id, utils, markAsRead]);
```

**After:**
```typescript
const { socket, connectionState, markAsRead } = useSocket();

useEffect(() => {
  // ... event listeners
}, [socket, connectionState.isConnected, user?.id, utils, markAsRead]);
```

### 3. Enhanced Debugging

Added comprehensive logging to track:
- When new messages are received via Socket.io
- When messages are marked as read
- Unread count calculations
- Connection status changes

```typescript
const handleNewMessage = (message: any) => {
  console.log("📨 useUnreadMessages: New message received", {
    messageId: message.id,
    chatId: message.chatId,
    senderId: message.sender?.id,
    currentUserId: user?.id,
    isFromCurrentUser: message.sender?.id === user?.id
  });
  utils.chat.getChats.invalidate();
};
```

## How the Fix Works

1. **Correct Property Access**: Components now properly access `connectionState.isConnected` instead of trying to destructure `isConnected` directly.

2. **Proper Event Listener Setup**: Socket event listeners are now properly attached when the socket is connected, ensuring real-time updates work.

3. **Dependency Array Fix**: useEffect hooks now have the correct dependencies, ensuring they re-run when the connection state changes.

4. **Real-time Updates**: When new messages arrive or messages are marked as read:
   - Socket events (`newMessage`, `messagesRead`) are properly received
   - The `getChats` query is invalidated, triggering a refetch
   - The unread count is recalculated with fresh data
   - The navigation bar updates in real-time

## Verification

The fix ensures that:

✅ **New Messages**: When a new message is received via Socket.io, the unread count in the navigation bar updates immediately

✅ **Messages Read**: When messages are marked as read (either in the current chat or other chats), the count decreases in real-time

✅ **Navigation**: When users navigate between different chats, the count updates appropriately

✅ **Connection State**: The unread count only updates when the socket is properly connected

## Testing

To verify the fix is working:

1. Open the browser console to see debug logs
2. Send a message from another user account
3. Check that the navigation bar unread count updates immediately
4. Mark messages as read and verify the count decreases
5. Navigate between chats and verify counts update correctly

## Debug Information

The enhanced logging provides detailed information:
- `📨 useUnreadMessages: New message received` - When new messages arrive
- `👁️ useUnreadMessages: Messages marked as read` - When read status changes
- `🔢 useUnreadMessages: Unread count calculated` - When count is recalculated

This helps identify if:
- Socket events are being received
- Connection state is correct
- Unread count calculation is working
- Real-time updates are functioning

## Prevention

To prevent similar issues in the future:
1. Always check the return type of hooks when making changes
2. Use TypeScript interfaces to ensure correct property access
3. Test real-time functionality after making socket-related changes
4. Include comprehensive logging for debugging real-time features
