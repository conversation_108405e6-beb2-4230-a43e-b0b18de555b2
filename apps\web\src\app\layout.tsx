import type { <PERSON>ada<PERSON> } from "next";
import { Suspense } from "react";
import { Poppins } from "next/font/google";

import { Providers } from "./providers";

import "./globals.css";

import { FullPageLoadingSpinner } from "@/components/ui/LoadingSpinner";

const poppins = Poppins({
  subsets: ["latin"],
  weight: ["400", "500", "600", "700"],
  variable: "--font-poppins",
});

export const metadata: Metadata = {
  title: "AIMS",
  description: "AIMS",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className={`tw-h-full ${poppins.variable}`}>
      <body className={`${poppins.className} tw-h-full tw-bg-aims-bg`}>
        <Providers>
          <Suspense fallback={<FullPageLoadingSpinner />}>{children}</Suspense>
        </Providers>
      </body>
    </html>
  );
}
