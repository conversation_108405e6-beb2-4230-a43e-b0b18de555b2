"use client";

import { useState } from "react";
import { useParams } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { trpc } from "@/lib/trpc/client";
import { setBasicInfo } from "@/store/slices/campaign";
import { setBasicInfo as setEditBasicInfo } from "@/store/slices/editCampaign";
import { CloudArrowUpIcon, XMarkIcon } from "@heroicons/react/24/outline";
import { useDispatch, useSelector } from "react-redux";
import type { RootState } from "@/store";
import type { CampaignInfo } from "@/store/slices/campaign";

const AdditionalInfo = () => {
  const dispatch = useDispatch();
  const params = useParams();
  const isEditMode = !!params?.id;

  // Select from the appropriate store based on mode
  const basicInfo = useSelector((state: RootState): CampaignInfo =>
    isEditMode ? state.editCampaign.basicInfo : state.campaign.basicInfo,
  );
  const [isUploading, setIsUploading] = useState(false);

  const getUploadUrl = trpc.campaign.getUploadUrl.useMutation();

  const handleFileUpload = async (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsUploading(true);
    try {
      // Get the signed URL for upload
      const { url, publicUrl } = await getUploadUrl.mutateAsync({
        fileType: file.type,
        uploadType: "file",
      });

      // Upload the file to S3
      await fetch(url, {
        method: "PUT",
        body: file,
        headers: {
          "Content-Type": file.type,
        },
      });

      // Update campaign info with the new file
      const updatedFiles = [
        ...(basicInfo.files || []),
        {
          url: publicUrl,
          originalName: file.name,
        },
      ];
      const action = isEditMode ? setEditBasicInfo : setBasicInfo;
      dispatch(
        action({
          ...basicInfo,
          files: updatedFiles,
        }),
      );
    } catch (error) {
      console.error("Error uploading file:", error);
      // You might want to show an error message to the user here
    } finally {
      setIsUploading(false);
    }
  };

  const handleRemoveFile = (index: number) => {
    const newFiles = [...(basicInfo.files || [])];
    newFiles.splice(index, 1);

    // Update campaign info
    const action = isEditMode ? setEditBasicInfo : setBasicInfo;
    dispatch(
      action({
        ...basicInfo,
        files: newFiles,
      }),
    );
  };

  return (
    <div className="tw-mx-auto tw-mt-8 tw-max-w-7xl tw-p-6">
      <div className="tw-space-y-6">
        <h2 className="tw-text-xl tw-font-semibold">Additional Information</h2>

        <div className="tw-space-y-4">
          <div>
            <Label htmlFor="file-upload">Upload Campaign Files</Label>
            <div className="tw-mt-2">
              <Button
                variant="outline"
                className="tw-w-full tw-text-aims-text-primary"
                disabled={isUploading}
                onClick={() => document.getElementById("file-upload")?.click()}
              >
                <CloudArrowUpIcon className="tw-mr-2 tw-h-5 tw-w-5" />
                {isUploading ? "Uploading..." : "Upload File"}
              </Button>
              <input
                id="file-upload"
                type="file"
                onChange={handleFileUpload}
                className="tw-hidden"
              />
            </div>
          </div>

          {basicInfo.files && basicInfo.files.length > 0 && (
            <div className="tw-flex tw-flex-wrap tw-gap-2">
              {basicInfo.files.map(
                (
                  file: { url: string; originalName: string },
                  index: number,
                ) => (
                  <div
                    key={index}
                    className="tw-flex tw-items-center tw-gap-1 tw-rounded-full tw-bg-gray-100 tw-px-3 tw-py-1 tw-text-sm"
                  >
                    <a
                      href={file.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="hover:tw-underline"
                    >
                      {file.originalName}
                    </a>
                    <button
                      onClick={() => handleRemoveFile(index)}
                      className="tw-ml-1 tw-rounded-full tw-p-1 hover:tw-bg-gray-200"
                    >
                      <XMarkIcon className="tw-h-4 tw-w-4" />
                    </button>
                  </div>
                ),
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AdditionalInfo;
