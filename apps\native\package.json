{"name": "@repo/native", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"dev": "cp ../../.env .env & pnpm clean-port & expo start", "add": "npx gluestack-ui add", "clean-port": "npx kill-port 8081", "reset-project": "node ./scripts/reset-project.js", "android": "DARK_MODE=media expo start --android", "ios": "DARK_MODE=media expo start --ios", "web": "DARK_MODE=media expo start --web", "test": "jest --watchAll", "lint": "expo lint"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/html-elements": "0.4.2", "@expo/vector-icons": "^14.0.2", "@gluestack-ui/avatar": "^0.1.18", "@gluestack-ui/button": "^1.0.12", "@gluestack-ui/checkbox": "^0.1.37", "@gluestack-ui/form-control": "^0.1.19", "@gluestack-ui/icon": "^0.1.25", "@gluestack-ui/input": "^0.1.36", "@gluestack-ui/link": "^0.1.27", "@gluestack-ui/nativewind-utils": "^1.0.26", "@gluestack-ui/overlay": "^0.1.20", "@gluestack-ui/pressable": "^0.1.21", "@gluestack-ui/switch": "^0.1.27", "@gluestack-ui/themed": "^1.1.69", "@gluestack-ui/toast": "^1.0.9", "@hookform/resolvers": "^4.1.1", "@legendapp/motion": "^2.4.0", "@react-native-async-storage/async-storage": "^2.1.1", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@reduxjs/toolkit": "^2.5.1", "@repo/server": "workspace:*", "@trpc/client": "11.0.0-rc.730", "@trpc/server": "11.0.0-rc.730", "babel-plugin-module-resolver": "^5.0.2", "expo": "~52.0.37", "expo-blur": "~14.0.3", "expo-constants": "~17.0.7", "expo-font": "~13.0.4", "expo-haptics": "~14.0.1", "expo-image-manipulator": "~13.0.6", "expo-image-picker": "^16.0.6", "expo-linking": "~7.0.5", "expo-router": "~4.0.17", "expo-splash-screen": "~0.29.22", "expo-status-bar": "~2.0.1", "expo-symbols": "~0.2.2", "expo-system-ui": "~4.0.8", "expo-web-browser": "~14.0.2", "input": "^1.0.1", "lucide-react-native": "^0.475.0", "nativewind": "^4.1.23", "react": "18.3.1", "react-dom": "18.3.1", "react-hook-form": "^7.54.2", "react-native": "0.76.7", "react-native-compressor": "0.3.3", "react-native-css-interop": "^0.1.22", "react-native-gesture-handler": "~2.20.2", "react-native-otp-entry": "^1.8.2", "react-native-reanimated": "~3.16.2", "react-native-safe-area-context": "5.2.0", "react-native-screens": "~4.4.0", "react-native-svg": "15.2.0", "react-native-web": "~0.19.13", "react-native-webview": "13.12.5", "react-redux": "^9.2.0", "yup": "^1.4.0", "zod": "^3.24.2"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/jest": "^29.5.12", "@types/react": "18.2.12", "@types/react-test-renderer": "^18.3.0", "eslint": "9.14.0", "jest": "^29.2.1", "jest-expo": "~52.0.4", "jscodeshift": "0.15.2", "postcss": "8.4.49", "react-test-renderer": "18.3.1", "tailwindcss": "3.4.17", "typescript": "5.7.3"}, "private": true}