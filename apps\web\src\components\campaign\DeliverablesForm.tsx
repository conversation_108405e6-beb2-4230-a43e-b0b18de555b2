"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Table } from "@/components/ui/Table";
import {
  DocumentPlusIcon,
  PencilIcon,
  PlusIcon,
  TrashIcon,
} from "@heroicons/react/24/outline";

import {
  DeliverableType,
  getDeliverableTypeLabel,
} from "@repo/server/src/types/deliverable";

import { DeliverableModalForm } from "./DeliverableModalForm";

export interface DeliverableInput {
  name: string;
  daysToComplete: number;
  minimumPayment: number;
  description: string;
  type: DeliverableType;
  // Type-specific fields
  location?: string;
  date?: string;
  time?: string;
  content?: string[];
  productName?: string;
  productPrice?: number;
}

export const deliverableTypeOptions = [
  {
    value: DeliverableType.PHOTO_VIDEO_SHOOT,
    label: "Photo/Video Shoot",
  },
  {
    value: DeliverableType.IN_PERSON,
    label: "In-Person Event",
  },
  {
    value: DeliverableType.CONTENT_CREATION,
    label: "Content Creation",
  },
  {
    value: DeliverableType.CONTENT_SHARE,
    label: "Content Share",
  },
  {
    value: DeliverableType.GIFTED_COLLABORATION,
    label: "Gifted Collaboration",
  },
  {
    value: DeliverableType.CUSTOM,
    label: "Custom",
  },
];

interface DeliverablesFormProps {
  deliverables: DeliverableInput[];
  onAddDeliverable: (d: DeliverableInput) => void;
  onRemoveDeliverable: (index: number) => void;
  onEditDeliverable: (index: number, d: DeliverableInput) => void;
  isEditMode?: boolean;
}

export default function DeliverablesForm({
  deliverables,
  onAddDeliverable,
  onRemoveDeliverable,
  onEditDeliverable,
}: DeliverablesFormProps) {
  const [showForm, setShowForm] = useState(false);
  const [editingIndex, setEditingIndex] = useState<number | null>(null);

  const handleEdit = (index: number) => {
    setEditingIndex(index);
    setShowForm(true);
  };

  const handleDelete = (index: number) => {
    onRemoveDeliverable(index);
  };

  const tableClassName =
    "tw-px-3 tw-py-2 tw-text-left tw-text-sm tw-font-medium tw-text-aims-text-secondary";

  return (
    <div className="tw-px-2">
      {/* Header Section */}
      <div className="tw-text-center tw-mb-8">
        <div className="tw-flex tw-flex-col tw-items-center tw-justify-center tw-mb-4">
          <h2 className="tw-text-2xl tw-font-bold tw-text-aims-text-primary">
            Campaign Deliverables
          </h2>
          <p className="tw-text-base tw-text-aims-dark-6 tw-mt-2">
            List the required deliverables for the campaign with detailed
            descriptions so participants know what&apos;s expected.
          </p>
        </div>
        {deliverables.length > 0 && (
          <Button
            className="tw-bg-green-600 tw-text-aims-text-primary tw-rounded-full tw-px-6 tw-py-3 tw-text-lg tw-font-semibold tw-mt-6 hover:tw-bg-green-700"
            onClick={() => setShowForm(true)}
          >
            <PlusIcon className="tw-h-5 tw-w-5 tw-mr-2" />
            Add new deliverable
          </Button>
        )}
      </div>
      {/* Empty State */}
      {deliverables.length === 0 && !showForm && (
        <div className="tw-bg-aims-dark-2 tw-rounded-2xl tw-p-10 tw-shadow-lg tw-flex tw-flex-col tw-items-center tw-min-h-[350px] tw-max-w-3xl tw-mx-auto">
          <div className="tw-flex tw-flex-col tw-items-center tw-justify-center tw-h-full">
            <div className="tw-mb-8">
              <DocumentPlusIcon className="tw-w-16 tw-h-16 tw-text-aims-dark-6" />
            </div>
            <div className="tw-text-lg tw-font-semibold tw-text-aims-text-primary tw-mb-1">
              Your deliverables will appear here
            </div>
            <div className="tw-text-sm tw-text-aims-dark-6 tw-mb-6">
              Once you start creating deliverables, they&apos;ll be listed here.
            </div>
            <Button
              className="tw-bg-green-600 tw-text-aims-text-primary tw-rounded-full tw-px-6 tw-py-3 tw-text-lg tw-font-semibold hover:tw-bg-green-700"
              onClick={() => setShowForm(true)}
            >
              <PlusIcon className="tw-h-5 tw-w-5 tw-mr-2" />
              Add new deliverable
            </Button>
          </div>
        </div>
      )}

      {/* Add Form */}
      <DeliverableModalForm
        open={showForm}
        onSubmit={(d) => {
          if (editingIndex !== null) {
            onEditDeliverable(editingIndex, d);
            setEditingIndex(null);
          } else {
            onAddDeliverable(d);
          }
          setShowForm(false);
        }}
        onCancel={() => {
          setShowForm(false);
          setEditingIndex(null);
        }}
        initialValues={
          editingIndex !== null ? deliverables[editingIndex] : undefined
        }
      />
      {/* Deliverables List */}
      {deliverables.length > 0 && !showForm && (
        <Table
          columns={[
            {
              header: "Deliverable",
              className: tableClassName,
              cell: (row: DeliverableInput) => (
                <div className="tw-flex tw-flex-col">
                  <div className="tw-font-medium tw-text-aims-text-primary">
                    {row.name}
                  </div>
                </div>
              ),
            },
            {
              header: "Days to complete",
              className: tableClassName,
              cell: (row: DeliverableInput) => (
                <div className="tw-text-sm tw-text-aims-dark-6">
                  {row.daysToComplete}
                </div>
              ),
            },
            {
              header: "Minimum payment",
              className: tableClassName,
              cell: (row: DeliverableInput) => (
                <div className="tw-text-sm tw-text-aims-dark-6">
                  ${row.minimumPayment.toFixed(2)}
                </div>
              ),
            },
            {
              header: "Type",
              className: tableClassName,
              cell: (row: DeliverableInput) => (
                <div className="tw-text-sm tw-text-aims-dark-6">
                  {getDeliverableTypeLabel(row.type)}
                  {row.content && row.content.length > 0 && (
                    <span className="tw-text-aims-dark-6 tw-text-xs tw-ml-1">
                      ({row.content.length})
                    </span>
                  )}
                </div>
              ),
            },
          ]}
          data={deliverables}
          rowKey={(_, idx) => idx}
          actions={(row: DeliverableInput) => {
            const index = deliverables.findIndex((d) => d === row);
            return (
              <div className="tw-flex tw-gap-2">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => handleEdit(index)}
                  className="tw-text-blue-500 hover:tw-text-blue-600"
                >
                  <PencilIcon className="tw-h-4 tw-w-4" />
                  <span className="tw-sr-only">Edit</span>
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => handleDelete(index)}
                  className="tw-text-red-500 hover:tw-text-red-600"
                >
                  <TrashIcon className="tw-h-4 tw-w-4" />
                  <span className="tw-sr-only">Delete</span>
                </Button>
              </div>
            );
          }}
        />
      )}
    </div>
  );
}
