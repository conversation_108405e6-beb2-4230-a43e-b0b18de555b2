import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { SPORTS, UNIVERSITIES } from "@/lib/utils";
import { MagnifyingGlassIcon } from "@heroicons/react/24/outline";

interface AthleteFiltersProps {
  searchTerm: string;
  onSearchChange: (value: string) => void;
  university: string;
  onUniversityChange: (value: string) => void;
  sport: string;
  onSportChange: (value: string) => void;
  yearInSchool: string;
  onYearInSchoolChange: (value: string) => void;
}

const yearOptions = [
  { label: "Any year", value: "any" },
  { label: "Freshman", value: "freshman" },
  { label: "Sophomore", value: "sophomore" },
  { label: "Junior", value: "junior" },
  { label: "Senior", value: "senior" },
  { label: "Graduate", value: "graduate" },
];

export default function AthleteFilters({
  searchTerm,
  onSearchChange,
  university,
  onUniversityChange,
  sport,
  onSportChange,
  yearInSchool,
  onYearInSchoolChange,
}: AthleteFiltersProps) {
  return (
    <div className="tw-space-y-4">
      {/* Search Bar - Full width on mobile */}
      <div className="tw-relative">
        <MagnifyingGlassIcon className="tw-absolute tw-left-3 tw-top-1/2 -tw-translate-y-1/2 tw-h-5 tw-w-5 tw-text-aims-text-secondary" />
        <Input
          type="text"
          placeholder="Search athletes..."
          value={searchTerm}
          onChange={(e) => onSearchChange(e.target.value)}
          className="tw-pl-10 tw-bg-aims-dark-2 tw-border-aims-dark-3 tw-text-aims-text-primary placeholder:tw-text-aims-text-secondary tw-h-12 sm:tw-h-10 tw-text-base"
        />
      </div>

      {/* Filters - Horizontal scroll on mobile, flex on desktop */}
      <div className="tw-flex tw-gap-3 tw-overflow-x-auto tw-pb-2 sm:tw-overflow-x-visible sm:tw-pb-0 sm:tw-gap-4 tw-px-1 sm:tw-px-0">
        <div className="tw-flex-shrink-0">
          <Select value={university} onValueChange={onUniversityChange}>
            <SelectTrigger className="tw-w-[160px] sm:tw-w-[180px] tw-bg-aims-dark-2 tw-border-aims-dark-3 tw-text-aims-text-primary tw-h-12 sm:tw-h-10">
              <SelectValue placeholder="University" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="any">Any university</SelectItem>
              {UNIVERSITIES.map((uni) => (
                <SelectItem key={uni} value={uni}>
                  {uni}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="tw-flex-shrink-0">
          <Select value={sport} onValueChange={onSportChange}>
            <SelectTrigger className="tw-w-[160px] sm:tw-w-[180px] tw-bg-aims-dark-2 tw-border-aims-dark-3 tw-text-aims-text-primary tw-h-12 sm:tw-h-10">
              <SelectValue placeholder="Sport" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="any">Any sport</SelectItem>
              {SPORTS.map((s) => (
                <SelectItem key={s} value={s}>
                  {s}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="tw-flex-shrink-0">
          <Select value={yearInSchool} onValueChange={onYearInSchoolChange}>
            <SelectTrigger className="tw-w-[160px] sm:tw-w-[180px] tw-bg-aims-dark-2 tw-border-aims-dark-3 tw-text-aims-text-primary tw-h-12 sm:tw-h-10">
              <SelectValue placeholder="Year" />
            </SelectTrigger>
            <SelectContent>
              {yearOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );
}
