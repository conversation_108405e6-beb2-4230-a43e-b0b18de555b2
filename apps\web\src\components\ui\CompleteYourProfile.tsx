import { Swiper, SwiperSlide } from "swiper/react";

import ProfileStepCard from "./ProfileStepCard";

import "swiper/css";

export default function CompleteYourProfile({
  profileSteps,
  href,
}: {
  profileSteps: {
    title: string;
    description: string;
    buttonText: string;
    completed: boolean;
  }[];
  href: string;
}) {
  const incompleteSteps = profileSteps.filter((step) => !step.completed);
  const needsScroll = incompleteSteps.length > 3;

  return (
    <div className="tw-bg-aims-dark-2 tw-rounded-lg tw-p-6 tw-mb-8">
      <div className="tw-mb-2 tw-text-aims-text-primary tw-text-lg tw-font-semibold">
        Complete your profile
      </div>
      <div className="tw-text-aims-text-secondary tw-text-sm tw-mb-6">
        By completing your profile, you&apos;ll receive better recommendations based
        on your preferences.
      </div>
      {needsScroll ? (
        <Swiper
          spaceBetween={16}
          slidesPerView={1}
          breakpoints={{
            640: { slidesPerView: 2 },
            1024: { slidesPerView: 3 },
          }}
          className="tw-pb-4"
        >
          {incompleteSteps.map((step, idx) => (
            <SwiperSlide key={idx}>
              <ProfileStepCard {...step} href={href} />
            </SwiperSlide>
          ))}
        </Swiper>
      ) : (
        <div className="tw-grid tw-grid-cols-1 md:tw-grid-cols-3 tw-gap-4">
          {incompleteSteps.map((step, idx) => (
            <ProfileStepCard key={idx} {...step} href={href} />
          ))}
        </div>
      )}
    </div>
  );
}
