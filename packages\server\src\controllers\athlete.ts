import { Types } from "mongoose";

import AthleteModel from "../models/athlete";
import UserModel, { UserType } from "../models/user";
import { deleteFile, generateUploadURL } from "../utils/s3";
import { ExtendedTRPCError } from "../utils/trpc";
import { UploadUrlResponse } from "../validators/profile";

interface PopulatedUser {
  _id: Types.ObjectId;
  name: string;
}

export const createAthleteProfile = async (userId: string, input: any) => {
  try {
    const user = await UserModel.findById(userId);
    if (!user) {
      throw new ExtendedTRPCError("NOT_FOUND", "User not found");
    }

    const newAthlete = new AthleteModel({
      userId: userId,
      name: user.name,
      college: input.basicInfo.college,
      sport: input.basicInfo.sport,
      yearInSchool: input.basicInfo.yearInSchool,
      position: input.basicInfo.position,
      birthDate: new Date(input.basicInfo.birthDate),
      gender: input.basicInfo.gender,
      profilePicture: input.basicInfo.profilePictureUrl
        ? {
            url: input.basicInfo.profilePictureUrl,
            uploadedAt: new Date(),
          }
        : null,
      interests: input.interests,
      socialMedia: input.socialMedia,
      hometown: input.basicInfo.hometown,
      minPayment: input.paymentInfo.minPayment,
      referralSource: input.paymentInfo.referralSource || null,
      referralName: input.paymentInfo.referralName || null,
      referralVenmo: input.paymentInfo.referralVenmo || null,
    });

    await newAthlete.save();
    return { success: true };
  } catch (error) {
    throw error;
  }
};

export const updateAthleteProfile = async (
  userId: string,
  userType: UserType,
  input: any,
) => {
  const profile = await AthleteModel.findOne({ userId });
  if (!profile) {
    throw new ExtendedTRPCError("NOT_FOUND", "Athlete profile not found");
  }
  if (userType !== "athlete") {
    throw new ExtendedTRPCError(
      "FORBIDDEN",
      "Only athlete users can update athlete profiles",
    );
  }

  Object.assign(profile, input);
  await profile.save();
  return profile;
};

export const getUploadUrl = async (
  fileType: string,
): Promise<UploadUrlResponse> => {
  try {
    return await generateUploadURL(fileType, "profile-picture");
  } catch (error) {
    throw new ExtendedTRPCError(
      "INTERNAL_SERVER_ERROR",
      "Failed to generate upload URL",
    );
  }
};

export const updateProfilePicture = async (
  userId: string,
  url: string,
  key: string,
) => {
  const athlete = await AthleteModel.findOne({ userId });
  if (!athlete) {
    throw new ExtendedTRPCError("NOT_FOUND", "Athlete profile not found");
  }

  // Delete old profile picture if exists
  if (athlete.profilePicture?.key) {
    try {
      await deleteFile(athlete.profilePicture.key);
      console.log("Successfully deleted old profile picture");
    } catch (error) {
      console.error("Failed to delete old profile picture:", error);
      // We continue even if deletion fails, to not block the new upload
    }
  }

  athlete.profilePicture = {
    url,
    key,
    uploadedAt: new Date(),
  };

  await athlete.save();
  return { success: true };
};

export const getAthleteProfile = async (userId: string) => {
  const profile = await AthleteModel.findOne({ userId });
  if (!profile) {
    throw new ExtendedTRPCError("NOT_FOUND", "Athlete profile not found");
  }
  return profile;
};

export const getAthleteById = async (userId: string, athleteId: string) => {
  const profile = await AthleteModel.findOne({
    $or: [{ _id: athleteId }, { userId: athleteId }],
  })
    .populate<{ userId: PopulatedUser }>({
      path: "userId",
      select: "name",
      model: "Users",
    })
    .lean();

  if (!profile) {
    throw new ExtendedTRPCError("NOT_FOUND", "Athlete profile not found");
  }

  return {
    ...profile,
    userId: {
      _id: profile.userId._id.toString(),
      name: profile.userId.name,
    },
  };
};

export async function getAthletes(
  userId: string,
  filters?: {
    search?: string;
    university?: string;
    sport?: string;
    yearInSchool?: string;
  },
) {
  const query: any = {};

  if (filters?.university) {
    query.university = filters.university;
  }

  if (filters?.sport) {
    query.sport = filters.sport;
  }

  if (filters?.yearInSchool) {
    query.yearInSchool = filters.yearInSchool;
  }

  let athleteQuery = query;

  // If there's a search term, first find matching users
  if (filters?.search) {
    const matchingUsers = await UserModel.find({
      name: { $regex: filters.search, $options: "i" },
    }).select("_id");

    const userIds = matchingUsers.map((user) => user._id);

    // Add to query to find athletes with matching user IDs or bio
    athleteQuery = {
      ...query,
      $or: [
        { userId: { $in: userIds } },
        { bio: { $regex: filters.search, $options: "i" } },
      ],
    };
  }

  const athletes = await AthleteModel.find(athleteQuery)
    .populate<{ userId: PopulatedUser }>({
      path: "userId",
      select: "name",
      model: "Users",
    })
    .lean();

  // Filter out athletes with null userId or where population failed
  return athletes
    .filter((athlete) => athlete.userId && athlete.userId._id)
    .map((athlete) => ({
      ...athlete,
      userId: {
        _id: athlete.userId._id.toString(),
        name: athlete.userId.name,
      },
    }));
}

export const getAthletesByIds = async (athleteIds: string[]) => {
  const profiles = await AthleteModel.find({ _id: { $in: athleteIds } })
    .populate<{ userId: PopulatedUser }>({
      path: "userId",
      select: "name",
      model: "Users",
    })
    .lean();

  return profiles.map((profile) => ({
    ...profile,
    userId: profile.userId && {
      _id: profile.userId._id.toString(),
      name: profile.userId.name,
    },
  }));
};
