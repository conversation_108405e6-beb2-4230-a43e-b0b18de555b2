import {
  BaseDeliverable,
  DeliverableType,
  getDeliverableTypeLabel,
  GiftedCollaborationDeliverable,
} from "@repo/server/src/types/deliverable";

import { Table } from "./Table";

interface DeliverablesTableProps {
  deliverables: BaseDeliverable[];
  className?: string;
}

export function DeliverablesTable({
  deliverables,
}: DeliverablesTableProps) {
  return (
    <Table
      columns={[
        {
          header: "Name",
          className:
            "tw-py-3.5 tw-pl-4 tw-pr-3 tw-text-left tw-text-sm tw-font-medium tw-text-aims-text-secondary tw-sm:tw-pl-6",
          cell: (deliverable) => (
            <div className="tw-font-medium tw-text-aims-text-primary">
              {deliverable.name}
            </div>
          ),
        },
        {
          header: "Description",
          className:
            "tw-px-3 tw-py-3.5 tw-text-left tw-text-sm tw-font-medium tw-text-aims-text-secondary",
          cell: (deliverable) => (
            <div className="tw-text-sm tw-text-aims-text-secondary">
              {deliverable.description}
            </div>
          ),
        },
        {
          header: "Days to Complete",
          className:
            "tw-px-3 tw-py-3.5 tw-text-left tw-text-sm tw-font-medium tw-text-aims-text-secondary",
          cell: (deliverable) => (
            <div className="tw-text-sm tw-text-aims-text-secondary">
              {deliverable.daysToComplete} days
            </div>
          ),
        },
        {
          header: "Type",
          className:
            "tw-px-3 tw-py-3.5 tw-text-left tw-text-sm tw-font-medium tw-text-aims-text-secondary",
          cell: (deliverable) => (
            <div className="tw-text-sm tw-text-aims-text-secondary">
              {getDeliverableTypeLabel(deliverable.type)}
            </div>
          ),
        },
        {
          header: "Payment",
          className:
            "tw-px-3 tw-py-3.5 tw-text-left tw-text-sm tw-font-medium tw-text-aims-text-secondary",
          cell: (deliverable) => (
            <div className="tw-text-sm tw-text-aims-text-secondary">
              {deliverable.minimumPayment.toLocaleString("en-US", {
                style: "currency",
                currency: "USD",
              })}
            </div>
          ),
        },
        ...(deliverables.some(
          (d) => d.type === DeliverableType.GIFTED_COLLABORATION,
        )
          ? [
              {
                header: "Product Name",
                className:
                  "tw-px-3 tw-py-3.5 tw-text-left tw-text-sm tw-font-medium tw-text-aims-text-secondary",
                cell: (deliverable: BaseDeliverable) => (
                  <div className="tw-text-sm tw-text-aims-text-secondary">
                    {deliverable.type === DeliverableType.GIFTED_COLLABORATION
                      ? (deliverable as GiftedCollaborationDeliverable)
                          .productName
                      : "-"}
                  </div>
                ),
              },
              {
                header: "Product Price",
                className:
                  "tw-px-3 tw-py-3.5 tw-text-left tw-text-sm tw-font-medium tw-text-aims-text-secondary",
                cell: (deliverable: BaseDeliverable) => (
                  <div className="tw-text-sm tw-text-aims-text-secondary">
                    {deliverable.type === DeliverableType.GIFTED_COLLABORATION
                      ? (
                          deliverable as GiftedCollaborationDeliverable
                        ).productPrice?.toLocaleString("en-US", {
                          style: "currency",
                          currency: "USD",
                        })
                      : "-"}
                  </div>
                ),
              },
            ]
          : []),
      ]}
      data={deliverables}
      rowKey={(row, idx) => `${row.name}-${idx}`}
      tableClassName="tw-min-w-full tw-divide-y tw-divide-aims-dark-3"
      theadClassName="tw-bg-aims-dark-3"
      tbodyClassName="tw-divide-y tw-divide-aims-dark-3 tw-bg-aims-dark-2"
    />
  );
}
