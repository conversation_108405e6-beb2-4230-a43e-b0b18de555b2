import { trpc } from "../lib/trpc";
import { authRouter } from "./auth";
import { usersRouter } from "./users";
import { redisRouter } from "./redis";
import { athleteRouter } from "./athlete";
import { brandRouter } from "./brand";
import { campaignRouter } from "./campaign";
import { chatRouter } from "./chat";
import { contractRouter } from "./contract";
import { deliverableRouter } from "./deliverable";
import { deliverableSubmissionRouter } from "./deliverableSubmission";

export const appRouter = trpc.router({
  auth: authRouter,
  users: usersRouter,
  redis: redisRouter,
  athlete: athleteRouter,
  brand: brandRouter,
  campaign: campaignRouter,
  chat: chatRouter,
  contract: contractRouter,
  deliverable: deliverableRouter,
  deliverableSubmission: deliverableSubmissionRouter,
});

export type AppRouter = typeof appRouter;

