"use client";

import React, { useState } from "react";
import { Input } from "@/components/ui/input";
import {
  selectCurrentStep,
  selectInterests,
  updateInterests,
} from "@/store/slices/onboarding";
import { useDispatch, useSelector } from "react-redux";
import { INTERESTS } from "@/lib/utils";

export default function AthleteInterests() {
  const dispatch = useDispatch();
  const currentStep = useSelector(selectCurrentStep);
  const savedInterests = useSelector(selectInterests);
  const selected = savedInterests.businessInterests || [];
  const [input, setInput] = useState("");
  const [touched, setTouched] = useState(false);

  // Filter tags based on input and not already selected
  const filteredTags = INTERESTS.filter(
    (tag) =>
      tag.toLowerCase().includes(input.toLowerCase()) &&
      !selected.includes(tag),
  );

  const handleAddTag = (tag: string) => {
    if (!selected.includes(tag)) {
      dispatch(updateInterests({ businessInterests: [...selected, tag] }));
    }
    setInput("");
    setTouched(true);
  };

  const handleRemoveTag = (tag: string) => {
    dispatch(
      updateInterests({
        businessInterests: selected.filter((t) => t !== tag),
      }),
    );
    setTouched(true);
  };

  const handleInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" && input.trim()) {
      handleAddTag(input.trim());
    }
  };

  if (currentStep !== 2) {
    return null;
  }

  return (
    <div className="tw-mx-auto tw-space-y-6 sm:tw-space-y-8 tw-text-aims-text-primary">
      <div className="tw-text-center tw-px-4 sm:tw-px-0">
        <h1 className="tw-text-xl sm:tw-text-2xl tw-font-semibold tw-mb-2">
          What are your interests?
        </h1>
        <p className="tw-text-sm sm:tw-text-base tw-text-gray-400">
          Select all that apply to help us match you with the right
          opportunities
        </p>
      </div>

      <div className="tw-space-y-4">
        <label className="tw-block tw-text-aims-text-primary tw-font-medium tw-text-sm sm:tw-text-base">
          Interests for opportunity matching*
        </label>

        <div className="tw-space-y-4">
          {/* Selected Tags */}
          <div className="tw-flex tw-flex-wrap tw-gap-2">
            {selected.map((tag) => (
              <span
                key={tag}
                className="tw-bg-aims-primary tw-text-black tw-px-3 tw-py-2 tw-rounded-full tw-flex tw-items-center tw-gap-1 tw-text-sm tw-font-semibold tw-touch-manipulation"
              >
                {tag}
                <button
                  type="button"
                  onClick={() => handleRemoveTag(tag)}
                  className="tw-ml-1 tw-text-black hover:tw-text-red-600 tw-w-5 tw-h-5 tw-flex tw-items-center tw-justify-center tw-touch-manipulation"
                  aria-label={`Remove ${tag}`}
                >
                  ×
                </button>
              </span>
            ))}
          </div>

          {/* Input Field */}
          <Input
            placeholder="Enter a new tag or search for an existing one."
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={handleInputKeyDown}
            onBlur={() => setTouched(true)}
            className="tw-w-full sm:tw-min-w-[300px] tw-h-12 sm:tw-h-10 tw-text-base sm:tw-text-sm"
          />
        </div>
      </div>

      {/* Available Tags */}
      <div className="tw-space-y-3">
        <h3 className="tw-text-sm tw-font-medium tw-text-gray-400">Available interests:</h3>
        <div className="tw-flex tw-flex-wrap tw-gap-2">
          {filteredTags.map((tag) => (
            <button
              key={tag}
              type="button"
              onClick={() => handleAddTag(tag)}
              className={`tw-px-3 tw-py-2 tw-rounded-full tw-text-sm tw-font-semibold tw-border tw-border-gray-600 tw-bg-gray-700 tw-text-gray-200 hover:tw-bg-aims-primary hover:tw-text-black tw-touch-manipulation tw-transition-colors ${
                selected.includes(tag) ? "tw-bg-aims-primary tw-text-black" : ""
              }`}
            >
              {tag}
            </button>
          ))}
        </div>
      </div>

      {touched && selected.length === 0 && (
        <div className="tw-p-3 sm:tw-p-4 tw-bg-red-500/10 tw-border tw-border-red-500 tw-rounded-lg tw-text-red-500 tw-text-sm tw-text-center">
          Please select at least one business interest
        </div>
      )}
    </div>
  );
}
