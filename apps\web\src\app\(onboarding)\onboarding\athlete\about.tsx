"use client";

import { memo, useCallback, useEffect, useMemo, useState } from "react";
import Image from "next/image";
import { Input } from "@/components/ui/input";
import { LoadingSpinner } from "@/components/ui/LoadingSpinner";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { client } from "@/lib/trpc/client";
import { SPORTS, UNIVERSITIES } from "@/lib/utils";
import {
  AthleteBasicInfo,
  selectAthleteBasicInfo,
  selectCurrentStep,
  updateAthleteBasicInfo,
} from "@/store/slices/onboarding";
import { PlusIcon } from "@heroicons/react/24/outline";
import { useDispatch, useSelector } from "react-redux";

// Memoized form field components
const FormField = memo(
  ({
    label,
    required,
    children,
  }: {
    label: string;
    required?: boolean;
    children: React.ReactNode;
  }) => (
    <div className="tw-space-y-2">
      <label className="tw-block tw-text-sm tw-font-medium">
        {label}
        {required && <span className="tw-text-red-500">*</span>}
      </label>
      {children}
    </div>
  ),
);
FormField.displayName = "FormField";

const SelectField = memo(
  ({
    value,
    onChange,
    placeholder,
    options,
  }: {
    value: string;
    onChange: (value: string) => void;
    placeholder: string;
    options: string[];
  }) => (
    <Select value={value} onValueChange={onChange}>
      <SelectTrigger className="!tw-bg-aims-dark-3 tw-border-aims-dark-3 tw-text-aims-text-primary">
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {options.map((option) => (
          <SelectItem key={option} value={option}>
            {option}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  ),
);
SelectField.displayName = "SelectField";

const InputField = memo(
  ({
    type = "text",
    value,
    onChange,
    placeholder,
  }: {
    type?: string;
    value: string;
    onChange: (value: string) => void;
    placeholder?: string;
  }) => (
    <Input
      type={type}
      defaultValue={value}
      onChange={(e) => onChange(e.target.value)}
      placeholder={placeholder}
    />
  ),
);
InputField.displayName = "InputField";

const TextareaField = memo(
  ({
    value,
    onChange,
    placeholder,
  }: {
    value: string;
    onChange: (value: string) => void;
    placeholder?: string;
  }) => (
    <Textarea
      placeholder={placeholder}
      defaultValue={value}
      onChange={(e) => onChange(e.target.value)}
      className="tw-h-32 tw-bg-aims-dark-3 tw-border-aims-dark-3 tw-text-aims-text-primary"
    />
  ),
);
TextareaField.displayName = "TextareaField";

const RadioField = memo(
  ({
    name,
    value,
    checked,
    onChange,
    label,
  }: {
    name: string;
    value: string;
    checked: boolean;
    onChange: (value: string) => void;
    label: string;
  }) => (
    <label className="tw-flex tw-items-center tw-gap-2">
      <input
        type="radio"
        name={name}
        value={value}
        checked={checked}
        onChange={(e) => onChange(e.target.value)}
        className="tw-text-aims-primary tw-bg-aims-dark-3"
      />
      {label}
    </label>
  ),
);
RadioField.displayName = "RadioField";

export default function AthleteAbout() {
  const dispatch = useDispatch();
  const currentStep = useSelector(selectCurrentStep);
  const basicInfo = useSelector(selectAthleteBasicInfo);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState("");

  // Initialize form data from Redux state
  const [formData, setFormData] = useState(basicInfo);

  // Memoize the form validation
  const isFormValid = useMemo(() => {
    const requiredFields = [
      "college",
      "sport",
      "yearInSchool",
      "birthDate",
      "gender",
    ];
    return requiredFields.every((field) =>
      Boolean(formData[field as keyof AthleteBasicInfo]),
    );
  }, [formData]);

  // Update Redux when moving to next step
  useEffect(() => {
    if (currentStep !== 1 && isFormValid) {
      dispatch(updateAthleteBasicInfo(formData));
    }
  }, [currentStep, dispatch, formData, isFormValid]);

  // Optimize field change handler
  const handleFieldChange = useCallback(
    (field: keyof AthleteBasicInfo, value: string) => {
      setFormData((prev) => ({ ...prev, [field]: value }));
    },
    [],
  );

  // Memoize handlers for each field type
  const handleCollegeChange = useCallback(
    (value: string) => {
      handleFieldChange("college", value);
    },
    [handleFieldChange],
  );

  const handleSportChange = useCallback(
    (value: string) => {
      handleFieldChange("sport", value);
    },
    [handleFieldChange],
  );

  const handleYearChange = useCallback(
    (value: string) => {
      handleFieldChange("yearInSchool", value);
    },
    [handleFieldChange],
  );

  const handlePositionChange = useCallback(
    (value: string) => {
      handleFieldChange("position", value);
    },
    [handleFieldChange],
  );

  const handleBirthDateChange = useCallback(
    (value: string) => {
      handleFieldChange("birthDate", value);
    },
    [handleFieldChange],
  );

  const handleGenderChange = useCallback(
    (value: string) => {
      handleFieldChange("gender", value);
    },
    [handleFieldChange],
  );

  const handleBioChange = useCallback(
    (value: string) => {
      handleFieldChange("bio", value);
    },
    [handleFieldChange],
  );

  const handleHometownChange = useCallback(
    (value: string) => {
      handleFieldChange("hometown", value);
    },
    [handleFieldChange],
  );

  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validate file size (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
      setUploadError("File size must be less than 10MB");
      return;
    }

    try {
      setIsUploading(true);
      setUploadError("");

      // Get presigned URL
      const { url, key, publicUrl } = await client.athlete.getUploadUrl.mutate({
        fileType: file.type,
      });

      // Upload to S3
      const uploadResponse = await fetch(url, {
        method: "PUT",
        body: file,
        headers: {
          "Content-Type": file.type,
        },
      });

      if (!uploadResponse.ok) throw new Error("Failed to upload image");

      // Update profile with new image
      await client.athlete.updateProfilePicture.mutate({
        key,
        url: publicUrl,
      });

      setFormData((prev) => ({
        ...prev,
        profilePictureUrl: publicUrl,
      }));
    } catch (error) {
      setUploadError(
        error instanceof Error ? error.message : "Failed to upload image",
      );
    } finally {
      setIsUploading(false);
    }
  };

  if (currentStep !== 1) {
    return null;
  }

  return (
    <div className="tw-space-y-6 sm:tw-space-y-8 tw-text-aims-text-primary">
      <div className="tw-text-center">
        <h1 className="tw-text-xl sm:tw-text-2xl tw-font-semibold tw-mb-2">
          Tell us about yourself
        </h1>
      </div>

      <div className="tw-grid tw-grid-cols-1 lg:tw-grid-cols-2 tw-gap-6 lg:tw-gap-8">
        <div className="tw-space-y-4 sm:tw-space-y-6 tw-order-2 lg:tw-order-1">
          <FormField label="College/University" required>
            <SelectField
              value={formData.college}
              onChange={handleCollegeChange}
              placeholder="Select College/University"
              options={UNIVERSITIES}
            />
          </FormField>

          <FormField label="Sport" required>
            <SelectField
              value={formData.sport}
              onChange={handleSportChange}
              placeholder="Select Sport"
              options={SPORTS}
            />
          </FormField>

          <FormField label="Year In School" required>
            <SelectField
              value={formData.yearInSchool || ""}
              onChange={handleYearChange}
              placeholder="Select Year"
              options={[
                "freshman",
                "sophomore",
                "junior",
                "senior",
                "graduate",
              ]}
            />
          </FormField>

          <FormField label="Position/Event">
            <InputField
              value={formData.position}
              onChange={handlePositionChange}
            />
          </FormField>

          <FormField label="Birthday" required>
            <InputField
              type="date"
              value={formData.birthDate}
              onChange={handleBirthDateChange}
            />
          </FormField>
          <FormField label="Hometown">
            <InputField
              value={formData.hometown || ""}
              onChange={handleHometownChange}
              placeholder="Enter your hometown"
            />
          </FormField>

          <FormField label="Gender" required>
            <div className="tw-flex tw-gap-4">
              <RadioField
                name="gender"
                value="male"
                checked={formData.gender === "male"}
                onChange={handleGenderChange}
                label="Male"
              />
              <RadioField
                name="gender"
                value="female"
                checked={formData.gender === "female"}
                onChange={handleGenderChange}
                label="Female"
              />
            </div>
          </FormField>

          <FormField label="Bio">
            <TextareaField
              value={formData.bio || ""}
              onChange={handleBioChange}
              placeholder="Tell us about yourself..."
            />
          </FormField>

        </div>

        {/* Profile Picture Upload */}
        <div className="tw-flex tw-justify-center tw-order-1 lg:tw-order-2">
          <div
            className="tw-cursor-pointer tw-h-[200px] tw-w-[200px] sm:tw-h-[250px] sm:tw-w-[250px] lg:tw-h-[300px] lg:tw-w-[300px] tw-bg-aims-dark-3 tw-rounded-full tw-flex tw-flex-col tw-items-center tw-justify-center tw-border-2 tw-border-dashed tw-border-gray-600 tw-relative tw-overflow-hidden tw-touch-manipulation"
            onClick={() =>
              !isUploading && document.getElementById("profile-upload")?.click()
            }
          >
            {isUploading ? (
              <div className="tw-flex tw-items-center tw-justify-center tw-w-full tw-h-full">
                <LoadingSpinner />
              </div>
            ) : formData.profilePictureUrl ? (
              <div className="tw-w-full tw-h-full tw-relative">
                <Image
                  src={formData.profilePictureUrl}
                  alt="Profile"
                  fill
                  sizes="(max-width: 640px) 200px, (max-width: 1024px) 250px, 300px"
                  className="tw-object-cover tw-rounded-full"
                />
                <div className="tw-absolute tw-inset-0 tw-bg-black tw-bg-opacity-50 tw-flex tw-items-center tw-justify-center tw-opacity-0 hover:tw-opacity-100 tw-transition-opacity tw-rounded-full">
                  <p className="tw-text-aims-text-primary tw-text-xs sm:tw-text-sm">Change photo</p>
                </div>
              </div>
            ) : (
              <div className="tw-text-center tw-p-4 sm:tw-p-6">
                <PlusIcon className="tw-mx-auto tw-h-8 tw-w-8 sm:tw-h-12 sm:tw-w-12 tw-text-gray-400" />
                <p className="tw-mt-2 tw-text-xs sm:tw-text-sm tw-text-gray-400">
                  Click to upload
                </p>
                <p className="tw-text-xs tw-text-gray-500">
                  PNG, JPG, GIF up to 10MB
                </p>
              </div>
            )}
            <input
              id="profile-upload"
              type="file"
              className="tw-hidden"
              accept="image/jpeg,image/png,image/gif"
              onChange={handleFileUpload}
              disabled={isUploading}
            />
          </div>
        </div>
      </div>

      {uploadError && (
        <div className="tw-p-3 sm:tw-p-4 tw-bg-red-500/10 tw-border tw-border-red-500 tw-rounded-lg tw-text-red-500 tw-text-sm tw-text-center">
          {uploadError}
        </div>
      )}
    </div>
  );
}
