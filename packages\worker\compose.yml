services:
  ai-agent:
    env_file:
      - ./.env
    # Environment variables are loaded from .env file and processed by entrypoint.sh
    build:
      context: ./src/agent
    stdin_open: true
    tty: true
    volumes:
      # Mount specific files for hot reloading, but exclude entrypoint.sh
      - ./src/agent/worker.py:/app/worker.py
      - ./src/agent/internet_search_agent.py:/app/internet_search_agent.py
    restart: unless-stopped
    deploy:
      mode: replicated
      replicas: 1
      resources:
        limits:
          cpus: "1"
          memory: 2000M
    extra_hosts:
      - "host.docker.internal:host-gateway"
