"use client";

import { useState } from "react";
import Image from "next/image";
import { useParams } from "next/navigation";
import CampaignCard from "@/components/ui/CampaignCard";
import {
  FullPageLoadingSpinner,
} from "@/components/ui/LoadingSpinner";
import SendMessageButton from "@/components/ui/SendMessageButton";
import { trpc } from "@/lib/trpc/client";

import type { SerializedBrandProfile } from "@repo/server/src/types/brand";
import type { SerializedCampaign } from "@repo/server/src/types/campaign";
import { CampaignStatus } from "@repo/server/src/types/campaign";
import { useAuth } from "@/hooks/use-auth";

export default function BrandPage() {
  const { id } = useParams();
  const { user } = useAuth();
  const { data: brand, isLoading: isBrandLoading } =
    trpc.brand.getBrand.useQuery(id as string) as {
      data: SerializedBrandProfile | undefined;
      isLoading: boolean;
    };
  const { data: campaigns, isLoading: isCampaignsLoading } =
    trpc.campaign.getCampaignsByBrandId.useQuery({
      brandId: id as string,
    }) as {
      data: SerializedCampaign[] | undefined;
      isLoading: boolean;
    };

  const [activeTab, setActiveTab] = useState<"details" | "active">("details");
  const isLoading = isBrandLoading || isCampaignsLoading;

  // Check if the current user is viewing their own brand profile
  const isOwnProfile = user && brand && user.id === brand.userId;

  if (isLoading) {
    return <FullPageLoadingSpinner />;
  }

  const activeCampaigns = campaigns?.filter(
    (campaign) => campaign.status === CampaignStatus.ACTIVE,
  );
  const completedCampaigns = campaigns?.filter(
    (campaign) => campaign.status === CampaignStatus.COMPLETED,
  );
  const activeParticipants = activeCampaigns?.reduce(
    (acc, campaign) => acc + (campaign.athletes?.length ?? 0),
    0,
  );

  if (!brand) {
    return (
      <div className="tw-flex tw-justify-center tw-items-center tw-min-h-[200px] tw-p-4">
        <div className="tw-text-aims-text-secondary tw-text-center">
          <div className="tw-text-lg tw-mb-2">Brand not found</div>
          <div className="tw-text-sm">The brand you&apos;re looking for doesn&apos;t exist or has been removed.</div>
        </div>
      </div>
    );
  }
  return (
    <div className="tw-min-h-screen tw-p-4 sm:tw-p-6 tw-text-aims-text-primary">
      {/* Header */}
      <div className="tw-flex tw-flex-col sm:tw-flex-row tw-items-start sm:tw-items-center tw-justify-between tw-gap-4 tw-mb-4 sm:tw-mb-6">
        <div className="tw-flex tw-items-center tw-flex-1 tw-min-w-0">
          <div className="tw-relative tw-w-16 tw-h-16 sm:tw-w-20 sm:tw-h-20 tw-rounded-lg tw-mr-3 sm:tw-mr-4 tw-flex-shrink-0">
            <Image
              src={brand.logo.url || "/no-profile-pic.jpg"}
              alt={brand.companyName}
              fill
              className="tw-object-cover tw-rounded-lg"
              sizes="(max-width: 640px) 64px, 80px"
              quality={100}
              priority
            />
          </div>
          <div className="tw-min-w-0 tw-flex-1">
            <h1 className="tw-text-xl sm:tw-text-2xl tw-font-bold tw-leading-tight tw-mb-1">
              {brand.companyName}
            </h1>
            {/* <div className="tw-flex tw-items-center tw-gap-2">
              <FaStar className="tw-text-yellow-400" />
              <span className="tw-font-semibold">{brand.rating}</span>
              <span className="tw-text-gray-400">
                ({brand.reviews} reviews)
              </span>
            </div> */}
            {brand.website && (
              <a
                href={brand.website}
                target="_blank"
                rel="noopener noreferrer"
                className="tw-text-blue-400 tw-text-sm tw-inline-flex tw-items-center tw-min-h-[44px] tw-break-all"
              >
                {brand.website}
              </a>
            )}
          </div>
        </div>
        {!isOwnProfile && (
          <div className="tw-flex tw-items-center tw-w-full sm:tw-w-auto">
            <SendMessageButton
              targetUserId={brand.userId}
              targetName={brand.companyName}
              variant="default"
              className="tw-text-black tw-w-full sm:tw-w-auto tw-h-12 sm:tw-h-10"
            />
          </div>
        )}
      </div>

      {/* Tabs */}
      <div className="tw-flex tw-gap-4 sm:tw-gap-8 tw-mb-4 sm:tw-mb-6 tw-border-b tw-border-gray-700 tw-overflow-x-auto tw-pb-2 sm:tw-pb-0">
        <button
          className={`tw-pb-2 tw-font-semibold tw-text-sm sm:tw-text-base tw-min-h-[44px] tw-flex tw-items-center tw-whitespace-nowrap tw-px-1 tw-transition-colors ${
            activeTab === "details"
              ? "tw-border-b-2 tw-border-aims-primary tw-text-aims-text-primary"
              : "tw-text-aims-text-secondary hover:tw-text-aims-text-primary"
          }`}
          onClick={() => setActiveTab("details")}
        >
          Brand details
        </button>
        <button
          className={`tw-pb-2 tw-font-semibold tw-text-sm sm:tw-text-base tw-min-h-[44px] tw-flex tw-items-center tw-whitespace-nowrap tw-px-1 tw-transition-colors ${
            activeTab === "active"
              ? "tw-border-b-2 tw-border-aims-primary tw-text-aims-text-primary"
              : "tw-text-aims-text-secondary hover:tw-text-aims-text-primary"
          }`}
          onClick={() => setActiveTab("active")}
        >
          Active Campaigns
        </button>
        {/* <button className="tw-pb-2 tw-text-aims-text-secondary">Review</button> */}
      </div>
      {activeTab === "details" && (
        <>
          {/* Stats Cards */}
          <div className="tw-grid tw-grid-cols-1 sm:tw-grid-cols-3 tw-gap-4 sm:tw-gap-6 tw-mb-4 sm:tw-mb-6">
            <div className="tw-bg-aims-dark-2 tw-p-4 sm:tw-p-6 tw-rounded-lg">
              <div className="tw-text-aims-dark-7 tw-text-sm tw-mb-1">Active campaigns</div>
              <div className="tw-text-xl sm:tw-text-2xl tw-font-bold">
                {activeCampaigns?.length || 0}
              </div>
            </div>
            <div className="tw-bg-aims-dark-2 tw-p-4 sm:tw-p-6 tw-rounded-lg">
              <div className="tw-text-aims-dark-7 tw-text-sm tw-mb-1">Completed campaigns</div>
              <div className="tw-text-xl sm:tw-text-2xl tw-font-bold">
                {completedCampaigns?.length || 0}
              </div>
            </div>
            <div className="tw-bg-aims-dark-2 tw-p-4 sm:tw-p-6 tw-rounded-lg">
              <div className="tw-text-aims-dark-7 tw-text-sm tw-mb-1">Active participants</div>
              <div className="tw-text-xl sm:tw-text-2xl tw-font-bold">
                {activeParticipants || 0}
              </div>
            </div>
          </div>

          {/* About the business */}
          <div className="tw-bg-aims-dark-2 tw-p-4 sm:tw-p-6 tw-rounded-lg tw-mb-4 sm:tw-mb-6">
            <h3 className="tw-text-base sm:tw-text-lg tw-font-semibold tw-mb-3 sm:tw-mb-2">
              About the business
            </h3>
            <div className="tw-mb-4">
              <div className="tw-text-aims-dark-6 tw-text-sm tw-mb-2">
                Description
              </div>
              <div className="tw-text-aims-text-primary tw-text-sm sm:tw-text-base tw-leading-relaxed">
                {brand.description || "No description available."}
              </div>
            </div>
            {/* <div>
            <span className="tw-font-semibold">Sports Preferences:</span>
            <div className="tw-flex tw-gap-2 tw-mt-2">
                {brand..map((sport: string) => (
                <span
                    key={sport}
                    className="tw-bg-gray-700 tw-px-3 tw-py-1 tw-rounded-full tw-text-sm"
                >
                    {sport}
                </span>
                ))}
            </div>
            </div> */}
          </div>

          {/* Social Medias */}
          {/* <div className="tw-bg-aims-dark-2 tw-p-6 tw-rounded-lg tw-w-full tw-max-w-xs tw-ml-auto">
            <h4 className="tw-font-semibold tw-mb-4">Social medias</h4>
            <div className="tw-flex tw-flex-col tw-gap-3">
              <div className="tw-flex tw-items-center tw-gap-2">
                <span>@handle</span>
                <span className="tw-ml-auto"> followers</span>
              </div>
            </div>
          </div> */}
        </>
      )}
      {activeTab === "active" && (
        <>
          {/* Active Campaigns */}
          {activeCampaigns?.length ? (
            <div className="tw-grid tw-grid-cols-1 sm:tw-grid-cols-2 lg:tw-grid-cols-3 tw-gap-4 sm:tw-gap-6">
              {activeCampaigns.map((campaign) => (
                <CampaignCard
                  key={campaign.id}
                  campaign={campaign}
                  refetchCampaigns={() => {}}
                  refetchApplications={() => {}}
                  brandView={user?.userType === "brand"}
                />
              ))}
            </div>
          ) : (
            <div className="tw-flex tw-justify-center tw-items-center tw-min-h-[200px] tw-p-4">
              <div className="tw-text-aims-text-secondary tw-text-center">
                <div className="tw-text-base tw-mb-2">No active campaigns</div>
                <div className="tw-text-sm">This brand doesn&apos;t have any active campaigns at the moment.</div>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
}
