"use client";

import { useEffect } from "react";
import DeliverablesForm from "@/components/campaign/DeliverablesForm";
import {
  addDeliverable,
  editDeliverable,
  removeDeliverable,
  selectBasicInfo,
  setCurrentStep,
} from "@/store/slices/campaign";
import { useDispatch, useSelector } from "react-redux";

export default function DeliverablesPage() {
  const dispatch = useDispatch();
  const basicInfo = useSelector(selectBasicInfo);

  useEffect(() => {
    dispatch(setCurrentStep(3));
  }, [dispatch]);

  return (
    <DeliverablesForm
      deliverables={basicInfo.deliverables}
      onAddDeliverable={(d) => dispatch(addDeliverable(d))}
      onRemoveDeliverable={(i) => dispatch(removeDeliverable(i))}
      onEditDeliverable={(i, d) =>
        dispatch(editDeliverable({ index: i, deliverable: d }))
      }
      isEditMode={false}
    />
  );
}
