import React from "react";

interface EmailTemplateProps {
  children: React.ReactNode;
  email: string;
}

export const EmailTemplate: React.FC<EmailTemplateProps> = ({
  children,
  email,
}) => {
  return (
    <html>
      <head>
        {/* Google Fonts for Poppins */}
        <link
          href="https://fonts.googleapis.com/css?family=Poppins:400,600,700&display=swap"
          rel="stylesheet"
        />
      </head>
      <body
        style={{
          backgroundColor: "#111928",
          fontFamily: "'Poppins', Arial, sans-serif",
          margin: 0,
          padding: 0,
          color: "#FFFFFF",
        }}
      >
        <div
          style={{
            backgroundColor: "#1F2A37",
            width: "100%",
            maxWidth: "40rem",
            margin: "40px auto",
            borderRadius: "10px",
            boxShadow: "0px 4px 16px rgba(0,0,0,0.12)",
            padding: "0 0 2rem 0",
            opacity: 1,
          }}
        >
          <div
            style={{
              width: "90%",
              margin: "auto",
              paddingTop: "2.5rem",
              paddingBottom: "1rem",
            }}
          >
            <img
              src="https://aimsmarketing.ai/_next/image?url=%2Faims-icon-transparent.png&w=256&q=75"
              alt="AIMS Logo"
              style={{
                width: "7rem",
                marginBottom: "1.5rem",
                display: "block",
                marginLeft: "auto",
                marginRight: "auto",
              }}
            />
            <div
              style={{
                backgroundColor: "#111928",
                borderRadius: "10px",
                padding: "24px 20px 20px 20px",
                boxShadow: "0px 2px 8px rgba(0,0,0,0.08)",
                marginBottom: "1.5rem",
              }}
            >
              {children}
            </div>
            <div
              style={{
                textAlign: "center",
                color: "#DEE4EE",
                fontSize: "0.8rem",
                backgroundColor: "#1F2A37",
                borderRadius: "8px",
                padding: "1rem 0 0 0",
              }}
            >
              <p style={{ margin: 0 }}>
                Need help?{" "}
                <a
                  href="https://aimsmarketing.ai/"
                  style={{
                    color: "#FC8D00",
                    textDecoration: "none",
                    fontWeight: 600,
                  }}
                >
                  Contact Us | aimsmarketing.ai
                </a>
              </p>
              <br />
              <p style={{ margin: 0 }}>
                This email was sent to{" "}
                <span style={{ color: "#FC8D00" }}>{email}</span>
              </p>
              <p style={{ margin: 0 }}>
                AIMS | 1555 Freedom Blvd 200 W, Provo, UT 84604 | USA
              </p>
            </div>
          </div>
        </div>
      </body>
    </html>
  );
};
