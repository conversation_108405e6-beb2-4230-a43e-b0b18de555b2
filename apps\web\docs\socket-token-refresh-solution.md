# Socket.io Token Refresh Solution

## Overview

This document describes the comprehensive Socket.io token refresh solution implemented to handle JWT token expiration gracefully in the Next.js messaging application.

## Problem Statement

Previously, when JWT access tokens expired (after 15 minutes of inactivity), users would encounter "Authentication error: Token has expired" when attempting to send messages through Socket.io. This occurred because:

1. Socket.io connections used a static token set during initial connection
2. No automatic token refresh mechanism existed for Socket.io
3. Users received cryptic error messages instead of seamless reconnection

## Solution Components

### 1. SocketTokenManager (`apps/web/src/lib/utils/socket-token-manager.ts`)

A utility class that handles token refresh specifically for Socket.io connections:

- **Token Retrieval**: Gets current tokens from localStorage with Redux fallback
- **Token Refresh**: Uses existing tRPC refresh endpoint to obtain new tokens
- **Error Detection**: Identifies token expiration and authentication errors
- **Concurrency Control**: Prevents multiple simultaneous refresh attempts

### 2. Enhanced Socket Hook (`apps/web/src/hooks/use-socket.ts`)

Completely rewritten socket hook with comprehensive token management:

- **Connection States**: Tracks detailed connection status (connecting, connected, reconnecting, etc.)
- **Automatic Reconnection**: Detects token expiration and automatically refreshes tokens
- **Error Handling**: Provides specific error handling for different failure scenarios
- **Connection Management**: Manual reconnection controls and connection status tracking

### 3. Connection Status Components (`apps/web/src/components/ui/SocketConnectionStatus.tsx`)

UI components for displaying connection status to users:

- **Status Indicator**: Shows current connection state with appropriate icons
- **Error Alerts**: User-friendly error messages with retry options
- **Connection Dot**: Compact status indicator for headers/status bars

### 4. Enhanced Chat Components

Updated chat components to handle connection status:

- **Connection Feedback**: Shows connection status in chat interface
- **Send Button State**: Disables sending when disconnected
- **Error Messages**: Provides specific feedback for connection issues

## Key Features

### Automatic Token Refresh

When a token expires:
1. Socket connection detects authentication error
2. SocketTokenManager refreshes tokens using existing tRPC mechanism
3. New tokens are stored in localStorage
4. Socket automatically reconnects with fresh tokens
5. User continues messaging seamlessly

### Connection Status Tracking

The solution tracks multiple connection states:
- `DISCONNECTED`: No active connection
- `CONNECTING`: Establishing initial connection
- `CONNECTED`: Successfully connected and authenticated
- `RECONNECTING`: Attempting to reconnect after disconnection
- `TOKEN_REFRESHING`: Refreshing expired authentication tokens
- `AUTH_ERROR`: Authentication failed (requires user action)
- `CONNECTION_ERROR`: Network or server connection issues

### User Experience Improvements

- **Seamless Messaging**: Users can continue messaging even when tokens expire
- **Clear Feedback**: Connection status is visible in the UI
- **Error Recovery**: Automatic retry mechanisms with manual fallback options
- **Graceful Degradation**: Messaging functionality remains available during reconnection

## Implementation Details

### Token Refresh Flow

```typescript
// 1. Detect token expiration
if (SocketTokenManager.isTokenExpirationError(error)) {
  // 2. Refresh tokens
  const refreshResult = await SocketTokenManager.refreshTokens();
  
  // 3. Reconnect with new tokens
  if (refreshResult.success) {
    connectSocket(); // Reconnect with fresh tokens
  }
}
```

### Connection State Management

```typescript
// Update connection state
updateConnectionState({
  status: SocketConnectionStatus.TOKEN_REFRESHING,
  isTokenRefreshing: true,
});

// After successful refresh
updateConnectionState({
  status: SocketConnectionStatus.CONNECTED,
  isConnected: true,
  isTokenRefreshing: false,
  reconnectAttempts: 0,
});
```

### Error Handling

The solution provides specific error handling for:
- Token expiration errors
- Network connectivity issues
- Server authentication failures
- Connection timeouts

## Configuration

### Token Expiration Settings

Current configuration in `.env`:
```bash
JWT_EXPIRES_IN="15m"  # Access token expires in 15 minutes
```

### Reconnection Settings

Socket configuration:
```typescript
{
  reconnection: false,        // Manual reconnection control
  reconnectionAttempts: 5,    // Maximum retry attempts
  reconnectionDelay: 1000,    // Initial delay between retries
  timeout: 20000,            // Connection timeout
}
```

## Testing the Solution

### Manual Testing

1. **Token Expiration Test**:
   - Log in to the application
   - Wait 15+ minutes for token to expire
   - Attempt to send a message
   - Verify automatic token refresh and seamless messaging

2. **Connection Recovery Test**:
   - Disconnect network connection
   - Attempt to send messages
   - Reconnect network
   - Verify automatic reconnection

3. **UI Feedback Test**:
   - Observe connection status indicators
   - Verify error messages are user-friendly
   - Test manual reconnection buttons

### Automated Testing

The solution can be tested by:
- Mocking token expiration scenarios
- Simulating network disconnections
- Testing token refresh API calls
- Verifying UI state updates

## Benefits

1. **Improved User Experience**: No more cryptic error messages or failed message sends
2. **Seamless Authentication**: Automatic token refresh without user intervention
3. **Better Visibility**: Clear connection status feedback
4. **Robust Error Handling**: Graceful handling of various failure scenarios
5. **Maintainable Code**: Well-structured, reusable components

## Future Enhancements

Potential improvements:
- Proactive token refresh before expiration
- Offline message queuing
- Advanced retry strategies
- Connection quality indicators
- Performance monitoring and analytics

## Conclusion

This solution eliminates the token expiration errors that users previously experienced when sending messages after periods of inactivity. The implementation provides a robust, user-friendly messaging experience with automatic token refresh and comprehensive error handling.
