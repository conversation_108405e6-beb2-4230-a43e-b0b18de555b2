"use client";

import { useEffect } from "react";
import ReviewCampaign from "@/components/campaign/Review";
import { setCurrentStep } from "@/store/slices/campaign";
import { useDispatch } from "react-redux";

export default function ReviewPage() {
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(setCurrentStep(5));
  }, [dispatch]);

  return <ReviewCampaign isEditing={false} />;
}
