"use client";

import { useEffect } from "react";
import DeliverablesForm from "@/components/campaign/DeliverablesForm";
import {
  addDeliverable,
  editDeliverable,
  removeDeliverable,
  selectEditBasicInfo,
  setCurrentStep,
} from "@/store/slices/editCampaign";
import { useDispatch, useSelector } from "react-redux";

export default function EditDeliverablesPage() {
  const dispatch = useDispatch();
  useEffect(() => {
    dispatch(setCurrentStep(3));
  }, [dispatch]);
  const basicInfo = useSelector(selectEditBasicInfo);

  return (
    <DeliverablesForm
      deliverables={basicInfo.deliverables}
      onAddDeliverable={(d) => dispatch(addDeliverable(d))}
      onRemoveDeliverable={(i) => dispatch(removeDeliverable(i))}
      onEditDeliverable={(i, d) =>
        dispatch(editDeliverable({ index: i, deliverable: d }))
      }
      isEditMode={true}
    />
  );
}
