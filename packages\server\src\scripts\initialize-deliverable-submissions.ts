import mongoose from "mongoose";
import ContractModel from "../models/contract";
import { DeliverableSubmissionModel } from "../models/deliverable";
import { ContractStatus } from "../types/contract";
import { DeliverableSubmissionStatus } from "../types/deliverableSubmission";

/**
 * Migration script to initialize deliverable submissions for existing contracts
 * that are in AWAITING_DELIVERABLES status but don't have submission records
 */
async function initializeDeliverableSubmissions() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI!);
    console.log("Connected to MongoDB");

    // Find all contracts in AWAITING_DELIVERABLES status
    const awaitingContracts = await ContractModel.find({
      status: ContractStatus.AWAITING_DELIVERABLES
    });
    
    console.log(`Found ${awaitingContracts.length} contracts in AWAITING_DELIVERABLES status`);
    
    if (awaitingContracts.length === 0) {
      console.log("No contracts to process.");
      return;
    }
    
    let processedCount = 0;
    let submissionsCreated = 0;
    
    // Process each contract
    for (const contract of awaitingContracts) {
      console.log(`Processing contract ${contract._id} (${contract.title})`);
      
      // Create placeholder submissions for each deliverable in the contract
      for (const deliverable of contract.terms.deliverables) {
        // Check if submission already exists
        const existingSubmission = await DeliverableSubmissionModel.findOne({
          campaignId: contract.campaignId,
          deliverableId: deliverable.deliverableId,
          athleteId: contract.athleteId,
        });

        // Only create if no submission exists
        if (!existingSubmission) {
          await DeliverableSubmissionModel.create({
            campaignId: contract.campaignId,
            deliverableId: deliverable.deliverableId,
            athleteId: contract.athleteId,
            description: "", // Empty description for placeholder
            files: [],
            status: DeliverableSubmissionStatus.AWAITING_SUBMISSION,
            submittedAt: new Date(), // Set to current time for sorting purposes
          });
          
          submissionsCreated++;
          console.log(`  Created AWAITING_SUBMISSION record for deliverable ${deliverable.name}`);
        } else {
          console.log(`  Submission already exists for deliverable ${deliverable.name} (status: ${existingSubmission.status})`);
        }
      }
      
      processedCount++;
    }
    
    console.log(`\nMigration completed successfully!`);
    console.log(`- Processed ${processedCount} contracts`);
    console.log(`- Created ${submissionsCreated} new submission records`);
    
  } catch (error) {
    console.error("Migration failed:", error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log("Disconnected from MongoDB");
  }
}

// Run the migration if this script is executed directly
if (require.main === module) {
  initializeDeliverableSubmissions()
    .then(() => {
      console.log("Migration script completed");
      process.exit(0);
    })
    .catch((error) => {
      console.error("Migration script failed:", error);
      process.exit(1);
    });
}

export { initializeDeliverableSubmissions };
