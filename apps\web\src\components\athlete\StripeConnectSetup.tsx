"use client";

import { useState } from "react";
import { trpc } from "@/lib/trpc/client";
import { But<PERSON> } from "../ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "../ui/card";
import { Badge } from "../ui/badge";
import { useToast } from "../ui/toast/use-toast";
import { createStripeConnectUrls } from "@/utils/urlUtils";

import { 
  BanknotesIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
} from "@heroicons/react/24/outline";

interface StripeConnectSetupProps {
  stripeStatus: any;
  onSetupComplete: () => void;
}

export default function StripeConnectSetup({ stripeStatus, onSetupComplete }: StripeConnectSetupProps) {
  const { toast } = useToast();
  const [isSettingUp, setIsSettingUp] = useState(false);

  const setupStripeMutation = trpc.athlete.setupStripeConnect.useMutation({
    onError: (error) => {
      console.error('❌ setupStripeMutation error:', error);
    },
    onSuccess: (data) => {
      console.log('✅ setupStripeMutation success:', data);
    }
  });

  const createOnboardingLinkMutation = trpc.athlete.createStripeOnboardingLink.useMutation({
    onError: (error) => {
      console.error('❌ createOnboardingLinkMutation error:', error);
      console.error('❌ Error details:', {
        message: error.message,
        data: error.data,
        shape: error.shape
      });
    },
    onSuccess: (data) => {
      console.log('✅ createOnboardingLinkMutation success:', data);
    },
    onMutate: (variables) => {
      console.log('🔄 createOnboardingLinkMutation starting with variables:', variables);
    }
  });

  const handleSetupStripe = async () => {
    console.log('🚀 handleSetupStripe called');
    console.log('🔍 stripeStatus:', stripeStatus);

    try {
      setIsSettingUp(true);
      console.log('✅ setIsSettingUp(true) called');

      // Create Stripe account if doesn't exist
      if (!stripeStatus?.hasAccount) {
        console.log('🏦 Creating Stripe account...');
        await setupStripeMutation.mutateAsync();
        console.log('✅ Stripe account created successfully');
        toast({
          title: "Account Created",
          description: "Stripe account created successfully. Redirecting to setup...",
          variant: "default",
        });
      } else {
        console.log('✅ Stripe account already exists');
      }

      // Create onboarding link with proper URL validation
      console.log('🔗 Creating onboarding URLs...');
      const { returnUrl, refreshUrl, baseUrl, validation } = createStripeConnectUrls();
      console.log('✅ URLs created successfully');

      console.log('📋 Stripe onboarding URLs:', {
        baseUrl,
        returnUrl,
        refreshUrl,
        validation,
        windowLocation: typeof window !== 'undefined' ? {
          origin: window.location.origin,
          protocol: window.location.protocol,
          hostname: window.location.hostname,
          port: window.location.port,
          href: window.location.href
        } : 'server-side'
      });

      console.log('🌐 About to call createOnboardingLinkMutation.mutateAsync...');
      console.log('📤 Request payload:', { returnUrl, refreshUrl });

      const linkResult = await createOnboardingLinkMutation.mutateAsync({
        returnUrl,
        refreshUrl,
      });

      console.log('✅ createOnboardingLinkMutation completed successfully');
      console.log('📥 Response:', linkResult);

      console.log('Stripe onboarding link created:', linkResult);

      // Redirect to Stripe onboarding
      console.log('🔄 Redirecting to:', linkResult.url);
      window.location.href = linkResult.url;
    } catch (error: any) {
      console.error("❌ Stripe setup failed:", error);
      console.error("❌ Error details:", {
        message: error.message,
        data: error.data,
        shape: error.shape,
        stack: error.stack
      });

      toast({
        title: "Setup Failed",
        description: error.message || "Failed to set up bank account connection.",
        variant: "destructive",
      });
    } finally {
      console.log('🏁 Finally block - setIsSettingUp(false)');
      setIsSettingUp(false);
    }
  };

  const getStatusInfo = () => {
    if (!stripeStatus?.hasAccount) {
      return {
        title: "Connect Your Bank Account",
        description: "To receive payouts, you need to connect your bank account through Stripe.",
        icon: <BanknotesIcon className="tw-h-5 tw-w-5 tw-text-blue-600" />,
        badge: null,
        alertType: "info" as const,
        buttonText: "Connect Bank Account",
        buttonDisabled: false,
      };
    }

    switch (stripeStatus.status) {
      case 'incomplete':
        return {
          title: "Complete Bank Account Setup",
          description: "Your bank account setup is incomplete. Please complete the verification process.",
          icon: <ExclamationTriangleIcon className="tw-h-5 tw-w-5 tw-text-yellow-600" />,
          badge: <Badge variant="secondary" className="tw-bg-yellow-100 tw-text-yellow-800">Incomplete</Badge>,
          alertType: "warning" as const,
          buttonText: "Complete Setup",
          buttonDisabled: false,
        };
      case 'restricted':
        return {
          title: "Bank Account Under Review",
          description: "Your bank account is being reviewed. This process usually takes 1-2 business days.",
          icon: <InformationCircleIcon className="tw-h-5 tw-w-5 tw-text-blue-600" />,
          badge: <Badge variant="secondary" className="tw-bg-blue-100 tw-text-blue-800">Under Review</Badge>,
          alertType: "info" as const,
          buttonText: "Update Information",
          buttonDisabled: false,
        };
      case 'enabled':
        return {
          title: "Bank Account Connected",
          description: "Your bank account is successfully connected and ready to receive payouts.",
          icon: <CheckCircleIcon className="tw-h-5 tw-w-5 tw-text-green-600" />,
          badge: <Badge variant="secondary" className="tw-bg-green-100 tw-text-green-800">Connected</Badge>,
          alertType: "success" as const,
          buttonText: "Update Account",
          buttonDisabled: false,
        };
      case 'rejected':
        return {
          title: "Bank Account Setup Failed",
          description: "There was an issue with your bank account setup. Please try again or contact support.",
          icon: <ExclamationTriangleIcon className="tw-h-5 tw-w-5 tw-text-red-600" />,
          badge: <Badge variant="destructive">Failed</Badge>,
          alertType: "error" as const,
          buttonText: "Try Again",
          buttonDisabled: false,
        };
      default:
        return {
          title: "Bank Account Status Unknown",
          description: "Unable to determine bank account status. Please try refreshing the page.",
          icon: <InformationCircleIcon className="tw-h-5 tw-w-5 tw-text-gray-600" />,
          badge: <Badge variant="outline">Unknown</Badge>,
          alertType: "info" as const,
          buttonText: "Refresh Status",
          buttonDisabled: false,
        };
    }
  };

  const statusInfo = getStatusInfo();

  // Don't show if account is enabled
  if (stripeStatus?.status === 'enabled') {
    return null;
  }

  return (
    <Card className="tw-border-l-4 tw-border-l-blue-500">
      <CardHeader>
        <CardTitle className="tw-text-lg tw-flex tw-items-center tw-gap-3">
          {statusInfo.icon}
          <span>{statusInfo.title}</span>
          {statusInfo.badge}
        </CardTitle>
      </CardHeader>
      <CardContent className="tw-space-y-4">
        <div className={`tw-p-3 tw-rounded-lg tw-border tw-text-sm ${
          statusInfo.alertType === 'error'
            ? 'tw-bg-red-50 tw-border-red-200 tw-text-red-800'
            : statusInfo.alertType === 'warning'
            ? 'tw-bg-yellow-50 tw-border-yellow-200 tw-text-yellow-800'
            : statusInfo.alertType === 'success'
            ? 'tw-bg-green-50 tw-border-green-200 tw-text-green-800'
            : 'tw-bg-blue-50 tw-border-blue-200 tw-text-blue-800'
        }`}>
          {statusInfo.description}
        </div>

        <div className="tw-flex tw-flex-col sm:tw-flex-row tw-gap-3">
          <Button
            onClick={() => {
              console.log('🖱️ Button clicked!');
              console.log('🔍 Button state:', { isSettingUp, buttonDisabled: statusInfo.buttonDisabled });
              handleSetupStripe();
            }}
            disabled={isSettingUp || statusInfo.buttonDisabled}
            className="tw-h-10 sm:tw-h-9 tw-text-black"
          >
            {isSettingUp ? "Setting up..." : statusInfo.buttonText}
          </Button>
          
          {stripeStatus?.hasAccount && (
            <Button
              variant="outline"
              onClick={onSetupComplete}
              className="tw-h-10 sm:tw-h-9"
            >
              Refresh Status
            </Button>
          )}
        </div>

        <div className="tw-text-xs tw-text-aims-text-secondary tw-space-y-1">
          <p>• Bank account information is securely handled by Stripe</p>
          <p>• You&apos;ll need to provide basic information and verify your identity</p>
          <p>• Payouts are typically processed within 1-2 business days</p>
        </div>
      </CardContent>
    </Card>
  );
}
