import { Document, model, Schema } from "mongoose";
import { AthleteWallet } from "../types/athlete";

// Document interface for MongoDB
export interface AthleteWalletDocument extends Document {
  _id: string;
  athleteId: Schema.Types.ObjectId;
  availableBalance: number;
  pendingEarnings: number;
  totalEarnings: number;
  stripeConnectAccountId?: string;
  stripeAccountStatus?: string;
  createdAt: Date;
  updatedAt: Date;
  toClient(): AthleteWallet;
}

// Mongoose schema
const athleteWalletSchema = new Schema<AthleteWalletDocument>(
  {
    athleteId: {
      type: Schema.Types.ObjectId,
      ref: "Athletes",
      required: true,
      unique: true, // One wallet per athlete
    },
    availableBalance: {
      type: Number,
      required: true,
      default: 0,
      min: 0,
    },
    pendingEarnings: {
      type: Number,
      required: true,
      default: 0,
      min: 0,
    },
    totalEarnings: {
      type: Number,
      required: true,
      default: 0,
      min: 0,
    },
    stripeConnectAccountId: {
      type: String,
    },
    stripeAccountStatus: {
      type: String,
      enum: [
        "pending", 
        "restricted", 
        "enabled", 
        "rejected",
        "incomplete"
      ],
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Indexes for performance
// Note: athleteId already has unique: true which creates an index
// athleteWalletSchema.index({ athleteId: 1 }); // Removed duplicate
athleteWalletSchema.index({ stripeConnectAccountId: 1 }, { sparse: true });

// Instance method to convert to client format
athleteWalletSchema.methods.toClient = function(): AthleteWallet {
  return {
    athleteId: this.athleteId,
    availableBalance: this.availableBalance,
    pendingEarnings: this.pendingEarnings,
    totalEarnings: this.totalEarnings,
    stripeConnectAccountId: this.stripeConnectAccountId,
    stripeAccountStatus: this.stripeAccountStatus,
    createdAt: this.createdAt,
    updatedAt: this.updatedAt,
  };
};

const AthleteWalletModel = model<AthleteWalletDocument>("AthleteWallet", athleteWalletSchema);
export default AthleteWalletModel;
