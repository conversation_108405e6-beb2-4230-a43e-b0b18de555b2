"use client";

import { trpc } from "@/lib/trpc/client";

import { <PERSON>, CardContent, CardHeader, CardTitle } from "../ui/card";
import { Badge } from "../ui/badge";
import { But<PERSON> } from "../ui/button";
import Link from "next/link";

interface CampaignContractOverviewProps {
  campaignId: string;
}

export function CampaignContractOverview({
  campaignId,
}: CampaignContractOverviewProps) {
  // Fetch contracts for this campaign
  const { data: contracts, isLoading } = trpc.contract.getByCampaign.useQuery({
    campaignId,
  });

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Contract Overview</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="tw-animate-pulse tw-space-y-3">
            <div className="tw-h-4 tw-bg-gray-200 tw-rounded tw-w-3/4"></div>
            <div className="tw-h-4 tw-bg-gray-200 tw-rounded tw-w-1/2"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Calculate contract statistics
  const contractStats = {
    total: contracts?.length || 0,
    draft: contracts?.filter(c => c.status === "DRAFT").length || 0,
    pendingReview: contracts?.filter(c => c.status === "PENDING_BRAND_REVIEW").length || 0,
    pendingApproval: contracts?.filter(c => c.status === "PENDING_BRAND_APPROVAL").length || 0,
    pendingSignature: contracts?.filter(c => c.status === "PENDING_ATHLETE_SIGNATURE").length || 0,
    signed: contracts?.filter(c => c.status === "ATHLETE_SIGNED").length || 0,
    paid: contracts?.filter(c => c.status === "PAID").length || 0,
    awaitingDeliverables: contracts?.filter(c => c.status === "AWAITING_DELIVERABLES").length || 0,
    fulfilled: contracts?.filter(c => c.status === "FULFILLED").length || 0,
    cancelled: contracts?.filter(c => c.status === "CANCELLED").length || 0,
    expired: contracts?.filter(c => c.status === "EXPIRED").length || 0,
  };

  const totalValue = contracts?.reduce((sum, contract) => sum + contract.terms.totalCompensation, 0) || 0;
  const signedValue = contracts?.filter(c => (c.status === "ATHLETE_SIGNED" || c.status === "PENDING_BRAND_SIGNATURE" || c.status === "BRAND_SIGNED" || c.status === "PAID" || c.status === "FULFILLED")).reduce((sum, contract) => sum + contract.terms.totalCompensation, 0) || 0;

  return (
    <Card>
      <CardHeader className="tw-flex tw-flex-row tw-items-center tw-justify-between">
        <CardTitle>Contract Overview</CardTitle>
        <Button
          variant="outline"
          size="sm"
          asChild
        >
          <Link href={`/app/brand/contracts`}>
            View All Contracts
          </Link>
        </Button>
      </CardHeader>
      <CardContent>
        {contractStats.total === 0 ? (
          <div className="tw-text-center tw-py-8">
            <p className="tw-text-gray-500 tw-mb-4">No contracts generated yet</p>
            <p className="tw-text-sm tw-text-gray-400">
              Contracts will appear here after you accept athlete applications and generate contracts.
            </p>
          </div>
        ) : (
          <div className="tw-space-y-6">
            {/* Summary Stats */}
            <div className="tw-grid tw-grid-cols-2 md:tw-grid-cols-4 tw-gap-4">
              <div className="tw-text-center">
                <div className="tw-text-2xl tw-font-bold tw-text-blue-600">{contractStats.total}</div>
                <div className="tw-text-sm tw-text-aims-dark-6">Total Contracts</div>
              </div>
              <div className="tw-text-center">
                <div className="tw-text-2xl tw-font-bold tw-text-green-600">{contractStats.paid + contractStats.awaitingDeliverables}</div>
                <div className="tw-text-sm tw-text-aims-dark-6">Active</div>
              </div>
              <div className="tw-text-center">
                <div className="tw-text-2xl tw-font-bold tw-text-purple-600">{contractStats.pendingSignature}</div>
                <div className="tw-text-sm tw-text-aims-dark-6">Pending Signature</div>
              </div>
              <div className="tw-text-center">
                <div className="tw-text-2xl tw-font-bold tw-text-yellow-600">
                  {contractStats.pendingReview + contractStats.pendingApproval}
                </div>
                <div className="tw-text-sm tw-text-aims-dark-6">Needs Review</div>
              </div>
            </div>

            {/* Value Summary */}
            <div className="tw-grid tw-grid-cols-1 md:tw-grid-cols-2 tw-gap-4 tw-pt-4 tw-border-t">
              <div className="tw-text-center">
                <div className="tw-text-lg tw-font-semibold">
                  {new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: 'USD',
                  }).format(totalValue)}
                </div>
                <div className="tw-text-sm tw-text-aims-dark-6">Total Contract Value</div>
              </div>
              <div className="tw-text-center">
                <div className="tw-text-lg tw-font-semibold tw-text-green-600">
                  {new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: 'USD',
                  }).format(signedValue)}
                </div>
                <div className="tw-text-sm tw-text-aims-dark-6">Signed Contract Value</div>
              </div>
            </div>

            {/* Status Breakdown */}
            <div className="tw-space-y-2 tw-pt-4 tw-border-t">
              <h4 className="tw-font-medium tw-mb-3">Contract Status Breakdown</h4>
              <div className="tw-flex tw-flex-wrap tw-gap-2">
                {contractStats.draft > 0 && (
                  <Badge variant="outline" className="tw-text-gray-600">
                    {contractStats.draft} Draft
                  </Badge>
                )}
                {contractStats.pendingReview > 0 && (
                  <Badge className="tw-bg-yellow-100 tw-text-yellow-800">
                    {contractStats.pendingReview} Pending Review
                  </Badge>
                )}
                {contractStats.pendingApproval > 0 && (
                  <Badge className="tw-bg-blue-100 tw-text-blue-800">
                    {contractStats.pendingApproval} Pending Approval
                  </Badge>
                )}
                {contractStats.pendingSignature > 0 && (
                  <Badge className="tw-bg-purple-100 tw-text-purple-800">
                    {contractStats.pendingSignature} Pending Signature
                  </Badge>
                )}
                {contractStats.signed > 0 && (
                  <Badge className="tw-bg-green-100 tw-text-green-800">
                    {contractStats.signed} Signed
                  </Badge>
                )}
                {contractStats.cancelled > 0 && (
                  <Badge className="tw-bg-red-100 tw-text-red-800">
                    {contractStats.cancelled} Cancelled
                  </Badge>
                )}
                {contractStats.expired > 0 && (
                  <Badge className="tw-bg-gray-100 tw-text-gray-600">
                    {contractStats.expired} Expired
                  </Badge>
                )}
                {contractStats.paid > 0 && (
                  <Badge className="tw-bg-green-100 tw-text-green-800">
                    {contractStats.paid} Paid
                  </Badge>
                )}
                {contractStats.awaitingDeliverables > 0 && (
                  <Badge className="tw-bg-purple-100 tw-text-purple-800">
                    {contractStats.awaitingDeliverables} Working on Deliverables
                  </Badge>
                )}
                {contractStats.fulfilled > 0 && (
                  <Badge className="tw-bg-green-100 tw-text-green-800">
                    {contractStats.fulfilled} Fulfilled
                  </Badge>
                )}
              </div>
            </div>

            {/* Quick Actions */}
            {/* {(contractStats.pendingReview > 0 || contractStats.pendingApproval > 0) && (
              <div className="tw-pt-4 tw-border-t">
                <h4 className="tw-font-medium tw-mb-3">Quick Actions</h4>
                <div className="tw-flex tw-flex-wrap tw-gap-2">
                  {contractStats.pendingReview > 0 && (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => window.open(`/app/brand/contracts?status=PENDING_BRAND_REVIEW`, '_blank')}
                    >
                      Review {contractStats.pendingReview} Contract{contractStats.pendingReview > 1 ? 's' : ''}
                    </Button>
                  )}
                  {contractStats.pendingApproval > 0 && (
                    <Button
                      size="sm"
                      onClick={() => window.open(`/app/brand/contracts?status=PENDING_BRAND_APPROVAL`, '_blank')}
                    >
                      Approve {contractStats.pendingApproval} Contract{contractStats.pendingApproval > 1 ? 's' : ''}
                    </Button>
                  )}
                </div>
              </div>
            )} */}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
