export enum MessageType {
  TEXT = "TEXT",
  CAMPAIGN_APPLICATION = "CAMPAIGN_APPLICATION",
  CAMPAIGN_INVITE = "CAMPAIGN_INVITE",
  CAMPAIGN_ACCEPT = "CAMPAIGN_ACCEPT",
  CAMPAIGN_REJECT = "CAMPAIGN_REJECT",
  APPLICATION_ACCEPTED = "APPLICATION_ACCEPTED",
  APPLICATION_REJECTED = "APPLICATION_REJECTED",
  CONTRACT_SENT = "CONTRACT_SENT",
  CONTRACT_SIGNED = "CONTRACT_SIGNED",
  CONTRACT_REJECTED = "CONTRACT_REJECTED",
  CONTRACT_UPDATED = "CONTRACT_UPDATED",
  CONTRACT_CANCELLED = "CONTRACT_CANCELLED",
  CONTRACT_EXPIRED = "CONTRACT_EXPIRED",
  CONTRACT_PAYMENT_REQUIRED = "CONTRACT_PAYMENT_REQUIRED",
  CONTRACT_PAYMENT_COMPLETED = "CONTRACT_PAYMENT_COMPLETED",
  CONTRACT_PAYMENT_FAILED = "CONTRACT_PAYMENT_FAILED",
  CONTRACT_FULFILLED = "CONTRACT_FULFILLED",
  DELIVERABLE_SUBMISSION = "DELIVERABLE_SUBMISSION",
  EARNINGS_CREDITED = "EARNINGS_CREDITED",
  PAYOUT_COMPLETED = "PAYOUT_COMPLETED",
  PAYOUT_FAILED = "PAYOUT_FAILED",
}

export enum ChatType {
  DIRECT = "DIRECT",
  CAMPAIGN = "CAMPAIGN",
}