"use client";

import CompleteYourProfile from "@/components/ui/CompleteYourProfile";
import DashboardStats from "@/components/ui/DashboardStats";
import { FullPageLoadingSpinner } from "@/components/ui/LoadingSpinner";
import { useAuth } from "@/hooks/use-auth";
import { trpc } from "@/lib/trpc/client";
import {
  CurrencyDollarIcon,
  DocumentTextIcon,
  MegaphoneIcon,
  UserGroupIcon,
} from "@heroicons/react/24/solid";

import { CampaignStatus } from "@repo/server/src/types/campaign";

export default function Dashboard() {
  const { user } = useAuth();
  const { data: campaigns, isLoading: campaignsLoading } =
    trpc.campaign.getBrandCampaigns.useQuery();
  const { data: contractStats } = trpc.contract.getStats.useQuery();

  if (campaignsLoading) {
    return <FullPageLoadingSpinner />;
  }

  const activeCampaigns = campaigns?.filter(
    (campaign) => campaign.status === CampaignStatus.ACTIVE,
  );

  const activeParticipants = activeCampaigns?.reduce(
    (sum, campaign) => sum + (campaign.athletes?.length ?? 0),
    0,
  );

  const totalSpend = campaigns?.reduce(
    (sum, campaign) => sum + (campaign.price ?? 0),
    0,
  );

  const companyName = user?.brand?.companyName;

  const stats = [
    {
      label: "Active campaigns",
      value: activeCampaigns?.length ?? 0,
      icon: (
        <MegaphoneIcon className="tw-h-6 tw-w-6 tw-text-aims-text-primary" />
      ),
    },
    {
      label: "Signed contracts",
      value: contractStats?.signed ?? 0,
      icon: (
        <DocumentTextIcon className="tw-h-6 tw-w-6 tw-text-aims-text-primary" />
      ),
    },
    {
      label: "Active participants",
      value: activeParticipants ?? 0,
      icon: (
        <UserGroupIcon className="tw-h-6 tw-w-6 tw-text-aims-text-primary" />
      ),
    },
    {
      label: "Total spend",
      value: `$${totalSpend?.toFixed(2) ?? 0}`,
      icon: (
        <CurrencyDollarIcon className="tw-h-6 tw-w-6 tw-text-aims-text-primary" />
      ),
    },
  ];

  const hasLogo =
    !!user?.brand?.logo?.url &&
    user?.brand?.logo?.url.startsWith("https://aims-photos");

  const profileCompleted = hasLogo;

  const profileSteps = [
    {
      title: "Add your logo",
      description:
        "For better engagement, consider uploading a logo to personalize your brand profile and increase your visibility.",
      buttonText: "Add",
      completed: hasLogo,
    },
  ];

  return (
    <div className="tw-p-6">
      <h2 className="tw-mb-3">Hi, {companyName}</h2>
      <DashboardStats stats={stats} />
      {!profileCompleted && (
        <CompleteYourProfile
          profileSteps={profileSteps}
          href="/app/brand/profile"
        />
      )}
    </div>
  );
}
