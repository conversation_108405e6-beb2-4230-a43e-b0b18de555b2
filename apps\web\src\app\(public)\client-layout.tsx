"use client";

import { useEffect, useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { useAuth } from "@/hooks/use-auth";
import { Bars3Icon, XMarkIcon } from "@heroicons/react/24/outline";

const AuthButtons = ({
  isAuthenticated,
  isAthlete,
}: {
  isAuthenticated: boolean;
  isAthlete: boolean;
}) => {
  if (isAuthenticated) {
    return (
      <Button asChild>
        <Link href={isAthlete ? "/app/athlete" : "/app/brand"}>Dashboard</Link>
      </Button>
    );
  }

  return (
    <>
      <Button variant="ghost" asChild>
        <Link href="/auth/login">Sign in</Link>
      </Button>
      <Button asChild>
        <Link href="/auth/register">Get Started</Link>
      </Button>
    </>
  );
};

const MobileMenuButton = ({
  isMobileMenuOpen,
  onClick,
}: {
  isMobileMenuOpen: boolean;
  onClick: () => void;
}) => (
  <button
    type="button"
    onClick={onClick}
    className="tw-inline-flex tw-items-center tw-justify-center tw-rounded-md tw-p-2 tw-text-aims-text-primary hover:tw-bg-gray-100 hover:tw-text-black"
    aria-expanded={isMobileMenuOpen}
  >
    <span className="tw-sr-only">
      {isMobileMenuOpen ? "Close main menu" : "Open main menu"}
    </span>
    {isMobileMenuOpen ? (
      <XMarkIcon className="tw-h-6 tw-w-6" />
    ) : (
      <Bars3Icon className="tw-h-6 tw-w-6" />
    )}
  </button>
);

export default function ClientLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { isAuthenticated, getProfile } = useAuth();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isAthlete, setIsAthlete] = useState<boolean>(false);

  useEffect(() => {
    const fetchProfile = async () => {
      const profile = await getProfile();
      setIsAthlete(!!profile?.athlete);
    };
    fetchProfile();
  }, [getProfile]);

  return (
    <div className="tw-min-h-screen tw-flex tw-flex-col tw-bg-aims-bg">
      {/* Navigation */}
      <nav className="tw-bg-aims-dark-2 tw-backdrop-blur-xl tw-sticky tw-top-0 tw-z-50">
        <div className="tw-mx-auto tw-px-4 sm:tw-px-6 lg:tw-px-8">
          <div className="tw-flex tw-h-16 tw-items-center tw-justify-between">
            {/* Left - Logo */}
            <div className="tw-flex-shrink-0">
              <Link href="/" className="tw-flex tw-items-center tw-gap-2">
                <Image
                  src="/landing-page/nav-logo.png"
                  alt="AIMS - Athlete & Influencer Marketing Solutions"
                  width={300}
                  height={300}
                  priority
                  className="tw-h-10 tw-w-auto"
                />
                <Image
                  src="/landing-page/nav-text.png"
                  alt="AIMS - Athlete & Influencer Marketing Solutions"
                  width={300}
                  height={300}
                  priority
                  className="tw-h-5 tw-w-auto"
                />
              </Link>
            </div>

            {/* Center - Navigation Links */}
            {/* <div className="tw-hidden md:tw-block">
              <div className="tw-flex tw-items-center tw-justify-center tw-space-x-8">
                <NavigationLink href="/">Home</NavigationLink>
                <NavigationLink href="/about">About</NavigationLink>
              </div>
            </div> */}

            {/* Right - Auth Buttons */}
            <div className="tw-hidden md:tw-block">
              <div className="tw-flex tw-items-center tw-gap-4">
                <AuthButtons
                  isAuthenticated={isAuthenticated}
                  isAthlete={isAthlete}
                />
              </div>
            </div>

            {/* Mobile menu button */}
            <div className="tw-flex md:tw-hidden">
              <MobileMenuButton
                isMobileMenuOpen={isMobileMenuOpen}
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              />
            </div>
          </div>

          {/* Mobile menu panel */}
          {isMobileMenuOpen && (
            <div className="md:tw-hidden">
              <div className="tw-space-y-1 tw-px-2 tw-pb-3 tw-pt-2">
                <div className="tw-flex tw-justify-end tw-gap-2">
                  <AuthButtons
                    isAuthenticated={isAuthenticated}
                    isAthlete={isAthlete}
                  />
                </div>
              </div>
            </div>
          )}
        </div>
      </nav>

      {/* Main content */}
      <main className="tw-flex-grow">{children}</main>

      {/* Footer */}
      <footer className="tw-bg-aims-bg-primary">
        <hr className="tw-border-aims-dark-2 tw-mx-4 sm:tw-mx-8 lg:tw-mx-12" />
        <div className="tw-mx-auto tw-max-w-7xl tw-px-4 tw-py-6 sm:tw-py-8 lg:tw-py-12 sm:tw-px-6 lg:tw-px-8">
          <div className="tw-flex tw-flex-col sm:tw-flex-row tw-justify-between tw-items-center tw-gap-4 sm:tw-gap-0">
            <p className="tw-text-xs sm:tw-text-sm tw-text-aims-dark-6 tw-text-center sm:tw-text-left tw-leading-relaxed">
              © {new Date().getFullYear()} Athlete & Influencer Marketing, Inc.
              All rights reserved.
            </p>
            <Link
              href="https://www.linkedin.com/company/athlete-influencer-marketing-solutions-llc/"
              target="_blank"
              className="tw-flex tw-items-center tw-justify-center tw-min-h-[44px] tw-min-w-[44px] tw-p-2"
              aria-label="Visit our LinkedIn page"
            >
              <Image
                src="/landing-page/linkedin.png"
                alt="LinkedIn"
                width={400}
                height={400}
                className="tw-h-5 sm:tw-h-6 tw-w-auto"
              />
            </Link>
          </div>
        </div>
      </footer>
    </div>
  );
}
