"use client";

import { useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { Checkbox } from "@/components/ui/auth/Checkbox";
import { FormInput } from "@/components/ui/auth/FormInput";
import { But<PERSON> } from "@/components/ui/button";
import { LoadingSpinner } from "@/components/ui/LoadingSpinner";
import { useToast } from "@/components/ui/toast/use-toast";
import { useAuth } from "@/hooks/use-auth";
import { CredentialResponse, GoogleLogin } from "@react-oauth/google";

export default function Login() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [rememberDevice, setRememberDevice] = useState(false);
  const router = useRouter();
  const { signIn, signInWithGoogle } = useAuth();
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const response = await signIn(email, password);
      let serverUserType;
      if (typeof response === "object" && response !== null) {
        serverUserType = response.profile.userType;
      } else {
        serverUserType = response;
      }
      const dev = process.env.NODE_ENV === "development";
      if (dev && process.env.NEXT_PUBLIC_SKIP_EMAIL_VERIFICATION === "true") {
        if (serverUserType === "athlete") {
          router.push("/app/athlete");
        } else {
          router.push("/app/brand");
        }
        return;
      }

      if (response) {
        router.push(
          `/auth/verify?email=${encodeURIComponent(email)}&userType=${serverUserType}`,
        );
      } else {
        if (serverUserType === "athlete") {
          router.push("/app/athlete");
        } else {
          router.push("/app/brand");
        }
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: (error as Error).message,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div>
      <h4 className="tw-mb-6">Sign In</h4>
      {/* 
      <UserTypeToggle userType={userType} onChange={setUserType} /> */}

      <form onSubmit={handleSubmit} className="tw-space-y-4">
        <FormInput
          id="email"
          label="Email"
          type="email"
          value={email}
          onChange={setEmail}
          required
          disabled={isLoading}
          placeholder="<EMAIL>"
        />

        <div>
          <FormInput
            id="password"
            label="Password"
            type="password"
            value={password}
            onChange={setPassword}
            required
            disabled={isLoading}
            isPassword
          />
          <div className="tw-flex tw-justify-end tw-mt-2">
            <Link
              href="/auth/forgot-password"
              className="tw-text-sm tw-text-aims-primary hover:tw-text-aims-primary/90"
            >
              Forgot your password?
            </Link>
          </div>
        </div>

        <Checkbox
          id="remember"
          checked={rememberDevice}
          onChange={setRememberDevice}
          label="Remember this device"
        />

        <Button type="submit" disabled={isLoading} className="tw-w-full">
          {isLoading ? (
            <div className="tw-flex tw-items-center tw-justify-center tw-gap-2">
              <span>Signing in...</span>
              <LoadingSpinner color="black" />
            </div>
          ) : (
            "Sign in"
          )}
        </Button>

        <div className="tw-relative tw-my-6">
          <div className="tw-absolute tw-inset-0 tw-flex tw-items-center">
            <div className="tw-w-full tw-border-t tw-border-aims-dark-3" />
          </div>
          <div className="tw-relative tw-flex tw-justify-center tw-text-sm">
            <span className="tw-px-2 tw-text-aims-text-secondary tw-bg-aims-dark-2">
              OR
            </span>
          </div>
        </div>

        <div className="tw-flex tw-gap-4 tw-justify-center">
          <GoogleLogin
            onSuccess={async (credentialResponse: CredentialResponse) => {
              try {
                setIsLoading(true);
                const response = await signInWithGoogle(
                  credentialResponse.credential || "",
                  "brand",
                );
                if (
                  response &&
                  typeof response === "object" &&
                  "profile" in response
                ) {
                  if (response.profile.userType === "athlete") {
                    router.push("/app/athlete");
                  } else {
                    router.push("/app/brand");
                  }
                }
              } catch (error) {
                toast({
                  variant: "destructive",
                  title: "Error",
                  description: (error as Error).message,
                });
              } finally {
                setIsLoading(false);
              }
            }}
            onError={() => {
              toast({
                variant: "destructive",
                title: "Error",
                description: "Google Sign-In failed",
              });
            }}
            shape="rectangular"
            text="signin_with"
            locale="en"
          />
        </div>

        <p className="tw-text-center tw-text-sm tw-text-aims-text-secondary">
          Not registered yet?{" "}
          <Link
            href="/auth/register"
            className="tw-text-aims-primary hover:tw-text-aims-primary/90"
          >
            Create an account
          </Link>
        </p>
      </form>
    </div>
  );
}
