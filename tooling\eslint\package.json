{"name": "@repo/eslint-config", "private": true, "version": "0.3.0", "type": "module", "exports": {"./base": "./base.js", "./nextjs": "./nextjs.js", "./react": "./react.js"}, "scripts": {"clean": "rm -rf .turbo node_modules", "format": "prettier --check . --ignore-path ../../.gitignore", "typecheck": "tsc --noEmit"}, "dependencies": {"@next/eslint-plugin-next": "^14.2.3", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "rc", "eslint-plugin-turbo": "^2.0.3", "typescript-eslint": "rc-v8"}, "devDependencies": {"@repo/prettier-config": "workspace:*", "@repo/tsconfig": "workspace:*", "eslint": "9.14.0", "prettier": "^3.5.3", "typescript": "5.7.3"}, "prettier": "@repo/prettier-config"}