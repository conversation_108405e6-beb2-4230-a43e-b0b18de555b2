# Puppeteer PDF Generation Fix

## Problem
Your staging environment was experiencing Puppeteer failures when generating contract PDFs due to missing system libraries in the Docker container. The error `libgobject-2.0.so.0: cannot open shared object file: No such file or directory` indicated that Chrome couldn't launch because required dependencies were missing from the `node:20-slim` base image.

## Root Cause
The `node:20-slim` Docker image is a minimal Linux distribution that doesn't include the system libraries required by Chrome/Chromium that <PERSON><PERSON>peteer uses for PDF generation.

## Solution Applied

### 1. Updated Dockerfile
**File:** `Dockerfile`

Added comprehensive system dependencies required for Chrome to run in a containerized environment:
- GLib libraries (`libgobject-2.0-0`, `libglib2.0-0`)
- GTK and graphics libraries
- Font libraries
- Audio libraries
- X11 libraries
- NSS libraries for security

### 2. Enhanced Puppeteer Configuration
**File:** `packages/server/src/services/pdfGenerator.ts`

Added additional Chrome arguments optimized for containerized environments:
- `--single-process`: Runs Chrome in single-process mode (safer for containers)
- `--no-zygote`: Disables the zygote process (reduces memory usage)
- `--disable-background-mode`: Prevents background processes
- Additional stability and performance flags

### 3. Added Test Script
**File:** `packages/server/src/scripts/test-puppeteer.ts`

Created a test script to verify Puppeteer functionality before deployment.

## Testing

### Local Testing (Before Deployment)
```bash
# Test Puppeteer locally
cd packages/server
pnpm test:puppeteer
```

### Docker Testing (Recommended)
```bash
# Build and test the Docker image locally
docker build -t aims-test .
docker run --rm -it aims-test pnpm test:puppeteer
```

## Deployment Steps

### 1. Deploy to Staging
Since you're using Fly.io (based on `fly.toml`), the deployment should be straightforward:

```bash
# Deploy to staging
flyctl deploy
```

### 2. Verify the Fix
After deployment, test PDF generation:
1. Go to your staging environment
2. Navigate to a contract that needs PDF generation
3. Try generating a contract PDF
4. Check the logs to ensure no more Chrome launch errors

### 3. Monitor Logs
Watch for the success messages:
```
[PDF Generator] Puppeteer browser initialized successfully
[PDF Generation] PDF generated successfully, uploading to S3
```

## Expected Behavior After Fix

✅ **Before (Failing):**
```
libgobject-2.0.so.0: cannot open shared object file: No such file or directory
[PDF Generator] Failed to initialize browser: Error: Failed to launch the browser process!
```

✅ **After (Working):**
```
[PDF Generator] Puppeteer browser initialized successfully
[PDF Generation] PDF generated successfully, uploading to S3
[PDF Generation] PDF uploaded successfully to S3
```

## Performance Impact

The updated Docker image will be larger due to the additional system libraries (~50-100MB increase), but this is necessary for Chrome to function. The performance impact on PDF generation should be minimal or potentially improved due to the optimized Chrome arguments.

## Rollback Plan

If issues arise, you can quickly rollback by reverting the Dockerfile changes:
1. Remove the `RUN apt-get update && apt-get install` section
2. Redeploy
3. The system will fall back to the HTML fallback method in the PDF generator

## Additional Notes

- The fix includes both the main Puppeteer initialization and the fallback method
- The system now has better error handling and retry logic
- Chrome arguments are optimized for containerized environments
- The test script can be used for ongoing monitoring and debugging

## Future Considerations

Consider monitoring:
- Memory usage (Chrome can be memory-intensive)
- PDF generation times
- Container startup times (slightly longer due to additional dependencies)

If memory becomes an issue, you may want to:
- Implement PDF generation as a separate microservice
- Use a queue system for PDF generation
- Consider alternative PDF generation libraries (though Puppeteer is generally the most reliable)
