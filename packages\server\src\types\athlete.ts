import { Schema } from "mongoose";

// Athlete payout and wallet types
export enum PayoutRequestStatus {
  PENDING = "PENDING",
  PROCESSING = "PROCESSING", 
  COMPLETED = "COMPLETED",
  FAILED = "FAILED",
  CANCELLED = "CANCELLED",
}

export enum TransactionType {
  EARNINGS_CREDITED = "EARNINGS_CREDITED", // When contract is fulfilled
  PAYOUT_REQUESTED = "PAYOUT_REQUESTED",   // When athlete requests payout
  PAYOUT_COMPLETED = "PAYOUT_COMPLETED",   // When payout is processed
  PAYOUT_FAILED = "PAYOUT_FAILED",         // When payout fails
  PAYOUT_CANCELLED = "PAYOUT_CANCELLED",   // When payout is cancelled
}

export interface AthleteWallet {
  athleteId: Schema.Types.ObjectId;
  availableBalance: number;      // Money that can be withdrawn
  pendingEarnings: number;       // Money from active contracts not yet available
  totalEarnings: number;         // Lifetime earnings
  stripeConnectAccountId?: string; // Stripe Express account ID
  stripeAccountStatus?: string;    // Account verification status
  createdAt: Date;
  updatedAt: Date;
}

export interface AthletePayoutRequest {
  id: string;
  athleteId: Schema.Types.ObjectId;
  amount: number;
  status: PayoutRequestStatus;
  stripePayoutId?: string;       // Stripe payout ID when processed
  requestedAt: Date;
  processedAt?: Date;
  failureReason?: string;
  metadata?: {
    stripeConnectAccountId?: string;
    processingFee?: number;
  };
}

export interface WalletTransaction {
  id: string;
  athleteId: Schema.Types.ObjectId;
  type: TransactionType;
  amount: number;
  description: string;
  contractId?: Schema.Types.ObjectId;           // Reference to contract if applicable
  payoutRequestId?: Schema.Types.ObjectId;      // Reference to payout request if applicable
  metadata?: {
    contractNumber?: string;
    campaignName?: string;
    deliverableNames?: string[];
  };
  createdAt: Date;
}
