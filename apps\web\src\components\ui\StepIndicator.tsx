"use client";

interface StepIndicatorProps {
  currentStep: number;
  totalSteps: number;
  className?: string;
}

export function StepIndicator({
  currentStep,
  totalSteps,
  className = "",
}: StepIndicatorProps) {
  return (
    <div className={`tw-flex tw-justify-center tw-items-center ${className}`}>
      <div className="tw-flex tw-items-center">
        {Array.from({ length: totalSteps }, (_, i) => i + 1).map((step, index) => (
          <div key={step} className="tw-flex tw-flex-col tw-items-center">
            <div className="tw-flex tw-items-center">
              <div
                className={`tw-w-10 tw-h-10 tw-rounded-full tw-flex tw-items-center tw-justify-center tw-relative ${
                  step <= currentStep
                    ? "tw-bg-aims-primary"
                    : "tw-bg-gray-600"
                }`}
              >
                <span className="tw-text-aims-text-primary tw-font-medium tw-text-sm">
                  {step}
                </span>
              </div>
              {index < totalSteps - 1 && (
                <div
                  className={`tw-w-16 tw-h-[2px] tw-mx-[-8px] ${
                    step < currentStep
                      ? "tw-bg-aims-primary"
                      : "tw-bg-gray-600"
                  }`}
                />
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
