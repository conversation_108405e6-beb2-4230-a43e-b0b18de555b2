import { createSlice, PayloadAction } from "@reduxjs/toolkit";

import { CampaignVisibility } from "@repo/server/src/types/campaign";
import { DeliverableType } from "@repo/server/src/types/deliverable";

import { RootState } from "..";

export interface DeliverableInput {
  name: string;
  daysToComplete: number;
  minimumPayment: number;
  description: string;
  type: DeliverableType;
  location?: string;
  date?: string;
  time?: string;
  content?: string[];
  productName?: string;
  productPrice?: number;
}

export interface CampaignInfo {
  name: string;
  description: string;
  visibility: CampaignVisibility;
  startDate: string;
  endDate: string;
  deliverables: DeliverableInput[];
  price: number;
  interests: string[];
  files: Array<{
    url: string;
    originalName: string;
  }>;
}

interface CampaignState {
  currentStep: number;
  basicInfo: CampaignInfo;
  isComplete: boolean;
  campaignId?: string;
}

const initialState: CampaignState = {
  currentStep: 1,
  basicInfo: {
    name: "",
    description: "",
    visibility: CampaignVisibility.PUBLIC,
    startDate: "",
    endDate: "",
    deliverables: [],
    price: 0,
    interests: [],
    files: [],
  },
  isComplete: false,
  campaignId: undefined,
};

const editCampaignSlice = createSlice({
  name: "editCampaign",
  initialState,
  reducers: {
    setCurrentStep: (state, action: PayloadAction<number>) => {
      state.currentStep = action.payload;
    },
    setBasicInfo: (state, action: PayloadAction<Partial<CampaignInfo>>) => {
      state.basicInfo = { ...state.basicInfo, ...action.payload };
    },
    addDeliverable: (state, action: PayloadAction<DeliverableInput>) => {
      state.basicInfo.deliverables.push(action.payload);
    },
    removeDeliverable: (state, action: PayloadAction<number>) => {
      state.basicInfo.deliverables.splice(action.payload, 1);
    },
    editDeliverable: (
      state,
      action: PayloadAction<{ index: number; deliverable: DeliverableInput }>,
    ) => {
      state.basicInfo.deliverables[action.payload.index] =
        action.payload.deliverable;
    },
    resetCampaign: () => initialState,
    setComplete: (state, action: PayloadAction<boolean>) => {
      state.isComplete = action.payload;
    },
    setCampaignId: (state, action: PayloadAction<string | undefined>) => {
      state.campaignId = action.payload;
    },
    setPrice: (state, action: PayloadAction<number>) => {
      state.basicInfo.price = action.payload;
    },
  },
});

// Selectors
export const selectEditCurrentStep = (state: RootState) =>
  state.editCampaign.currentStep;
export const selectEditBasicInfo = (state: RootState) =>
  state.editCampaign.basicInfo;
export const selectEditIsComplete = (state: RootState) =>
  state.editCampaign.isComplete;
export const selectEditCampaignId = (state: RootState) =>
  state.editCampaign.campaignId;

export const {
  setCurrentStep,
  setBasicInfo,
  addDeliverable,
  removeDeliverable,
  editDeliverable,
  resetCampaign,
  setComplete,
  setCampaignId,
  setPrice,
} = editCampaignSlice.actions;

export default editCampaignSlice.reducer;
