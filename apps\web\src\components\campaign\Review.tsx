"use client";

import React, { useEffect } from "react";
import { Table, TableColumn } from "@/components/ui/Table";
import {
  DeliverableInput,
  selectBasicInfo,
  setPrice,
} from "@/store/slices/campaign";
import {
  selectEditBasicInfo,
  setPrice as setEditPrice,
} from "@/store/slices/editCampaign";
import { useDispatch, useSelector } from "react-redux";
import { getDeliverableTypeLabel } from "@repo/server/src/types/deliverable";
import { Button } from "../ui/button";

const tableClassName =
  "tw-px-3 tw-py-4 tw-text-left tw-text-sm tw-font-medium tw-text-aims-text-secondary";
const deliverableColumns: TableColumn<
  DeliverableInput & { typeLabel: string }
>[] = [
  { header: "DELIVERABLE", accessor: "name", className: tableClassName },
  {
    header: "DAYS TO COMPLETE",
    accessor: (d) => `${d.daysToComplete} days`,
    className: tableClassName,
  },
  {
    header: "MINIMUM PAYMENT",
    cell: (d) => (
      <span className="tw-font-semibold tw-text-green-400">
        ${d.minimumPayment}
      </span>
    ),
    className: tableClassName,
  },
  { header: "TYPE", accessor: "typeLabel", className: tableClassName },
];

type ReviewCampaignProps = {
  isEditing: boolean;
};

export default function ReviewCampaign({
  isEditing = false,
}: ReviewCampaignProps) {
  const campaign = useSelector(
    isEditing ? selectEditBasicInfo : selectBasicInfo,
  );
  const dispatch = useDispatch();

  const fileColumns: TableColumn<{
    url: string;
    originalName: string;
  }>[] = [
    {
      header: "FILE NAME",
      accessor: "originalName",
      className: tableClassName,
    },
  ];

  const deliverablesWithLabels = campaign.deliverables.map((deliverable) => ({
    ...deliverable,
    typeLabel:
      getDeliverableTypeLabel(deliverable.type) +
      (deliverable.content && deliverable.content.length > 0
        ? ` (${deliverable.content.length})`
        : ""),
  }));

  const total = campaign.deliverables.reduce(
    (sum, item) => sum + item.minimumPayment,
    0,
  );

  // New fee structure: (total * 1.10) + $0.50
  const totalWithAimsFee = total * 1.10;
  const finalPrice = totalWithAimsFee + 0.50;

  useEffect(() => {
    if (isEditing) {
      dispatch(setEditPrice(finalPrice));
    } else {
      dispatch(setPrice(finalPrice));
    }
  }, [dispatch, total, finalPrice, isEditing]);

  return (
    <div className="tw-px-2 md:tw-px-0">
      <h2 className="tw-text-center tw-text-aims-text-primary tw-text-4xl tw-font-semibold tw-mb-6">
        Review Campaign
      </h2>
      <div className="tw-flex tw-flex-col md:tw-flex-row tw-gap-6">
        {/* Main Card */}
        <div className="tw-bg-aims-dark-2 tw-rounded-lg tw-p-6 tw-flex-1 tw-text-aims-text-primary tw-shadow-lg">
          {/* About the Campaign */}
          <div className="tw-flex tw-flex-col md:tw-flex-row tw-gap-6">
            <div className="tw-flex-1">
              <h5 className="tw-font-semibold tw-mb-2">About the Campaign</h5>
              <div className="tw-grid tw-grid-cols-2 tw-gap-2 tw-text-sm tw-mb-2">
                <div>
                  <div className="tw-text-aims-dark-6">Campaign title</div>
                  <div>{campaign.name}</div>
                </div>
                <div>
                  <div className="tw-text-aims-dark-6">Start date</div>
                  <div>{campaign.startDate}</div>
                </div>
                <div>
                  <div className="tw-text-aims-dark-6">End date</div>
                  <div>{campaign.endDate}</div>
                </div>
                <div>
                  <div className="tw-text-aims-dark-6">Visibility</div>
                  <div>{campaign.visibility}</div>
                </div>
              </div>
              <div className="tw-mb-2">
                <div className="tw-text-aims-dark-6 tw-text-xs">
                  Interests for influencer matching
                </div>
                <div className="tw-flex tw-flex-wrap tw-gap-2 tw-mt-1">
                  {campaign.interests.map((interest, i) => (
                    <span
                      key={i}
                      className="tw-bg-aims-dark-3 tw-px-2 tw-py-1 tw-rounded-full tw-text-xs tw-text-gray-200"
                    >
                      {interest}
                    </span>
                  ))}
                </div>
              </div>
              <div className="tw-mb-2">
                <div className="tw-text-aims-dark-6 tw-text-xs">
                  Campaign description
                </div>
                <div className="tw-text-sm tw-mt-1">{campaign.description}</div>
              </div>
            </div>
            {/* <div className="tw-flex-shrink-0 tw-flex tw-flex-col tw-items-center tw-gap-2">
              <div className="tw-text-aims-dark-6 tw-text-xs">
                Campaign Profile picture
              </div>
              <img
                src={mockCampaign.profilePic}
                alt="Campaign profile"
                className="tw-w-32 tw-h-32 tw-object-cover tw-rounded-lg tw-border tw-border-gray-700"
              />
            </div> */}
          </div>

          {/* Deliverables */}
          <div className="tw-mt-8">
            <h4 className="tw-font-semibold tw-mb-2">Deliverables</h4>
            <div className="tw-overflow-hidden tw-rounded-lg tw-ring-1 tw-ring-aims-dark-3">
              <Table
                columns={deliverableColumns}
                data={deliverablesWithLabels}
                rowKey={(row, idx) => row.name + idx}
              />
            </div>
          </div>

          {/* Uploaded Files */}
          <div className="tw-mt-8">
            <h4 className="tw-font-semibold tw-mb-2">Uploaded files</h4>
            <div className="tw-overflow-hidden tw-rounded-lg tw-ring-1 tw-ring-aims-dark-3">
              <Table
                columns={fileColumns}
                data={campaign.files ?? []}
                rowKey={(row, idx) => row.originalName + idx}
                actions={() => (
                  <Button
                    variant="outline"
                    className="tw-text-aims-text-primary tw-text-xs"
                    onClick={() => {
                      window.open(campaign.files?.[0].url, "_blank");
                    }}
                  >
                    View
                  </Button>
                )}
              />
            </div>
          </div>
        </div>

        {/* Price Details Sidebar */}
        <div className="tw-bg-aims-dark-3 tw-rounded-lg tw-p-6 tw-w-full md:tw-w-80 tw-text-aims-text-primary tw-shadow-lg tw-h-fit">
          <h4 className="tw-font-semibold tw-mb-4">Price Details</h4>
          <div className="tw-space-y-2 tw-text-sm">
            {campaign.deliverables.map((item, i) => (
              <div key={i} className="tw-flex tw-justify-between">
                <span className="tw-text-gray-300">{item.name}</span>
                <span>${item.minimumPayment.toFixed(2)}</span>
              </div>
            ))}
            <div className="tw-flex tw-justify-between">
              <span className="tw-text-gray-300">AIMS Fee (10%)</span>
              <span>${(total * 0.10).toFixed(2)}</span>
            </div>
            <div className="tw-flex tw-justify-between">
              <span className="tw-text-gray-300">Processing Fee</span>
              <span>$0.50</span>
            </div>
            <div className="tw-border-t tw-border-gray-700 tw-my-2"></div>
            <div className="tw-flex tw-justify-between tw-font-bold tw-text-lg">
              <span>Total</span>
              <span>${finalPrice.toFixed(2)}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
