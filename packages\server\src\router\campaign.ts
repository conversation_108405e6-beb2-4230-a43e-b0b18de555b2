import * as yup from "yup";

import {
  applyCampaign,
  checkAthleteRelationshipWithCampaign,
  createCampaign,
  deleteCampaign,
  getActiveCampaigns,
  getAthleteApplications,
  getBrandCampaigns,
  getCampaignApplications,
  getCampaignByCampaignId,
  getCampaignById,
  getCampaignsByBrandId,
  getCampaignsForAthlete,
  inviteAthletesToCampaign,
  respondToCampaignInvite,
  updateApplicationStatus,
  updateCampaign,
  updateCampaignStatus,
} from "../controllers/campaign";
import { privateProcedure, trpc } from "../lib/trpc";
import { CampaignStatus } from "../types/campaign";
import { getSignedUrl } from "../utils/s3";
import {
  campaignApplicationSchema,
  campaignSchema,
  campaignUpdateSchema,
  updateApplicationStatusSchema,
} from "../validators/campaign";

export const campaignRouter = trpc.router({
  create: privateProcedure
    .input(campaignSchema)
    .mutation(async ({ ctx, input }) => {
      return createCampaign(ctx.req.user.id, ctx.req.user.userType, input);
    }),

  update: privateProcedure
    .input(
      yup.object({
        campaignId: yup.string().required(),
        ...campaignUpdateSchema.fields,
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { campaignId, ...updateData } = input;
      return updateCampaign(ctx.req.user.id, campaignId, updateData);
    }),
  updateStatus: privateProcedure
    .input(
      yup.object({
        campaignId: yup.string().required(),
        status: yup.string().required(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return updateCampaignStatus(
        ctx.req.user.id,
        input.campaignId,
        input.status as CampaignStatus,
      );
    }),

  getBrandCampaigns: privateProcedure.query(async ({ ctx }) => {
    return getBrandCampaigns(ctx.req.user.id);
  }),

  getActiveCampaigns: privateProcedure
    .input(
      yup.object({
        page: yup.number().min(1).default(1),
        limit: yup.number().min(1).max(50).default(10),
        filters: yup
          .object({
            categories: yup.array().of(yup.string()).optional(),
            location: yup.string().optional(),
            minPrice: yup.number().min(0).optional(),
            maxPrice: yup.number().min(0).optional(),
            search: yup.string().optional(),
            daysToComplete: yup
              .object({
                min: yup.number().min(0).optional(),
                max: yup.number().optional(),
              })
              .optional(),
            deliverables: yup
              .object({
                min: yup.number().min(0).optional(),
                max: yup.number().min(0).optional(),
              })
              .optional(),
          })
          .optional(),
      }),
    )
    .query(async ({ input }) => {
      return getActiveCampaigns(input.page, input.limit, input.filters);
    }),

  submitApplication: privateProcedure
    .input(campaignApplicationSchema)
    .mutation(async ({ ctx, input }) => {
      return applyCampaign(ctx.req.user.id, ctx.req.user.userType, input);
    }),

  getAthleteApplications: privateProcedure.query(async ({ ctx }) => {
    return getAthleteApplications(ctx.req.user.id);
  }),

  getCampaignsForAthlete: privateProcedure.query(async ({ ctx }) => {
    return getCampaignsForAthlete(ctx.req.user.id);
  }),

  getCampaignApplications: privateProcedure
    .input(
      yup.object({
        campaignId: yup.string().required(),
      }),
    )
    .query(async ({ ctx, input }) => {
      return getCampaignApplications(ctx.req.user.id, input.campaignId);
    }),

  updateApplicationStatus: privateProcedure
    .input(updateApplicationStatusSchema)
    .mutation(async ({ ctx, input }) => {
      return updateApplicationStatus(ctx.req.user.id, input);
    }),

  delete: privateProcedure
    .input(
      yup.object({
        campaignId: yup.string().required(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return deleteCampaign(ctx.req.user.id, input.campaignId);
    }),

  getById: privateProcedure
    .input(
      yup.object({
        campaignId: yup.string().required(),
      }),
    )
    .query(async ({ ctx, input }) => {
      return getCampaignById(ctx.req.user.id, input.campaignId);
    }),

  getByCampaignId: privateProcedure
    .input(
      yup.object({
        campaignId: yup.string().required(),
      }),
    )
    .query(async ({ ctx, input }) => {
      return getCampaignByCampaignId(input.campaignId);
    }),

  getCampaignsByBrandId: privateProcedure
    .input(
      yup.object({
        brandId: yup.string().required(),
      }),
    )
    .query(async ({ ctx, input }) => {
      return getCampaignsByBrandId(input.brandId);
    }),

  getUploadUrl: privateProcedure
    .input(
      yup.object({
        fileType: yup.string().required(),
        uploadType: yup.string().required(),
      }),
    )
    .mutation(async ({ input }) => {
      const { fileType, uploadType } = input;
      const key = `${uploadType}/${Date.now()}-${Math.random().toString(36).substring(7)}`;
      const url = await getSignedUrl(key, fileType);
      const publicUrl = `https://${process.env.AWS_BUCKET_NAME}.s3.${process.env.AWS_REGION}.amazonaws.com/${key}`;
      return { url, key, publicUrl };
    }),

  inviteAthletes: privateProcedure
    .input(
      yup.object({
        campaignId: yup.string().required(),
        athleteIds: yup.array().of(yup.string().required()).required(),
        deliverablePricing: yup.object().optional(), // Record<string, number>
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return inviteAthletesToCampaign(
        ctx.req.user.id,
        ctx.req.user.userType,
        input,
      );
    }),

  respondToInvite: privateProcedure
    .input(
      yup.object({
        campaignId: yup.string().required(),
        accept: yup.boolean().required(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return respondToCampaignInvite(
        ctx.req.user.id,
        ctx.req.user.userType,
        input,
      );
    }),

  checkAthleteRelationships: privateProcedure
    .input(
      yup.object({
        campaignId: yup.string().required(),
        athleteIds: yup.array().of(yup.string().required()).required(),
      }),
    )
    .query(async ({ ctx, input }) => {
      return checkAthleteRelationshipWithCampaign(
        ctx.req.user.id,
        ctx.req.user.userType,
        input,
      );
    }),
});
