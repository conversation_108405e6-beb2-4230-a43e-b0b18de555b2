import type { Metadata } from "next";

import ClientLayout from "@/app/(public)/client-layout";

export const metadata: Metadata = {
  title: "AIMS - Athlete & Influencer Marketing Solutions",
  description:
    "Connect with college athletes and influencers for authentic brand partnerships. AIMS helps local businesses and athletes create meaningful marketing collaborations.",
  keywords:
    "athlete marketing, influencer marketing, college athletes, brand partnerships, sports marketing, social media marketing",
  openGraph: {
    title: "AIMS - Athlete & Influencer Marketing Solutions",
    description:
      "Connect with college athletes and influencers for authentic brand partnerships. AIMS helps local businesses and athletes create meaningful marketing collaborations.",
    type: "website",
    locale: "en_US",
    siteName: "AIMS",
  },
  twitter: {
    card: "summary_large_image",
    title: "AIMS - Athlete & Influencer Marketing Solutions",
    description:
      "Connect with college athletes and influencers for authentic brand partnerships.",
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function PublicLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <ClientLayout>{children}</ClientLayout>;
}
