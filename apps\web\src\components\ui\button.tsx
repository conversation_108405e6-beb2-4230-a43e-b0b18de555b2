import type { VariantProps } from "class-variance-authority";
import * as React from "react";
import { cn } from "@/lib/utils";
import { Slot } from "@radix-ui/react-slot";
import { cva } from "class-variance-authority";

const buttonVariants = cva(
  "tw-inline-flex tw-items-center tw-justify-center tw-whitespace-nowrap tw-rounded-lg tw-text-sm tw-font-medium tw-transition-colors focus:tw-outline-none focus:tw-ring-1 focus:tw-ring-offset-1 disabled:tw-pointer-events-none disabled:tw-opacity-50 disabled:tw-cursor-not-allowed",
  {
    variants: {
      variant: {
        default:
          "tw-bg-aims-primary hover:tw-bg-aims-primary/90 focus:tw-ring-aims-primary",
        secondary:
          "tw-bg-aims-secondary hover:tw-bg-aims-secondary/90 focus:tw-ring-aims-secondary",
        outline:
          "tw-border tw-border-aims-dark-3 tw-bg-transparent hover:tw-bg-aims-dark-2 focus:tw-ring-aims-primary",
        ghost: "tw-text-aims-text-primary",
        destructive:
          "tw-bg-red-500 hover:tw-bg-red-500/90 focus:tw-ring-red-500",
        link: "tw-text-aims-primary tw-underline-offset-4 hover:tw-underline",
      },
      size: {
        default: "tw-h-10 tw-px-4 tw-py-2",
        sm: "tw-h-9 tw-rounded-md tw-px-3",
        lg: "tw-h-11 tw-rounded-md tw-px-8",
        icon: "tw-h-10 tw-w-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  },
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button";
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    );
  },
);
Button.displayName = "Button";

export { Button, buttonVariants };
