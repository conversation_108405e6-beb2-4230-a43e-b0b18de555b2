import {
  createChat,
  getChatById,
  getChats,
  getMess<PERSON>,
  mark<PERSON><PERSON><PERSON>,
  sendMessage,
} from "../controllers/chat";
import { privateProcedure, trpc } from "../lib/trpc";
import {
  chatIdSchema,
  createChatSchema,
  getMessagesSchema,
  sendMessageSchema,
  SendMessageInput,
} from "../validators/chat";

export const chatRouter = trpc.router({
  // Get all chats for the current user
  getChats: privateProcedure.query(async ({ ctx }) => {
    return getChats(ctx.req.user.id);
  }),

  // Get a specific chat by ID
  getChatById: privateProcedure
    .input(chatIdSchema)
    .query(async ({ ctx, input }) => {
      return getChatById(ctx.req.user.id, input.chatId);
    }),

  // Get messages for a specific chat
  getMessages: privateProcedure
    .input(getMessagesSchema)
    .query(async ({ ctx, input }) => {
      return getMessages(
        input.chatId,
        ctx.req.user.id,
        input.before,
        input.limit,
      );
    }),

  // Create a new chat
  createChat: privateProcedure
    .input(createChatSchema)
    .mutation(async ({ ctx, input }) => {
      return createChat(
        ctx.req.user.id,
        input.participantIds,
        input.type,
        input.campaignId,
      );
    }),

  // Send a message
  sendMessage: privateProcedure
    .input(sendMessageSchema)
    .mutation(async ({ ctx, input }: { ctx: any; input: SendMessageInput }) => {
      return sendMessage(
        input.chatId,
        ctx.req.user.id,
        input.content,
        input.type,
        input.campaignId,
        input.contractId,
      );
    }),

  // Mark messages as read
  markAsRead: privateProcedure
    .input(chatIdSchema)
    .mutation(async ({ ctx, input }) => {
      return markAsRead(input.chatId, ctx.req.user.id);
    }),
});
