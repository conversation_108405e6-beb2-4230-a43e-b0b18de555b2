"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { LoadingSpinner } from "@/components/ui/LoadingSpinner";
import { client } from "@/lib/trpc/client";
import { CheckCircleIcon } from "@heroicons/react/24/solid";

export default function SubscribePage() {
  const [isLoading, setIsLoading] = useState(false);
  async function createCheckoutSession() {
    setIsLoading(true);
    const result = await client.brand.createCheckoutSession.mutate();
    if (result.url) {
      setTimeout(() => {
        window.location.href = result.url!;
        setIsLoading(false);
      }, 1000);
    }
  }
  return (
    <div className="tw-min-h-[calc(100vh-16rem)] tw-flex tw-flex-col tw-items-center tw-justify-center tw-px-4">
      <div className="tw-max-w-2xl tw-w-full tw-text-center">
        <div className="tw-mb-8">
          <h1 className="tw-text-4xl tw-font-bold tw-text-aims-text-primary tw-mb-4">
            Subscribe to AIMS
          </h1>
          <p className="tw-text-xl tw-text-aims-text-secondary tw-mb-8">
            Unlock the full potential of athlete and influencer marketing
          </p>
        </div>

        <div className="tw-bg-aims-dark-2 tw-rounded-xl tw-p-8 tw-mb-8">
          <div className="tw-flex tw-items-center tw-justify-center tw-gap-4 tw-mb-6">
            <span className="tw-text-3xl tw-font-bold tw-text-aims-text-primary">
              $10
            </span>
            <span className="tw-text-aims-text-secondary">/month</span>
          </div>

          <ul className="tw-space-y-4 tw-mb-8 tw-text-left">
            <li className="tw-flex tw-items-center tw-gap-2">
              <CheckCircleIcon className="tw-w-5 tw-h-5 tw-text-green-500" />
              <span className="tw-text-aims-text-secondary">
                Access to verified athletes and influencers
              </span>
            </li>
            <li className="tw-flex tw-items-center tw-gap-2">
              <CheckCircleIcon className="tw-w-5 tw-h-5 tw-text-green-500" />
              <span className="tw-text-aims-text-secondary">
                Advanced campaign management tools
              </span>
            </li>
            <li className="tw-flex tw-items-center tw-gap-2">
              <CheckCircleIcon className="tw-w-5 tw-h-5 tw-text-green-500" />
              <span className="tw-text-aims-text-secondary">
                Analytics and performance tracking
              </span>
            </li>
          </ul>

          <Button
            onClick={createCheckoutSession}
            disabled={isLoading}
            className="tw-w-full tw-py-6 tw-text-lg"
          >
            {isLoading ? (
              <div className="tw-flex tw-items-center tw-justify-center tw-gap-2">
                <span>Redirecting to Stripe...</span>
                <LoadingSpinner color="black" />
              </div>
            ) : (
              "Subscribe Now"
            )}
          </Button>
        </div>

        <p className="tw-text-sm tw-text-aims-text-secondary">
          Cancel anytime. No hidden fees.
        </p>
      </div>
    </div>
  );
}
