import React from "react";

// Generic column definition for type safety and flexibility
export type TableColumn<T> = {
  header: React.ReactNode;
  accessor?: keyof T | ((row: T) => React.ReactNode);
  className?: string;
  cell?: (row: T) => React.ReactNode;
};

export type TableProps<T> = {
  columns: TableColumn<T>[];
  data: T[];
  rowKey: (row: T, idx: number) => React.Key;
  actions?: (row: T) => React.ReactNode;
  emptyState?: React.ReactNode;
  tableClassName?: string;
  theadClassName?: string;
  tbodyClassName?: string;
  expandedRowRender?: (row: T) => React.ReactNode;
  expandedRowKeys?: React.Key[];
};

export function Table<T>({
  columns,
  data,
  rowKey,
  actions,
  emptyState,
  tableClassName = "tw-min-w-full tw-divide-y tw-divide-aims-dark-3 tw-rounded-lg tw-overflow-hidden",
  theadClassName = "tw-bg-aims-dark-3",
  tbodyClassName = "tw-divide-y tw-divide-aims-dark-3 tw-bg-aims-dark-2",
  expandedRowRender,
  expandedRowKeys = [],
}: TableProps<T>) {
  return (
    <table className={tableClassName}>
      <thead className={theadClassName}>
        <tr>
          {expandedRowRender && (
            <th className="tw-w-2 tw-py-3.5 tw-pr-1 tw-text-left tw-text-sm tw-font-medium tw-text-aims-text-secondary tw-sm:tw-pl-6">
              <span className="tw-sr-only">Expand</span>
            </th>
          )}
          {columns.map((col, i) => (
            <th key={i} className={col.className}>
              {col.header}
            </th>
          ))}
          {actions && (
            <th className="tw-relative tw-py-3.5 tw-pl-3 tw-pr-4 tw-sm:tw-pr-6">
              <span className="tw-sr-only">Actions</span>
            </th>
          )}
        </tr>
      </thead>
      <tbody className={tbodyClassName}>
        {data.length === 0 ? (
          <tr>
            <td
              colSpan={
                columns.length + (actions ? 1 : 0) + (expandedRowRender ? 1 : 0)
              }
              className="tw-px-3 tw-py-8 tw-text-center"
            >
              {emptyState || <span>No data found</span>}
            </td>
          </tr>
        ) : (
          data.map((row, idx) => {
            const rowId = rowKey(row, idx);
            const isExpanded = expandedRowKeys.includes(rowId);

            return (
              <React.Fragment key={rowId}>
                <tr>
                  {expandedRowRender && (
                    <td className="tw-w-2 tw-py-3.5 tw-pr-1 tw-text-left tw-text-sm tw-font-medium tw-text-aims-text-secondary tw-sm:tw-pl-6">
                      <div className="tw-h-4 tw-w-4" />
                    </td>
                  )}
                  {columns.map((col, i) => (
                    <td key={i} className={col.className}>
                      {col.cell
                        ? col.cell(row)
                        : typeof col.accessor === "function"
                          ? col.accessor(row)
                          : col.accessor
                            ? (row as any)[col.accessor]
                            : null}
                    </td>
                  ))}
                  {actions && (
                    <td className="tw-relative tw-whitespace-nowrap tw-py-4 tw-pl-3 tw-pr-4 tw-text-right tw-text-sm tw-font-medium tw-sm:tw-pr-6">
                      {actions(row)}
                    </td>
                  )}
                </tr>
                {expandedRowRender && isExpanded && (
                  <tr>
                    <td
                      colSpan={columns.length + (actions ? 1 : 0) + 1}
                      className="tw-bg-aims-dark-2 tw-border-t tw-border-aims-dark-3"
                    >
                      {expandedRowRender(row)}
                    </td>
                  </tr>
                )}
              </React.Fragment>
            );
          })
        )}
      </tbody>
    </table>
  );
}
