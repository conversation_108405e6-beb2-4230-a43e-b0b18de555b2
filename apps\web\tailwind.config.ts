import type { Config } from "tailwindcss";

const config: Config = {
  prefix: "tw-",
  darkMode: ["class"],
  content: ["./src/**/*.{js,ts,jsx,tsx,mdx}"],
  theme: {
    extend: {
      fontFamily: {
        sans: ["var(--font-poppins)", "sans-serif"],
        poppins: ["var(--font-poppins)", "sans-serif"],
      },
      fontSize: {
        // Special headings based on the design
        h1: ["60px", { lineHeight: "72px", fontWeight: "700" }], // Poppins Bold 60px/72px
        h2: ["48px", { lineHeight: "58px", fontWeight: "700" }], // Poppins Bold 48px/58px
        h3: ["40px", { lineHeight: "48px", fontWeight: "700" }], // Poppins Bold 40px/48px
        h4: ["30px", { lineHeight: "36px", fontWeight: "700" }], // Poppins Bold 30px/36px
        h5: ["24px", { lineHeight: "32px", fontWeight: "600" }], // Poppins SemiBold 24px
      },
      colors: {
        // AIMS Brand Colors
        aims: {
          // Primary Colors
          primary: "#FC8D00", // Primary Orange
          secondary: "#333A48", // Secondary Dark Blue

          // Text Colors
          text: {
            primary: "#FFFFFF",
            secondary: "#DEE4EE",
          },

          // Background Colors
          bg: {
            DEFAULT: "#111928", // Background
            secondary: "#101010", // Background 2
            tertiary: "#1F1F1F", // Background 3
          },

          // Dark Color Scale
          dark: {
            1: "#111520",
            2: "#1F2A37",
            3: "#374151",
            4: "#4B5563",
            5: "#6B7280",
            6: "#9CA3AF",
            7: "#D1D5DB",
            8: "#E5E7EB",
          },
        },

        background: "rgb(var(--background-rgb))",
        foreground: "rgb(var(--foreground-rgb))",
      },
      height: {
        navbar: "4rem", // 64px
      },
      spacing: {
        navbar: "4rem", // 64px
      },
      zIndex: {
        navbar: "100",
      },
      keyframes: {
        "toast-in": {
          "0%": { opacity: "0", transform: "translateY(-20px) scale(0.96)" },
          "100%": { opacity: "1", transform: "translateY(0) scale(1)" },
        },
        "toast-out": {
          "0%": { opacity: "1", transform: "translateY(0) scale(1)" },
          "100%": { opacity: "0", transform: "translateY(-20px) scale(0.96)" },
        },
      },
      animation: {
        "toast-in": "toast-in 0.35s cubic-bezier(0.16, 1, 0.3, 1)",
        "toast-out": "toast-out 0.2s cubic-bezier(0.16, 1, 0.3, 1) forwards",
      },
    },
  },
};

export default config;
