@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --font-sans: var(--font-poppins);
}

@layer base {
  h1 {
    @apply tw-text-h1 tw-font-bold tw-text-aims-text-primary;
  }
  h2 {
    @apply tw-text-h2 tw-font-bold tw-text-aims-text-primary;
  }
  h3 {
    @apply tw-text-h3 tw-font-bold tw-text-aims-text-primary;
  }
  h4 {
    @apply tw-text-h4 tw-font-bold tw-text-aims-text-primary;
  }
  h5 {
    @apply tw-text-h5 tw-font-semibold tw-text-aims-text-primary;
  }
  p {
    @apply tw-text-base tw-text-aims-text-primary;
  }
}

@layer components {
  /* Typography utility classes */
  .heading-h1 {
    @apply tw-text-h1 tw-font-bold tw-text-aims-text-primary;
  }
  .heading-h2 {
    @apply tw-text-h2 tw-font-bold tw-text-aims-text-primary;
  }
  .heading-h3 {
    @apply tw-text-h3 tw-font-bold tw-text-aims-text-primary;
  }
  .heading-h4 {
    @apply tw-text-h4 tw-font-bold tw-text-aims-text-primary;
  }
  .heading-h5 {
    @apply tw-text-h5 tw-font-semibold tw-text-aims-text-primary;
  }
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
  }
}

body {
  font-family: var(--font-sans), Arial, sans-serif;
}

/* Hide number input spinners for Chrome, Safari, Edge, Opera */
.no-spinner::-webkit-outer-spin-button,
.no-spinner::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Hide number input spinners for Firefox */
.no-spinner[type="number"] {
  appearance: textfield;
  -moz-appearance: textfield;
}

/* Mobile-friendly scrolling improvements */
@media (max-width: 640px) {
  /* Smooth scrolling for horizontal filter containers */
  .tw-overflow-x-auto {
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
  }

  .tw-overflow-x-auto::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }

  /* Improve touch targets for mobile */
  button, a, [role="button"] {
    min-height: 44px;
    min-width: 44px;
  }

  /* Better tap highlighting */
  * {
    -webkit-tap-highlight-color: rgba(252, 141, 0, 0.2);
  }

  /* Subtle scroll indicator for horizontal containers */
  .tw-overflow-x-auto {
    position: relative;
  }

  .tw-overflow-x-auto::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    width: 20px;
    background: linear-gradient(to left, rgba(17, 25, 40, 0.8), transparent);
    pointer-events: none;
    z-index: 1;
  }
}
