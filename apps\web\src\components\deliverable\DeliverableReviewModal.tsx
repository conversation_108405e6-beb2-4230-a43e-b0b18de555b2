"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogOverlay,
  DialogPortal,
  DialogTitle,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/toast/use-toast";
import { trpc } from "@/lib/trpc/client";
import { format } from "date-fns";
import { 
  CheckCircleIcon, 
  XCircleIcon, 
  ExclamationTriangleIcon,
  DocumentIcon,
  EyeIcon
} from "@heroicons/react/24/outline";

import {
  DeliverableSubmissionStatus,
  SerializedDeliverableSubmission,
} from "@/types/deliverableSubmission";

interface DeliverableReviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  submission: SerializedDeliverableSubmission;
  onReviewComplete?: (updatedSubmission: SerializedDeliverableSubmission) => void;
}

export function DeliverableReviewModal({
  isOpen,
  onClose,
  submission,
  onReviewComplete,
}: DeliverableReviewModalProps) {
  const { toast } = useToast();
  
  const [selectedStatus, setSelectedStatus] = useState<DeliverableSubmissionStatus | null>(null);
  const [feedback, setFeedback] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const reviewSubmission = trpc.deliverableSubmission.reviewSubmission.useMutation();

  const handleReview = async (status: DeliverableSubmissionStatus) => {
    if (status !== DeliverableSubmissionStatus.APPROVED && !feedback.trim()) {
      toast({
        variant: "destructive",
        title: "Feedback required",
        description: "Please provide feedback for rejections or revision requests",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const updatedSubmission = await reviewSubmission.mutateAsync({
        submissionId: submission.id,
        status,
        feedback: feedback.trim() || undefined,
      });

      toast({
        title: "Review submitted",
        description: `Submission has been ${status.toLowerCase().replace('_', ' ')}`,
      });

      if (onReviewComplete) {
        onReviewComplete(updatedSubmission);
      }

      onClose();
    } catch (error: any) {
      console.error("Error reviewing submission:", error);
      toast({
        variant: "destructive",
        title: "Review failed",
        description: error.message || "Failed to submit review",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (isSubmitting) return;
    setSelectedStatus(null);
    setFeedback("");
    onClose();
  };


  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogPortal>
        <DialogOverlay className="tw-fixed tw-inset-0 tw-bg-black/80 tw-backdrop-blur-sm tw-z-[100] tw-transition-opacity tw-duration-200" />
        <DialogContent className="tw-fixed tw-left-[50%] tw-top-[50%] tw-z-[101] tw-w-full tw-max-w-4xl tw-mx-4 sm:tw-mx-0 tw-translate-x-[-50%] tw-translate-y-[-50%] tw-bg-aims-dark-2 tw-border tw-border-aims-dark-4 tw-rounded-2xl tw-shadow-2xl tw-p-4 sm:tw-p-6 tw-max-h-[90vh] tw-overflow-y-auto">
          <DialogHeader className="tw-space-y-2 tw-mb-4">
            <DialogTitle className="tw-text-xl sm:tw-text-2xl tw-font-bold tw-text-aims-text-primary tw-tracking-tight">
              Review Deliverable Submission
            </DialogTitle>
            <DialogDescription className="tw-text-sm sm:tw-text-base tw-text-aims-text-secondary tw-leading-relaxed">
              Review and provide feedback for &quot;{submission.deliverable?.name}&quot;
            </DialogDescription>
          </DialogHeader>

          <div className="tw-space-y-6">
            {/* Submission Details */}
            <div className="tw-bg-aims-dark-3 tw-rounded-lg tw-p-4">
              <h3 className="tw-text-lg tw-font-semibold tw-text-aims-text-primary tw-mb-3">
                Submission Details
              </h3>
              
              <div className="tw-grid tw-grid-cols-1 sm:tw-grid-cols-2 tw-gap-4 tw-mb-4">
                <div>
                  <span className="tw-text-sm tw-font-medium tw-text-aims-text-primary tw-block tw-mb-1">
                    Athlete
                  </span>
                  <div className="tw-text-sm tw-text-aims-text-secondary">
                    {submission.athlete?.name || "Unknown"}
                  </div>
                </div>
                <div>
                  <span className="tw-text-sm tw-font-medium tw-text-aims-text-primary tw-block tw-mb-1">
                    Submitted
                  </span>
                  <div className="tw-text-sm tw-text-aims-text-secondary">
                    {format(new Date(submission.submittedAt), "MMM d, yyyy 'at' h:mm a")}
                  </div>
                </div>
              </div>

              <div className="tw-mb-4">
                <span className="tw-text-sm tw-font-medium tw-text-aims-text-primary tw-block tw-mb-2">
                  Description
                </span>
                <div className="tw-text-sm tw-text-aims-text-secondary tw-bg-aims-dark-2 tw-rounded tw-p-3">
                  {submission.description}
                </div>
              </div>

              {/* Files */}
              {submission.files && submission.files.length > 0 && (
                <div>
                  <span className="tw-text-sm tw-font-medium tw-text-aims-text-primary tw-block tw-mb-2">
                    Submitted Files ({submission.files.length})
                  </span>
                  <div className="tw-space-y-2">
                    {submission.files.map((file, index) => (
                      <div
                        key={index}
                        className="tw-flex tw-items-center tw-justify-between tw-p-3 tw-bg-aims-dark-2 tw-rounded-lg"
                      >
                        <div className="tw-flex tw-items-center tw-gap-3">
                          <DocumentIcon className="tw-w-5 tw-h-5 tw-text-aims-text-secondary" />
                          <div>
                            <p className="tw-text-sm tw-font-medium tw-text-aims-text-primary">
                              {file.originalName}
                            </p>
                            <p className="tw-text-xs tw-text-aims-text-secondary">
                              {(file.fileSize / 1024 / 1024).toFixed(2)} MB
                            </p>
                          </div>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.open(file.url, '_blank')}
                          className="tw-flex tw-items-center tw-gap-2 tw-text-aims-text-primary"
                        >
                          <EyeIcon className="tw-w-4 tw-h-4" />
                          View
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Review Actions */}
            <div className="tw-bg-aims-dark-3 tw-rounded-lg tw-p-4">
              <h3 className="tw-text-lg tw-font-semibold tw-text-aims-text-primary tw-mb-4">
                Review Decision
              </h3>

              {/* Status Buttons */}
              <div className="tw-grid tw-grid-cols-1 sm:tw-grid-cols-3 tw-gap-3 tw-mb-4">
                <Button
                  variant={selectedStatus === DeliverableSubmissionStatus.APPROVED ? "default" : "outline"}
                  onClick={() => setSelectedStatus(DeliverableSubmissionStatus.APPROVED)}
                  className={`tw-h-12 tw-flex tw-items-center tw-gap-2 ${
                    selectedStatus === DeliverableSubmissionStatus.APPROVED
                      ? "tw-text-black"
                      : "tw-text-green-600 hover:tw-text-green-700"
                  }`}
                  disabled={isSubmitting}
                >
                  <CheckCircleIcon className="tw-w-5 tw-h-5" />
                  Approve
                </Button>
                <Button
                  variant={selectedStatus === DeliverableSubmissionStatus.NEEDS_REVISION ? "default" : "outline"}
                  onClick={() => setSelectedStatus(DeliverableSubmissionStatus.NEEDS_REVISION)}
                  className={`tw-h-12 tw-flex tw-items-center tw-gap-2 ${
                    selectedStatus === DeliverableSubmissionStatus.NEEDS_REVISION
                      ? "tw-text-black"
                      : "tw-text-orange-600 hover:tw-text-orange-700"
                  }`}
                  disabled={isSubmitting}
                >
                  <ExclamationTriangleIcon className="tw-w-5 tw-h-5" />
                  Needs Revision
                </Button>
                <Button
                  variant={selectedStatus === DeliverableSubmissionStatus.REJECTED ? "default" : "outline"}
                  onClick={() => setSelectedStatus(DeliverableSubmissionStatus.REJECTED)}
                  className={`tw-h-12 tw-flex tw-items-center tw-gap-2 ${
                    selectedStatus === DeliverableSubmissionStatus.REJECTED
                      ? "tw-text-black"
                      : "tw-text-red-600 hover:tw-text-red-700"
                  }`}
                  disabled={isSubmitting}
                >
                  <XCircleIcon className="tw-w-5 tw-h-5" />
                  Reject
                </Button>
              </div>

              {/* Feedback */}
              {selectedStatus && selectedStatus !== DeliverableSubmissionStatus.APPROVED && (
                <div className="tw-mb-4">
                  <label className="tw-block tw-text-sm tw-font-medium tw-text-aims-text-primary tw-mb-2">
                    Feedback *
                  </label>
                  <Textarea
                    value={feedback}
                    onChange={(e) => setFeedback(e.target.value)}
                    placeholder="Provide specific feedback for the athlete..."
                    className="tw-min-h-[100px] tw-resize-none !tw-bg-aims-dark-2"
                    maxLength={2000}
                    disabled={isSubmitting}
                  />
                  <div className="tw-text-xs tw-text-aims-text-secondary tw-mt-1">
                    {feedback.length}/2000 characters
                  </div>
                </div>
              )}

              {selectedStatus === DeliverableSubmissionStatus.APPROVED && (
                <div className="tw-mb-4">
                  <label className="tw-block tw-text-sm tw-font-medium tw-text-aims-text-primary tw-mb-2">
                    Feedback (Optional)
                  </label>
                  <Textarea
                    value={feedback}
                    onChange={(e) => setFeedback(e.target.value)}
                    placeholder="Add any positive feedback or notes..."
                    className="tw-min-h-[80px] tw-resize-none !tw-bg-aims-dark-2"
                    maxLength={2000}
                    disabled={isSubmitting}
                  />
                </div>
              )}
            </div>
          </div>

          <DialogFooter className="tw-flex tw-flex-col sm:tw-flex-row tw-gap-2 sm:tw-gap-3 tw-pt-6">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isSubmitting}
              className="tw-h-12 sm:tw-h-10 tw-order-2 sm:tw-order-1 tw-text-aims-text-primary"
            >
              Cancel
            </Button>
            <Button
              onClick={() => selectedStatus && handleReview(selectedStatus)}
              disabled={!selectedStatus || isSubmitting}
              className="tw-h-12 sm:tw-h-10 tw-order-1 sm:tw-order-2"
            >
              {isSubmitting ? "Submitting..." : "Submit Review"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </DialogPortal>
    </Dialog>
  );
}
