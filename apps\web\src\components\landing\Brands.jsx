import { useRef } from "react";
import Image from "next/image";
import { motion, useInView } from "framer-motion";
import { Swiper, SwiperSlide } from "swiper/react";

import "swiper/css";
import "swiper/css/autoplay";

import { Autoplay } from "swiper/modules";

function Testimonial({ brandName, testimonial, author, position }) {
  return (
    <div className="tw-rounded-xl tw-shadow-lg tw-px-4 sm:tw-px-6 lg:tw-px-8 tw-py-6 sm:tw-py-8 lg:tw-py-10 tw-flex tw-flex-col tw-items-center tw-max-w-2xl tw-w-full tw-bg-aims-bg tw-h-full">
      {/* Logo + Brand Name */}
      <div
        className="tw-flex tw-items-center tw-mb-3 sm:tw-mb-4"
        style={{ minHeight: "32px" }}
      >
        {brandName ? (
          <span className="tw-text-aims-text-primary tw-font-bold tw-text-base sm:tw-text-lg tw-text-center">
            {brandName}
          </span>
        ) : (
          <span className="tw-text-base sm:tw-text-lg tw-font-bold tw-opacity-0 select-none">
            Placeholder
          </span>
        )}
      </div>
      {/* Quote */}
      <blockquote className="tw-text-aims-text-primary tw-text-center tw-text-sm sm:tw-text-base lg:tw-text-lg tw-mb-4 sm:tw-mb-6 tw-leading-relaxed">
        "{testimonial}"
      </blockquote>
      {/* Author */}
      <div className="tw-flex tw-items-center tw-gap-3 tw-mt-2">
        <div className="tw-flex tw-flex-col tw-items-center">
          <span className="tw-text-aims-text-primary tw-font-semibold tw-text-xs sm:tw-text-sm tw-text-center">
            {author} <span className="tw-text-aims-primary">/</span>{" "}
            <span className="tw-text-aims-text-secondary">{position}</span>
          </span>
        </div>
      </div>
    </div>
  );
}

function BrandLogo({ name, fileName }) {
  return (
    <div className="tw-flex tw-flex-col tw-items-center tw-gap-2">
      <div className="tw-w-[120px] sm:tw-w-[140px] lg:tw-w-[160px] tw-h-[90px] sm:tw-h-[105px] lg:tw-h-[120px] tw-flex tw-items-center tw-justify-center tw-bg-transparent">
        <Image
          src={`/landing-page/logos/${fileName}.png`}
          alt={name}
          width={140}
          height={100}
          className="tw-object-contain tw-max-w-full tw-max-h-full"
          sizes="(max-width: 640px) 120px, (max-width: 1024px) 140px, 160px"
        />
      </div>
      <span className="tw-text-aims-dark-7 tw-font-bold tw-text-xs tw-opacity-80 tw-text-center">
        {name}
      </span>
    </div>
  );
}

function Card({ imageSrc, imageAlt, title, description, children }) {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, y: 50 }}
      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
      transition={{ duration: 0.6 }}
      className="tw-bg-aims-dark-3 tw-rounded-lg tw-shadow-lg tw-flex-1 tw-flex tw-flex-col tw-items-center"
    >
      <div className="tw-w-full tw-h-60 sm:tw-h-72 lg:tw-h-80 tw-rounded-t-lg tw-overflow-hidden tw-relative">
        <Image
          src={imageSrc}
          alt={imageAlt}
          fill
          className="tw-object-cover"
          sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, 33vw"
          priority={true}
        />
      </div>
      <div className="tw-p-4 sm:tw-p-5 lg:tw-p-6 tw-w-full tw-flex tw-flex-col">
        <h3 className="tw-font-bold tw-text-base sm:tw-text-lg tw-text-aims-text-primary tw-mb-2 tw-leading-tight">
          {title}
        </h3>
        <p className="tw-text-aims-text-secondary tw-text-sm sm:tw-text-base tw-leading-relaxed">{description}</p>
        {children}
      </div>
    </motion.div>
  );
}

export default function Brands() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  const testimonials = [
    {
      testimonial:
        "Working with this platform made connecting with college athletes incredibly easy. The interface was user-friendly, and we were able to launch a campaign with three athletes in under a week. It saved us so much time compared to trying to manage everything ourselves.",
      brandName: "Thrive Supplement",
      author: "Jordan M.",
      position: "Marketing Director",
    },
    {
      testimonial:
        "As a small business owner, I wasn't sure how to get into the NIL space, but this platform walked me through everything. I found a local athlete whose values align with our brand, and the partnership has brought us a noticeable boost in engagement.",
      brandName: "CleanFuel Kitchen",
      author: "Lena T.",
      position: "Founder",
    },
    {
      testimonial:
        "This was a game-changer for our brand. The athlete profiles were detailed, communication was seamless, and the legal side was taken care of. We've already signed two more athletes for our summer campaign.",
      brandName: "Urban Gear Co.",
      author: "Chris B.",
      position: "VP of Brand Partnerships",
    },
    {
      testimonial:
        "What impressed me most was how organized the whole process was. From athlete discovery to contract execution, it felt like the platform anticipated every step we needed. It made doing NIL deals feel effortless.",
      brandName: "",
      author: "Samantha R.",
      position: "Marketing Consultant",
    },
    {
      testimonial:
        "I've tried a few NIL platforms, and this one is by far the best. The athletes were professional, the platform handled the paperwork, and our campaign was live in days. I'll definitely be using it again.",
      brandName: "FitCore Apparel",
      author: "Devin K.",
      position: "Social Media Manager",
    },
    {
      testimonial:
        "We were new to NIL deals, but the support team was incredibly helpful. They guided us through everything and made sure we picked athletes who truly fit our brand. Our campaign results have been fantastic so far.",
      brandName: "EcoGlow Beauty",
      author: "Priya D.",
      position: "Co-Founder",
    },
    {
      testimonial:
        "The platform delivered exactly what we needed—speed, reliability, and access to the right athletes. Everything from negotiations to payments was handled in one place. Can't recommend it enough.",
      brandName: "Peak Hydration",
      author: "Marcus L.",
      position: "Head of Marketing",
    },
    {
      testimonial:
        "Connecting with college athletes used to be a complicated mess, but this platform simplified it completely. Our brand saw a measurable lift in brand awareness thanks to the athletes we partnered with.",
      brandName: "NextStep Wellness",
      author: "Tina G.",
      position: "Brand Manager",
    },
  ];
  return (
    <div className="tw-bg-aims-dark-1 tw-py-8 sm:tw-py-12 lg:tw-py-16 tw-px-4 sm:tw-px-6 lg:tw-px-8 tw-min-h-[400px] sm:tw-min-h-[500px]">
      <div className="tw-max-w-7xl tw-mx-auto tw-flex tw-flex-col tw-items-center tw-justify-center tw-gap-12 sm:tw-gap-16 lg:tw-gap-24 tw-mb-24 sm:tw-mb-32 lg:tw-mb-48">
        {/* Title */}
        <motion.h2
          ref={ref}
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.6 }}
          className="tw-text-2xl md:tw-text-3xl tw-font-bold tw-text-aims-text-primary tw-text-center"
        >
          Brands that Trust AIMS
        </motion.h2>

        {/* Brand Logos Row */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="
            tw-grid tw-grid-cols-2 sm:tw-grid-cols-3 lg:tw-flex
            tw-justify-between tw-items-center
            tw-w-full
            tw-gap-4 sm:tw-gap-6 lg:tw-gap-8 xl:tw-gap-12
            tw-mx-auto
          "
        >
          <BrandLogo name="Banter" fileName="banter" />
          <BrandLogo name="Neuronova" fileName="neuronova" />
          <BrandLogo name="Backdoor BRGR" fileName="back-door-brgr" />
          <BrandLogo name="Blue Unicorn" fileName="blue-unicorn" />
          <BrandLogo name="Wingman Coastal" fileName="wingman-coastal" />
        </motion.div>

        {/* Testimonial Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="tw-w-full tw-h-[420px]"
        >
          <Swiper
            modules={[Autoplay]}
            spaceBetween={32}
            slidesPerView={1}
            loop={true}
            autoplay={{ delay: 10000, disableOnInteraction: false }}
            breakpoints={{
              640: { slidesPerView: 1 },
              1024: { slidesPerView: 2 },
              1280: { slidesPerView: 3 },
            }}
            className="tw-pb-8 tw-h-full"
          >
            {testimonials.map((testimonial, index) => (
              <SwiperSlide
                key={index}
                className="
                  tw-h-full tw-flex tw-justify-center tw-items-center
                "
              >
                <div className="tw-w-full tw-flex tw-justify-center">
                  <Testimonial {...testimonial} />
                </div>
              </SwiperSlide>
            ))}
          </Swiper>
        </motion.div>
      </div>
      <div className="tw-max-w-7xl tw-mx-auto tw-flex tw-flex-col tw-items-center tw-justify-center tw-gap-6">
        <motion.h2
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="tw-text-2xl md:tw-text-3xl tw-font-bold tw-text-aims-text-primary tw-text-center"
        >
          How AIMS works for Brands
        </motion.h2>
        <motion.p
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="tw-text-aims-dark-6 tw-text-center tw-max-w-2xl"
        >
          Athlete &amp; Influencer Marketing Solutions (AIMS) makes it
          incredibly easy to connect with the right college athletes and run
          compliant, effective NIL campaigns.
        </motion.p>
        {/* Cards Row */}
        <div className="tw-flex tw-flex-col sm:tw-flex-row tw-gap-4 sm:tw-gap-6 lg:tw-gap-8 tw-mt-6 sm:tw-mt-8 tw-w-full">
          <Card
            imageSrc="/landing-page/create-campaign.png"
            imageAlt="Create Campaign"
            title="Create Your Campaign"
            description="Create campaigns with clear deadlines, deliverables, and pricing. Choose from a variety of deliverable options to fit your goals and launch with ease."
          />
          <Card
            imageSrc="/landing-page/athlete-partnerships.png"
            imageAlt="Athlete Partnerships"
            title="Athlete Partnerships, Perfectly Matched"
            description="Build athlete partnerships with talent that fits your brand. Easy, Fast, On Brand Audiences."
          />
          <Card
            imageSrc="/landing-page/campaign-complete.png"
            imageAlt="Campaign Complete"
            title="Content Approved, Campaign Complete"
            description="After campaign deliverables are approved, you can focus on growing your business while we take care of payouts."
          />
        </div>
      </div>
    </div>
  );
}
