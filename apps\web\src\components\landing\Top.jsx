import Image from "next/image";
import Link from "next/link";
import { motion } from "framer-motion";

import Book<PERSON><PERSON> from "../ui/BookACall";
import { Button } from "../ui/button";

export default function Top() {
  return (
    <div className="tw-bg-gradient-to-r tw-from-aims-bg tw-via-aims-bg tw-to-[#15337a] tw-py-8 sm:tw-py-10 md:tw-pt-16 tw-pb-0 tw-px-4 sm:tw-px-6 lg:tw-px-8 tw-min-h-[320px] sm:tw-min-h-[350px] md:tw-min-h-[450px]">
      <div className="tw-max-w-7xl tw-mx-auto tw-flex tw-flex-col md:tw-flex-row tw-items-center tw-gap-6 sm:tw-gap-10 md:tw-gap-16">
        {/* Left Side */}
        <motion.div
          className="tw-max-w-xl tw-w-full"
          initial={{ opacity: 0, x: -50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <h1 className="tw-text-2xl sm:tw-text-3xl md:tw-text-4xl lg:tw-text-5xl tw-font-extrabold tw-text-aims-text-primary tw-leading-tight">
            Athlete Influencer Marketing Made <br />{" "}
            <span className="tw-text-aims-primary">Easy</span>
          </h1>
          <p className="tw-text-sm sm:tw-text-base md:tw-text-lg tw-text-aims-text-secondary tw-opacity-80 tw-mt-3 sm:tw-mt-4 tw-mb-6 sm:tw-mb-8 tw-leading-relaxed">
            AIMS connects your brand with trusted athletes and influencers for
            impactful, creative, and budget-friendly marketing.
          </p>
          <div
            className="tw-flex tw-flex-col sm:tw-flex-row tw-gap-3 sm:tw-gap-4"
            role="group"
            aria-label="Call to action buttons"
          >
            <div className="sm:tw-w-auto">
              <BookACall />
            </div>
            <Button variant="ghost" asChild className="sm:tw-w-auto tw-h-12 sm:tw-h-10 tw-text-sm sm:tw-text-base">
              <Link
                href="/auth/register?userType=athlete"
                aria-label="Register as an athlete"
                className="tw-flex tw-items-center tw-justify-center tw-min-h-[44px]"
              >
                Athlete Registration
              </Link>
            </Button>
          </div>
        </motion.div>
        {/* Right Side */}
        <motion.div
          className="tw-relative tw-w-full tw-h-[240px] sm:tw-h-[300px] md:tw-w-[440px] md:tw-h-[600px] tw-mt-6 sm:tw-mt-8 md:tw-mt-0 tw-flex-shrink-0"
          initial={{ opacity: 0, x: 50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <Image
            src="/landing-page/athlete.png"
            alt="College athlete holding a football, representing AIMS athlete marketing platform"
            fill
            priority
            style={{ objectFit: "contain" }}
            sizes="(max-width: 768px) 100vw, 440px"
          />
        </motion.div>
      </div>
    </div>
  );
}
