import { Document, model, Schema } from "mongoose";
import { ChatType } from "../types/chat";

export interface ChatDocument extends Document {
  _id: string;
  participants: Schema.Types.ObjectId[];
  type: ChatType;
  campaignId?: Schema.Types.ObjectId;
  lastMessage?: Schema.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

export interface Chat {
  id: string;
  participants: string[];
  type: ChatType;
  campaignId?: string;
  lastMessage?: string;
  createdAt: string;
  updatedAt: string;
}

const chatSchema = new Schema<ChatDocument>(
  {
    participants: [
      {
        type: Schema.Types.ObjectId,
        ref: "Users",
        required: true,
      },
    ],
    type: {
      type: String,
      enum: Object.values(ChatType),
      required: true,
    },
    campaignId: {
      type: Schema.Types.ObjectId,
      ref: "Campaigns",
    },
    lastMessage: {
      type: Schema.Types.ObjectId,
      ref: "Messages",
    },
  },
  { timestamps: true },
);

// Add indexes for faster querying
chatSchema.index({ participants: 1 });
chatSchema.index({ type: 1, campaignId: 1 });

// Add method to transform document to client format
chatSchema.methods.toClient = function (): Chat {
  return {
    id: this._id.toString(),
    participants: this.participants.map((p: Schema.Types.ObjectId) =>
      p.toString(),
    ),
    type: this.type,
    campaignId: this.campaignId?.toString(),
    lastMessage: this.lastMessage?.toString(),
    createdAt: this.createdAt.toISOString(),
    updatedAt: this.updatedAt.toISOString(),
  };
};

const ChatModel = model("Chats", chatSchema);
export default ChatModel;
