"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import { DeliverablesTable } from "@/components/ui/DeliverablesTable";
import { FullPageLoadingSpinner } from "@/components/ui/LoadingSpinner";
import { InvitationBottomBar } from "@/components/ui/InvitationBottomBar";
import { Table, TableColumn } from "@/components/ui/Table";
import { useToast } from "@/components/ui/toast/use-toast";
import { trpc } from "@/lib/trpc/client";
import { ChevronDownIcon, ChevronUpIcon } from "@heroicons/react/24/outline";
import { format } from "date-fns";

import { CampaignStatus, SerializedCampaign } from "@repo/server/src/types/campaign";

interface AthleteRelationship {
  athleteId: string;
  hasRelationship: boolean;
  relationshipType: string | null;
  relationshipStatus: string | null;
}

export default function InviteAthletesPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { toast } = useToast();
  const [selectedAthletes, setSelectedAthletes] = useState<string[]>([]);
  const [isInviting, setIsInviting] = useState(false);
  const [selectedCampaign, setSelectedCampaign] = useState<string | null>(null);
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());

  const { data: campaigns, isLoading } =
    trpc.campaign.getBrandCampaigns.useQuery();
  const utils = trpc.useUtils();

  // Filter active campaigns on the client side
  const activeCampaigns =
    campaigns?.filter(
      (campaign) => campaign.status === CampaignStatus.ACTIVE,
    ) ?? [];

  useEffect(() => {
    // Read selected athletes from URL
    const athletesParam = searchParams.get("athletes");
    if (athletesParam) {
      try {
        const decodedAthletes = JSON.parse(decodeURIComponent(athletesParam));
        setSelectedAthletes(decodedAthletes);
      } catch (error) {
        console.error("Failed to parse athletes from URL:", error);
        toast({
          title: "Error",
          description: "Failed to load selected athletes. Please try again.",
          variant: "destructive",
        });
        router.push("/app/brand/athletes");
      }
    } else {
      // If no athletes are selected, redirect back to athletes page
      router.push("/app/brand/athletes");
    }
  }, [searchParams, router, toast]);

  const toggleRow = (campaignId: string) => {
    const newExpandedRows = new Set(expandedRows);
    if (newExpandedRows.has(campaignId)) {
      newExpandedRows.delete(campaignId);
    } else {
      newExpandedRows.add(campaignId);
    }
    setExpandedRows(newExpandedRows);
  };

  const handleBack = () => {
    router.push("/app/brand/athletes");
  };

  const handleInviteClick = async () => {
    if (!selectedCampaign) return;

    try {
      setIsInviting(true);

      // Check for existing relationships to prevent duplicate invitations
      // This validates that athletes don't already have:
      // 1. Confirmed participation in the campaign
      // 2. Pending invitations to the campaign
      // 3. Existing applications (pending, accepted, or rejected)
      const relationships = await utils.campaign.checkAthleteRelationships.fetch({
        campaignId: selectedCampaign,
        athleteIds: selectedAthletes,
      });

      // Find athletes with existing relationships
      const athletesWithRelationships = relationships.filter(
        (rel) => rel.hasRelationship,
      );

      if (athletesWithRelationships.length > 0) {
        // Group athletes by relationship type for better error messaging
        const participantCount = athletesWithRelationships.filter(
          (rel: AthleteRelationship) => rel.relationshipType === "participant",
        ).length;
        const pendingCount = athletesWithRelationships.filter(
          (rel: AthleteRelationship) => rel.relationshipType === "application" && rel.relationshipStatus === "PENDING",
        ).length;
        const acceptedCount = athletesWithRelationships.filter(
          (rel: AthleteRelationship) => rel.relationshipType === "application" && rel.relationshipStatus === "ACCEPTED",
        ).length;

        const errorParts = [];
        if (participantCount > 0) {
          errorParts.push(
            `${participantCount} athlete${participantCount > 1 ? "s" : ""} already participating`,
          );
        }
        if (pendingCount > 0) {
          errorParts.push(
            `${pendingCount} athlete${pendingCount > 1 ? "s" : ""} already invited (pending)`,
          );
        }
        if (acceptedCount > 0) {
          errorParts.push(
            `${acceptedCount} athlete${acceptedCount > 1 ? "s" : ""} already accepted`,
          );
        }

        const errorMessage =
          athletesWithRelationships.length === selectedAthletes.length
            ? `Cannot invite any athletes: ${errorParts.join(", ")}`
            : `Cannot invite ${athletesWithRelationships.length} of ${selectedAthletes.length} athletes: ${errorParts.join(", ")}`;

        toast({
          title: "Invitation Blocked",
          description: errorMessage,
          variant: "destructive",
        });
        return;
      }

      // If no existing relationships, proceed to payment page
      router.push(
        `/app/brand/athletes/payment?campaign=${selectedCampaign}&athletes=${encodeURIComponent(JSON.stringify(selectedAthletes))}`,
      );
    } catch (error) {
      console.error("Failed to check athlete relationships:", error);
      toast({
        title: "Error",
        description: "Failed to validate athlete eligibility. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsInviting(false);
    }
  };

  const columns: TableColumn<SerializedCampaign>[] = [
    {
      header: "",
      className:
        "tw-py-3.5 tw-pr-3 tw-text-left tw-text-sm tw-font-medium tw-text-aims-text-secondary",
      cell: (campaign) => (
        <input
          type="radio"
          name="campaign"
          checked={selectedCampaign === campaign.id}
          onChange={() => setSelectedCampaign(campaign.id)}
          className="tw-h-4 tw-w-4 tw-border-aims-dark-3 tw-text-aims-accent focus:tw-ring-aims-accent"
        />
      ),
    },
    {
      header: "Name",
      className:
        "tw-py-3.5 tw-pl-4 tw-pr-3 tw-text-left tw-text-sm tw-font-medium tw-text-aims-text-secondary tw-sm:tw-pl-6",
      cell: (campaign) => (
        <div className="tw-flex tw-flex-col">
          <div className="tw-font-medium tw-text-aims-text-primary">
            {campaign.name}
          </div>
        </div>
      ),
    },
    {
      header: "Price",
      className:
        "tw-px-3 tw-py-3.5 tw-text-left tw-text-sm tw-font-medium tw-text-aims-text-secondary",
      cell: (campaign) => (
        <span className="tw-text-sm tw-text-aims-text-secondary">
          {campaign.price.toLocaleString("en-US", {
            style: "currency",
            currency: "USD",
          })}
        </span>
      ),
    },
    {
      header: "Start Date",
      className:
        "tw-px-3 tw-py-3.5 tw-text-left tw-text-sm tw-font-medium tw-text-aims-text-secondary",
      cell: (campaign) => (
        <span className="tw-text-sm tw-text-aims-text-secondary">
          {format(new Date(campaign.startDate), "MMM d, yyyy")}
        </span>
      ),
    },
    {
      header: "End Date",
      className:
        "tw-px-3 tw-py-3.5 tw-text-left tw-text-sm tw-font-medium tw-text-aims-text-secondary",
      cell: (campaign) => (
        <span className="tw-text-sm tw-text-aims-text-secondary">
          {format(new Date(campaign.endDate), "MMM d, yyyy")}
        </span>
      ),
    },
    {
      header: "Deliverables",
      className:
        "tw-px-3 tw-py-3.5 tw-text-left tw-text-sm tw-font-medium tw-text-aims-text-secondary",
      cell: (campaign) => (
        <span className="tw-text-aims-text-secondary">
          {campaign.deliverables.length}
        </span>
      ),
    },
  ];

  if (isLoading) {
    return <FullPageLoadingSpinner />;
  }

  return (
    <div className="tw-container tw-mx-auto tw-py-8 tw-px-4 sm:tw-px-6 lg:tw-px-8 tw-pb-32">
      <div className="tw-mb-4">
        <h4>Select Campaign</h4>
        <p className="tw-mt-2 tw-text-sm tw-text-aims-text-secondary">
          Choose a campaign to invite {selectedAthletes.length} selected
          athlete{selectedAthletes.length !== 1 ? "s" : ""} to.
        </p>
      </div>
      <div className="tw-mt-8 tw-flow-root">
        <div className="tw--mx-4 tw--my-2 tw-overflow-x-auto tw-sm:tw--mx-6 lg:tw--mx-8">
          <div className="tw-inline-block tw-min-w-full tw-py-2 tw-align-middle tw-sm:tw-px-6 lg:tw-px-8">
            <div className="tw-overflow-hidden tw-rounded-lg tw-ring-1 tw-ring-aims-dark-3">
              <Table
                columns={columns}
                data={activeCampaigns}
                rowKey={(row) => row.id}
                expandedRowRender={(campaign) => (
                  <div className="tw-p-4 tw-bg-aims-bg">
                    <h5 className="tw-font-medium tw-text-aims-text-primary tw-mb-4">
                      Deliverables
                    </h5>
                    <div className="tw-overflow-hidden tw-rounded-lg tw-ring-1 tw-ring-aims-dark-3">
                      <DeliverablesTable deliverables={campaign.deliverables} />
                    </div>
                  </div>
                )}
                expandedRowKeys={Array.from(expandedRows)}
                actions={(campaign) => (
                  <div className="tw-flex tw-justify-end tw-gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="tw-text-aims-accent hover:tw-text-aims-accent/90 tw-flex tw-items-center tw-gap-1"
                      onClick={() => toggleRow(campaign.id)}
                      title="View campaign details"
                    >
                      {expandedRows.has(campaign.id) ? "Hide" : "Show"} Details
                      {expandedRows.has(campaign.id) ? (
                        <ChevronUpIcon className="tw-h-4 tw-w-4" />
                      ) : (
                        <ChevronDownIcon className="tw-h-4 tw-w-4" />
                      )}
                    </Button>
                  </div>
                )}
                emptyState={
                  <div className="tw-flex tw-flex-col tw-items-center tw-gap-2">
                    <p className="tw-font-medium tw-text-aims-text-primary">
                      No active campaigns found
                    </p>
                    <p className="tw-text-aims-text-secondary">
                      Create a campaign first to invite athletes
                    </p>
                    <Button
                      onClick={() => router.push("/app/brand/campaigns/new")}
                      className="tw-flex tw-items-center tw-gap-2"
                    >
                      Create Campaign
                    </Button>
                  </div>
                }
              />
            </div>
          </div>
        </div>
      </div>

      <InvitationBottomBar
        onBack={handleBack}
        onNext={handleInviteClick}
        backLabel="Back"
        nextLabel={isInviting ? "Checking..." : "Continue"}
        isVisible={true}
        isLoading={isInviting}
        isNextDisabled={!selectedCampaign}
      >
        <div className="tw-text-aims-text-primary tw-text-sm sm:tw-text-base">
          {selectedAthletes.length} athlete{selectedAthletes.length !== 1 ? "s" : ""} selected
          {selectedCampaign && activeCampaigns.find(c => c.id === selectedCampaign) && (
            <span className="tw-block tw-text-aims-text-secondary tw-text-xs sm:tw-text-sm tw-mt-1">
              Campaign: {activeCampaigns.find(c => c.id === selectedCampaign)?.name}
            </span>
          )}
        </div>
      </InvitationBottomBar>
    </div>
  );
}
