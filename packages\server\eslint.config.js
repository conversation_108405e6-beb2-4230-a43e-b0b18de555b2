import baseConfig from "@repo/eslint-config/base";

/** @type {import('typescript-eslint').Config} */
export default [
  {
    ignores: ["dist/**"],
  },
  {
    rules: {
      "turbo/no-undeclared-env-vars": [
        "error",
        {
          env: {
            CLIENT_URL: true,
            JWT_SECRET: true,
            JWT_EXPIRES_IN: true,
          },
        },
      ],
    },
  },
  ...baseConfig,
];
