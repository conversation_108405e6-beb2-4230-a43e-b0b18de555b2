import { Document, model, Schema } from "mongoose";

import {
  Contract,
  ContractStatus,
  ContractType,
  ContractTerms,
  ContractParticipant,
  ContractRevision,
  SerializedContract,
  PaymentStatus,
} from "../types/contract";

// Document interface for MongoDB
export interface ContractDocument extends Document {
  _id: string;
  campaignId: Schema.Types.ObjectId;
  applicationId: Schema.Types.ObjectId;
  brandId: Schema.Types.ObjectId;
  athleteId: Schema.Types.ObjectId;
  contractNumber: string;
  type: ContractType;
  status: ContractStatus;
  version: number;
  parentContractId?: Schema.Types.ObjectId;
  title: string;
  terms: ContractTerms;
  participants: ContractParticipant[];
  pdfUrl?: string;
  pdfKey?: string;
  brandReviewedAt?: Date;
  brandApprovedAt?: Date;
  sentToAthleteAt?: Date;
  athleteSignedAt?: Date;
  brandSignedAt?: Date;
  paymentIntentId?: string;
  paymentStatus?: PaymentStatus;
  paymentInitiatedAt?: Date;
  paymentCompletedAt?: Date;
  paymentFailedAt?: Date;
  paymentFailureReason?: string;
  paymentRetryCount?: number;
  fulfilledAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  expiresAt?: Date;
  revisionHistory?: ContractRevision[];
  toClient(): SerializedContract;
}

// Sub-schemas for nested objects
const paymentScheduleSchema = new Schema(
  {
    description: { type: String, required: true },
    amount: { type: Number, required: true, min: 0 },
    dueDate: { type: Date, required: true },
    milestone: { type: String },
  },
  { _id: false }
);

const contractDeliverableSchema = new Schema(
  {
    deliverableId: { type: String, required: true },
    name: { type: String, required: true },
    description: { type: String, required: true },
    dueDate: { type: Date, required: true },
    compensation: { type: Number, required: true, min: 0 },
    requirements: [{ type: String }],
    type: { type: String, required: true },
  },
  { _id: false }
);

const contractTermsSchema = new Schema(
  {
    totalCompensation: { type: Number, required: true, min: 0 },
    paymentSchedule: [paymentScheduleSchema],
    deliverables: [contractDeliverableSchema],
    campaignDuration: {
      startDate: { type: Date, required: true },
      endDate: { type: Date, required: true },
    },
    additionalTerms: [{ type: String }],
    cancellationPolicy: { type: String },
    intellectualPropertyRights: { type: String },
    confidentialityClause: { type: String },
  },
  { _id: false }
);

const signatureDataSchema = new Schema(
  {
    ipAddress: { type: String, required: true },
    userAgent: { type: String, required: true },
    timestamp: { type: Date, required: true },
  },
  { _id: false }
);

const contractParticipantSchema = new Schema(
  {
    userId: { type: Schema.Types.ObjectId, ref: "Users", required: true },
    userType: { type: String, enum: ["brand", "athlete"], required: true },
    signedAt: { type: Date },
    signatureData: signatureDataSchema,
  },
  { _id: false }
);

const contractRevisionSchema = new Schema(
  {
    version: { type: Number, required: true },
    changedBy: { type: Schema.Types.ObjectId, ref: "Users", required: true },
    changedAt: { type: Date, required: true },
    changes: [{ type: String, required: true }],
    reason: { type: String },
  },
  { _id: false }
);

// Main contract schema
const contractSchema = new Schema<ContractDocument>(
  {
    campaignId: {
      type: Schema.Types.ObjectId,
      ref: "Campaigns",
      required: true,
    },
    applicationId: {
      type: Schema.Types.ObjectId,
      ref: "CampaignApplications",
      required: true,
    },
    brandId: {
      type: Schema.Types.ObjectId,
      ref: "Brands",
      required: true,
    },
    athleteId: {
      type: Schema.Types.ObjectId,
      ref: "Athletes",
      required: true,
    },
    contractNumber: {
      type: String,
      required: true,
      unique: true,
    },
    type: {
      type: String,
      enum: Object.values(ContractType),
      default: ContractType.CAMPAIGN_AGREEMENT,
    },
    status: {
      type: String,
      enum: Object.values(ContractStatus),
      default: ContractStatus.DRAFT,
    },
    version: {
      type: Number,
      default: 1,
      min: 1,
    },
    parentContractId: {
      type: Schema.Types.ObjectId,
      ref: "Contracts",
    },
    title: {
      type: String,
      required: true,
    },
    terms: {
      type: contractTermsSchema,
      required: true,
    },
    participants: [contractParticipantSchema],
    pdfUrl: { type: String },
    pdfKey: { type: String },
    brandReviewedAt: { type: Date },
    brandApprovedAt: { type: Date },
    sentToAthleteAt: { type: Date },
    athleteSignedAt: { type: Date },
    brandSignedAt: { type: Date },

    // Payment tracking
    paymentIntentId: { type: String },
    paymentStatus: {
      type: String,
      enum: Object.values(PaymentStatus),
    },
    paymentInitiatedAt: { type: Date },
    paymentCompletedAt: { type: Date },
    paymentFailedAt: { type: Date },
    paymentFailureReason: { type: String },
    paymentRetryCount: { type: Number, default: 0 },

    // Fulfillment tracking
    fulfilledAt: { type: Date },

    expiresAt: { type: Date },
    revisionHistory: [contractRevisionSchema],
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Indexes for efficient queries
contractSchema.index({ campaignId: 1, applicationId: 1 });
contractSchema.index({ brandId: 1, status: 1 });
contractSchema.index({ athleteId: 1, status: 1 });
// contractNumber index is already created by the unique: true in field definition
contractSchema.index({ status: 1, expiresAt: 1 });
contractSchema.index({ parentContractId: 1, version: 1 });

// Pre-save middleware to generate contract number
contractSchema.pre("save", async function (next) {
  if (this.isNew && !this.contractNumber) {
    const timestamp = Date.now().toString(36).toUpperCase();
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    this.contractNumber = `CONTRACT-${timestamp}-${random}`;
  }
  next();
});

// Also add pre-validate middleware as backup
contractSchema.pre("validate", function (next) {
  if (!this.contractNumber) {
    const timestamp = Date.now().toString(36).toUpperCase();
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    this.contractNumber = `CONTRACT-${timestamp}-${random}`;
  }
  next();
});

// Method to convert to client-safe format
contractSchema.methods.toClient = function (): SerializedContract {
  const obj = this.toObject();
  
  return {
    id: obj._id.toString(),
    campaignId: obj.campaignId.toString(),
    applicationId: obj.applicationId.toString(),
    brandId: obj.brandId.toString(),
    athleteId: obj.athleteId.toString(),
    parentContractId: obj.parentContractId?.toString(),
    contractNumber: obj.contractNumber,
    type: obj.type,
    status: obj.status,
    version: obj.version,
    title: obj.title,
    terms: {
      ...obj.terms,
      paymentSchedule: obj.terms.paymentSchedule.map((ps: any) => ({
        ...ps,
        dueDate: ps.dueDate.toISOString(),
      })),
      campaignDuration: {
        startDate: obj.terms.campaignDuration.startDate.toISOString(),
        endDate: obj.terms.campaignDuration.endDate.toISOString(),
      },
    },
    participants: obj.participants.map((p: any) => ({
      ...p,
      userId: p.userId.toString(),
      signedAt: p.signedAt?.toISOString(),
      signatureData: p.signatureData ? {
        ...p.signatureData,
        timestamp: p.signatureData.timestamp.toISOString(),
      } : undefined,
    })),
    pdfUrl: obj.pdfUrl,
    pdfKey: obj.pdfKey,
    brandReviewedAt: obj.brandReviewedAt?.toISOString(),
    brandApprovedAt: obj.brandApprovedAt?.toISOString(),
    sentToAthleteAt: obj.sentToAthleteAt?.toISOString(),
    athleteSignedAt: obj.athleteSignedAt?.toISOString(),
    brandSignedAt: obj.brandSignedAt?.toISOString(),
    paymentIntentId: obj.paymentIntentId,
    paymentStatus: obj.paymentStatus,
    paymentInitiatedAt: obj.paymentInitiatedAt?.toISOString(),
    paymentCompletedAt: obj.paymentCompletedAt?.toISOString(),
    paymentFailedAt: obj.paymentFailedAt?.toISOString(),
    paymentFailureReason: obj.paymentFailureReason,
    paymentRetryCount: obj.paymentRetryCount,
    fulfilledAt: obj.fulfilledAt?.toISOString(),
    createdAt: obj.createdAt.toISOString(),
    updatedAt: obj.updatedAt.toISOString(),
    expiresAt: obj.expiresAt?.toISOString(),
    revisionHistory: obj.revisionHistory?.map((r: any) => ({
      ...r,
      changedBy: r.changedBy.toString(),
      changedAt: r.changedAt.toISOString(),
    })),
  };
};

const ContractModel = model<ContractDocument>("Contracts", contractSchema);
export default ContractModel;
