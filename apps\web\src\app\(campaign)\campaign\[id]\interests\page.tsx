"use client";

import { useEffect } from "react";
import { useParams } from "next/navigation";
import Interests from "@/components/campaign/Interests";
import { setCurrentStep } from "@/store/slices/editCampaign";
import { useDispatch } from "react-redux";

export default function EditCampaignInterestsPage() {
  const params = useParams();
  const id =
    typeof params?.id === "string"
      ? params.id
      : Array.isArray(params?.id) && params?.id.length > 0
        ? params.id[0]
        : undefined;
  const dispatch = useDispatch();
  useEffect(() => {
    dispatch(setCurrentStep(2));
  }, [dispatch]);

  if (!id) return null;

  return <Interests isEditMode={true} />;
}
