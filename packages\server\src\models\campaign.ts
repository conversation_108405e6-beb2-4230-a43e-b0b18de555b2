import { model, Schema } from "mongoose";

import {
  ApplicationStatus,
  Campaign,
  CampaignApplication,
  CampaignStatus,
  CampaignVisibility,
  SerializedCampaign,
  SerializedCampaignApplication,
} from "../types/campaign";
import DeliverableModel, {
  deliverableSchema as baseDeliverableSchema,
} from "./deliverable";

const campaignSchema = new Schema<Campaign>(
  {
    brandId: {
      type: Schema.Types.ObjectId,
      ref: "Brands",
      required: true,
    },
    name: {
      type: String,
      required: true,
    },
    description: {
      type: String,
      required: true,
    },
    startDate: {
      type: Date,
      required: true,
    },
    endDate: {
      type: Date,
      required: true,
      validate: {
        validator: function (this: Campaign, endDate: Date) {
          return endDate > this.startDate;
        },
        message: "End date must be after start date",
      },
    },
    status: {
      type: String,
      enum: Object.values(CampaignStatus),
      default: CampaignStatus.DRAFT,
    },
    visibility: {
      type: String,
      enum: Object.values(CampaignVisibility),
      default: CampaignVisibility.PUBLIC,
    },
    price: {
      type: Number,
      default: 0,
    },
    athletes: [
      {
        type: Schema.Types.ObjectId,
        ref: "Athletes",
        default: [],
      },
    ],
    interests: [
      {
        type: String,
        default: [],
      },
    ],
    files: [
      {
        url: { type: String, required: true },
        originalName: { type: String, required: true },
        default: [],
      },
    ],
  },
  {
    timestamps: true,
  },
);

const deliverableSubSchema = new Schema(
  {
    name: { type: String, required: true },
    description: { type: String, required: true },
    daysToComplete: { type: Number, required: true, min: 1 },
    minimumPayment: { type: Number, required: true, min: 0 },
    type: {
      type: String,
      enum: Object.values(require("../types/deliverable").DeliverableType),
      required: true,
    },
  },
  { _id: false },
);

const campaignApplicationSchema = new Schema<CampaignApplication>(
  {
    campaignId: {
      type: Schema.Types.ObjectId,
      ref: "Campaigns",
      required: true,
    },
    athleteId: {
      type: Schema.Types.ObjectId,
      ref: "Athletes",
      required: true,
    },
    status: {
      type: String,
      enum: Object.values(ApplicationStatus),
      default: ApplicationStatus.PENDING,
    },
    appliedAt: {
      type: Date,
      default: Date.now,
    },
    message: {
      type: String,
    },
    compensation: {
      type: Number,
    },
    deliverables: {
      type: [deliverableSubSchema],
      required: false,
      default: [],
    },
    initiatedBy: {
      type: String,
      enum: ["brand", "athlete"],
      required: true,
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  },
);

// Create compound index to prevent multiple applications
campaignApplicationSchema.index(
  { campaignId: 1, athleteId: 1 },
  { unique: true },
);

// Create indexes for common queries
campaignSchema.index({ brandId: 1, status: 1 });
campaignSchema.index({ status: 1, startDate: 1, endDate: 1 });
campaignSchema.index({ visibility: 1 });
campaignSchema.index({ categories: 1 });

export const CampaignModel = model("Campaigns", campaignSchema);
export const CampaignApplicationModel = model(
  "CampaignApplications",
  campaignApplicationSchema,
);

export async function serializeCampaign(doc: any): Promise<SerializedCampaign> {
  const obj = doc.toObject ? doc.toObject() : doc;
  // Fetch deliverables for this campaign
  const deliverablesDocs = await DeliverableModel.find({ campaignId: obj._id });
  const deliverables = deliverablesDocs.map((d: any) =>
    d.toClient ? d.toClient() : d,
  );
  return {
    ...obj,
    id: obj._id.toString(),
    _id: undefined, // Optionally remove _id
    brandId: obj.brandId.toString(),
    createdAt:
      obj.createdAt instanceof Date
        ? obj.createdAt.toISOString()
        : obj.createdAt,
    updatedAt:
      obj.updatedAt instanceof Date
        ? obj.updatedAt.toISOString()
        : obj.updatedAt,
    startDate:
      obj.startDate instanceof Date
        ? obj.startDate.toISOString()
        : obj.startDate,
    endDate:
      obj.endDate instanceof Date ? obj.endDate.toISOString() : obj.endDate,
    athletes: Array.isArray(obj.athletes)
      ? obj.athletes.map((a: any) =>
          typeof a === "object" && a !== null
            ? {
                id: a._id?.toString?.() || a.id?.toString?.() || "",
                name: a.name,
                profilePicture: a.profilePicture,
              }
            : a.toString(),
        )
      : [],
    deliverables,
    visibility: obj.visibility,
    interests: obj.interests,
    files: obj.files,
  };
}

export function serializeCampaignApplication(
  doc: any,
): SerializedCampaignApplication {
  const obj = doc.toObject ? doc.toObject({ virtuals: true }) : doc;

  // Handle the case where athleteId might be null or undefined
  let athleteIdData: any = null;
  if (obj.athleteId) {
    if (typeof obj.athleteId === "object") {
      athleteIdData = {
        _id: obj.athleteId._id.toString(),
        userId: obj.athleteId.userId
          ? {
              _id: obj.athleteId.userId._id.toString(),
              name: obj.athleteId.userId.name,
            }
          : null,
        profilePicture: obj.athleteId.profilePicture,
      };
    } else {
      athleteIdData = obj.athleteId.toString();
    }
  }

  const result = {
    ...obj,
    id: obj._id.toString(),
    _id: undefined,
    campaignId: obj.campaignId?._id
      ? obj.campaignId._id.toString()
      : obj.campaignId?.toString(),
    athleteId: athleteIdData,
    appliedAt:
      obj.appliedAt instanceof Date
        ? obj.appliedAt.toISOString()
        : obj.appliedAt,
    createdAt:
      obj.createdAt instanceof Date
        ? obj.createdAt.toISOString()
        : obj.createdAt,
    updatedAt:
      obj.updatedAt instanceof Date
        ? obj.updatedAt.toISOString()
        : obj.updatedAt,
  };

  // Remove __v if it exists
  delete result.__v;

  return result;
}
