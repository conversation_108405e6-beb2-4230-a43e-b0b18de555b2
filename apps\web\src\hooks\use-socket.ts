import { useEffect, useRef, useState, useCallback } from "react";
import { useSelector } from "react-redux";
import { io, Socket } from "socket.io-client";

import { RootState } from "../store";
import { getAuthState } from "../store/slices/auth";
import { useAuth } from "./use-auth";
import { TokensStorage } from "../lib/utils/tokens-storage";
import { SocketTokenManager } from "../lib/utils/socket-token-manager";
import {
  SocketConnectionStatus,
  SocketConnectionState,
  SocketHookReturn
} from "../types/socket";

export function useSocket(): SocketHookReturn {
  const { user, loading } = useAuth();
  const authState = useSelector((state: RootState) => getAuthState(state));
  const socketRef = useRef<Socket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isReconnectingRef = useRef(false);
  const tokenRefreshAttemptsRef = useRef(0);
  const maxTokenRefreshAttempts = 3;

  // Track all pending timeouts to ensure proper cleanup
  const pendingTimeoutsRef = useRef<Set<NodeJS.Timeout>>(new Set());

  const [connectionState, setConnectionState] = useState<SocketConnectionState>({
    status: SocketConnectionStatus.DISCONNECTED,
    isConnected: false,
    isReconnecting: false,
    isTokenRefreshing: false,
    reconnectAttempts: 0,
    maxReconnectAttempts: 5,
  });

  // Track connection attempts for loop detection
  const connectionAttemptsRef = useRef<Array<{ timestamp: number; reason: string }>>([]);
  const lastHealthCheckRef = useRef<number>(0);

  // Helper functions for timeout management
  const createManagedTimeout = useCallback((callback: () => void, delay: number): NodeJS.Timeout => {
    const timeoutId = setTimeout(() => {
      pendingTimeoutsRef.current.delete(timeoutId);
      callback();
    }, delay);
    pendingTimeoutsRef.current.add(timeoutId);
    console.log("⏰ Created managed timeout", { timeoutId, delay, totalPending: pendingTimeoutsRef.current.size });
    return timeoutId;
  }, []);

  const clearAllManagedTimeouts = useCallback(() => {
    const count = pendingTimeoutsRef.current.size;
    console.log("🧹 Clearing all managed timeouts", { count });
    pendingTimeoutsRef.current.forEach(timeoutId => {
      clearTimeout(timeoutId);
    });
    pendingTimeoutsRef.current.clear();

    if (count > 0) {
      console.log("🧹 Successfully cleared all timeouts", { clearedCount: count });
    }
  }, []);

  // Calculate exponential backoff delay
  const getReconnectionDelay = useCallback((attemptCount: number) => {
    // Base delay of 1 second, exponentially increasing up to 30 seconds
    const baseDelay = 1000;
    const maxDelay = 30000;
    const delay = Math.min(baseDelay * Math.pow(2, attemptCount), maxDelay);

    // Add some jitter to prevent thundering herd
    const jitter = Math.random() * 0.3 * delay;
    return Math.floor(delay + jitter);
  }, []);

    // Clear any pending reconnection attempts
  const clearReconnectTimeout = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
  }, []);

  // Track connection attempts to detect infinite loops
  const trackConnectionAttempt = useCallback((reason: string) => {
    const now = Date.now();
    connectionAttemptsRef.current.push({ timestamp: now, reason });

    // Keep only last 10 attempts
    if (connectionAttemptsRef.current.length > 10) {
      connectionAttemptsRef.current = connectionAttemptsRef.current.slice(-10);
    }

    // Check for potential infinite loop (more than 5 attempts in 30 seconds)
    const recentAttempts = connectionAttemptsRef.current.filter(
      attempt => now - attempt.timestamp < 30000
    );

    if (recentAttempts.length >= 5) {
      console.warn("🚨 Potential infinite loop detected!", {
        recentAttempts: recentAttempts.map(a => ({
          reason: a.reason,
          timeAgo: `${Math.round((now - a.timestamp) / 1000)}s ago`
        })),
        refreshState: SocketTokenManager.getRefreshState()
      });

      // Auto-reset if we detect a loop
      if (recentAttempts.length >= 7) {
        console.error("🛑 Infinite loop detected, forcing complete reset");

        // Clear all pending timeouts to stop any delayed reconnection attempts
        clearAllManagedTimeouts();
        clearReconnectTimeout();

        // Reset all state
        SocketTokenManager.resetRefreshState();
        tokenRefreshAttemptsRef.current = 0;
        isReconnectingRef.current = false;

        // Clear connection attempts history
        connectionAttemptsRef.current = [];

        console.log("🛑 Complete reset performed - all timeouts cleared, state reset");
        return false; // Signal to abort current attempt
      }
    }

    return true; // Signal to continue
  }, [clearAllManagedTimeouts, clearReconnectTimeout]);

  // Update connection state helper
  const updateConnectionState = useCallback((updates: Partial<SocketConnectionState>) => {
    setConnectionState(prev => ({ ...prev, ...updates }));
  }, []);



  // Disconnect socket and clean up
  const disconnectSocket = useCallback(() => {
    console.log("🔌 Disconnecting socket and clearing all timeouts");

    if (socketRef.current) {
      socketRef.current.disconnect();
      socketRef.current = null;
    }

    // Clear all timeouts to prevent delayed reconnection attempts
    clearReconnectTimeout();
    clearAllManagedTimeouts();

    isReconnectingRef.current = false;
    updateConnectionState({
      status: SocketConnectionStatus.DISCONNECTED,
      isConnected: false,
      isReconnecting: false,
      isTokenRefreshing: false,
    });
  }, [clearReconnectTimeout, clearAllManagedTimeouts, updateConnectionState]);

  // Get current tokens with fallback - returns both access and refresh tokens
  const getCurrentTokens = useCallback(() => {
    const reduxTokens = authState.tokens;
    const storageTokens = TokensStorage.getTokens();

    // Prefer storage tokens as they're more up-to-date after refresh
    if (storageTokens?.accessToken && storageTokens?.refreshToken) {
      return {
        access: storageTokens.accessToken,
        refresh: storageTokens.refreshToken
      };
    }

    // Fallback to Redux tokens
    if (reduxTokens?.access && reduxTokens?.refresh) {
      return {
        access: reduxTokens.access,
        refresh: reduxTokens.refresh
      };
    }

    return null;
  }, [authState.tokens]);

  // Handle token refresh and reconnection
  const handleTokenRefreshAndReconnect = useCallback(async (expiredToken?: string): Promise<{ success: boolean; newToken?: string }> => {
    if (connectionState.isTokenRefreshing) {
      console.log("🔄 Token refresh already in progress, skipping");
      return { success: false };
    }

    console.log("🔑 Starting token refresh process", {
      expiredToken: expiredToken ? `${expiredToken.substring(0, 20)}...` : 'none',
      currentRefreshAttempts: tokenRefreshAttemptsRef.current,
      maxRefreshAttempts: maxTokenRefreshAttempts
    });

    // Enhanced circuit breaker: check if we've exceeded refresh attempts
    if (tokenRefreshAttemptsRef.current >= maxTokenRefreshAttempts) {
      console.error("🚫 Max token refresh attempts reached in handleTokenRefreshAndReconnect");
      updateConnectionState({
        status: SocketConnectionStatus.AUTH_ERROR,
        isTokenRefreshing: false,
        lastError: "Unable to refresh session. Please sign in again.",
      });
      return { success: false };
    }

    updateConnectionState({
      status: SocketConnectionStatus.TOKEN_REFRESHING,
      isTokenRefreshing: true,
    });

    try {
      const refreshResult = await SocketTokenManager.refreshTokens(expiredToken);

      if (refreshResult.success && refreshResult.tokens) {
        console.log("✅ Token refresh successful", {
          newToken: `${refreshResult.tokens.access.substring(0, 20)}...`,
          previousToken: expiredToken ? `${expiredToken.substring(0, 20)}...` : 'none'
        });

        // Validate that we actually got a new token
        if (expiredToken && refreshResult.tokens.access === expiredToken) {
          console.error("⚠️ Received same token from refresh, this may cause infinite loop");
          updateConnectionState({
            status: SocketConnectionStatus.AUTH_ERROR,
            isTokenRefreshing: false,
            lastError: "Session refresh failed. Please sign in again.",
          });
          return { success: false };
        }

        // Reset the token refreshing state
        updateConnectionState({
          isTokenRefreshing: false,
        });

        // Add a small delay to ensure Redux state is updated
        await new Promise(resolve => setTimeout(resolve, 100));

        return {
          success: true,
          newToken: refreshResult.tokens.access
        };
      } else {
        console.error("❌ Token refresh failed:", refreshResult.error);
        updateConnectionState({
          status: SocketConnectionStatus.AUTH_ERROR,
          isTokenRefreshing: false,
          lastError: "Session refresh failed. Please sign in again.",
        });
        return { success: false };
      }
    } catch (error) {
      console.error("💥 Token refresh error:", error);
      updateConnectionState({
        status: SocketConnectionStatus.AUTH_ERROR,
        isTokenRefreshing: false,
        lastError: "Connection error. Please check your network and try again.",
      });
      return { success: false };
    }
  }, [connectionState.isTokenRefreshing, updateConnectionState]);

  // Create socket connection with proper error handling
  const createSocketConnection = useCallback((accessToken: string) => {
    if (socketRef.current) {
      socketRef.current.disconnect();
    }

    updateConnectionState({
      status: SocketConnectionStatus.CONNECTING,
      isConnected: false,
    });

    const socket = io(process.env.NEXT_PUBLIC_SERVER_URL!, {
      auth: { token: accessToken },
      transports: ["websocket"],
      autoConnect: true,
      reconnection: false, // We'll handle reconnection manually
      timeout: 20000,
    });

    return socket;
  }, [updateConnectionState]);

  // Enhanced socket connection with token refresh
  const connectSocket = useCallback(async (specificToken?: string) => {
    if (isReconnectingRef.current) {
      console.log("🔄 Connection already in progress, skipping");
      return;
    }

    // Track this connection attempt and check for loops
    const reason = specificToken ? 'token-refresh-retry' : 'normal-connection';
    if (!trackConnectionAttempt(reason)) {
      console.error("🛑 Connection attempt aborted due to infinite loop detection");
      return;
    }

    const accessToken = specificToken || getCurrentTokens()?.access;
    console.log("🔌 Attempting socket connection", {
      usingSpecificToken: !!specificToken,
      tokenPreview: accessToken ? `${accessToken.substring(0, 20)}...` : 'none',
      currentRefreshAttempts: tokenRefreshAttemptsRef.current,
      isTokenRefreshing: connectionState.isTokenRefreshing,
      reason
    });

    if (!accessToken) {
      console.warn("⚠️ Socket connection skipped - no access token available");
      updateConnectionState({
        status: SocketConnectionStatus.AUTH_ERROR,
        lastError: "No access token available",
      });
      return;
    }

    // Prevent connection attempts during token refresh unless using a specific new token
    if (connectionState.isTokenRefreshing && !specificToken) {
      console.log("🔄 Token refresh in progress, delaying connection attempt");
      createManagedTimeout(() => {
        if (!isReconnectingRef.current && !connectionState.isTokenRefreshing) {
          connectSocket();
        }
      }, 2000);
      return;
    }

    isReconnectingRef.current = true;

    // Disconnect any existing socket before creating a new one
    if (socketRef.current) {
      console.log("🔌 Disconnecting existing socket before reconnection");
      socketRef.current.disconnect();
      socketRef.current = null;
    }

    const socket = createSocketConnection(accessToken);

    // Handle successful connection
    socket.on("connect", () => {
      console.log("✅ Socket connected successfully", {
        tokenPreview: accessToken ? `${accessToken.substring(0, 20)}...` : 'none',
        refreshState: SocketTokenManager.getRefreshState(),
        refreshAttempts: tokenRefreshAttemptsRef.current
      });
      updateConnectionState({
        status: SocketConnectionStatus.CONNECTED,
        isConnected: true,
        isReconnecting: false,
        isTokenRefreshing: false,
        reconnectAttempts: 0,
        lastError: undefined,
      });
      isReconnectingRef.current = false;

      // Reset refresh attempts on successful connection
      tokenRefreshAttemptsRef.current = 0;
    });

    // Handle connection errors with token refresh
    socket.on("connect_error", async (error) => {
      console.error("🚨 Socket connection error:", error.message);
      isReconnectingRef.current = false; // Reset reconnecting flag

      // Provide user-friendly error messages
      let userFriendlyError = "Connection failed. Attempting to reconnect...";
      if (error.message.includes("Authentication error")) {
        userFriendlyError = "Session expired. Refreshing authentication...";
      } else if (error.message.includes("timeout")) {
        userFriendlyError = "Connection timeout. Retrying...";
      } else if (error.message.includes("Network")) {
        userFriendlyError = "Network error. Please check your connection.";
      }

      updateConnectionState({
        status: SocketConnectionStatus.CONNECTION_ERROR,
        isConnected: false,
        lastError: userFriendlyError,
      });

      if (SocketTokenManager.isTokenExpirationError(error)) {
        // Circuit breaker: prevent infinite token refresh attempts
        if (tokenRefreshAttemptsRef.current >= maxTokenRefreshAttempts) {
          console.error(`🚫 Max token refresh attempts (${maxTokenRefreshAttempts}) reached, stopping`);
          updateConnectionState({
            status: SocketConnectionStatus.AUTH_ERROR,
            lastError: "Unable to refresh session. Please sign in again.",
          });
          return;
        }

        tokenRefreshAttemptsRef.current++;
        console.log(`🔑 Token expired, attempting refresh (${tokenRefreshAttemptsRef.current}/${maxTokenRefreshAttempts})...`);

        // Pass the current token to the refresh function for validation
        const currentToken = accessToken;
        const refreshResult = await handleTokenRefreshAndReconnect(currentToken);

        if (refreshResult.success && refreshResult.newToken) {
          console.log("🔄 Retrying connection with new token after delay");
          // Reset attempts on successful refresh
          tokenRefreshAttemptsRef.current = 0;

          // Use exponential backoff for reconnection delay
          const delay = getReconnectionDelay(tokenRefreshAttemptsRef.current);
          console.log(`🔄 Scheduling reconnection with exponential backoff delay: ${delay}ms`);

          createManagedTimeout(() => {
            if (!isReconnectingRef.current && !connectionState.isTokenRefreshing) {
              console.log("🔄 Executing delayed reconnection with new token", {
                newToken: `${refreshResult.newToken!.substring(0, 20)}...`,
                delay: `${delay}ms`
              });
              connectSocket(refreshResult.newToken); // Use the new token explicitly
            } else {
              console.log("🔄 Skipping delayed reconnection - already connecting or refreshing");
            }
          }, delay);
        } else {
          console.error("❌ Token refresh failed, cannot reconnect");
          updateConnectionState({
            status: SocketConnectionStatus.AUTH_ERROR,
            lastError: "Session refresh failed. Please sign in again.",
          });
        }
      } else if (connectionState.reconnectAttempts < connectionState.maxReconnectAttempts) {
        // Retry connection for non-auth errors
        const delay = Math.min(1000 * Math.pow(2, connectionState.reconnectAttempts), 10000);
        console.log(`🔄 Retrying connection in ${delay}ms (attempt ${connectionState.reconnectAttempts + 1}/${connectionState.maxReconnectAttempts})`);

        updateConnectionState({
          status: SocketConnectionStatus.RECONNECTING,
          isReconnecting: true,
          reconnectAttempts: connectionState.reconnectAttempts + 1,
        });

        reconnectTimeoutRef.current = setTimeout(() => {
          connectSocket(); // Use current token for non-auth errors
        }, delay);
      } else {
        console.error("🚫 Max reconnection attempts reached");
        updateConnectionState({
          status: SocketConnectionStatus.CONNECTION_ERROR,
          lastError: "Maximum reconnection attempts exceeded",
        });
      }
    });

    // Handle disconnection
    socket.on("disconnect", async (reason) => {
      console.log("🔌 Socket disconnected:", reason);
      isReconnectingRef.current = false; // Reset reconnecting flag

      updateConnectionState({
        status: SocketConnectionStatus.DISCONNECTED,
        isConnected: false,
      });

      // Handle server-initiated disconnection (often auth-related)
      if (reason === "io server disconnect") {
        console.log("🔑 Server disconnected socket, likely due to auth error");

        // Check if we've exceeded refresh attempts before trying again
        if (tokenRefreshAttemptsRef.current >= maxTokenRefreshAttempts) {
          console.error("🚫 Max token refresh attempts reached, not retrying after server disconnect");
          updateConnectionState({
            status: SocketConnectionStatus.AUTH_ERROR,
            lastError: `Maximum token refresh attempts exceeded (${maxTokenRefreshAttempts})`,
          });
          return;
        }

        const currentToken = accessToken;
        const refreshResult = await handleTokenRefreshAndReconnect(currentToken);

        if (refreshResult.success && refreshResult.newToken) {
          const delay = getReconnectionDelay(connectionState.reconnectAttempts);
          console.log(`🔄 Retrying connection after server disconnect with exponential backoff: ${delay}ms`);

          createManagedTimeout(() => {
            if (!isReconnectingRef.current && !connectionState.isTokenRefreshing) {
              console.log("🔄 Executing delayed reconnection after server disconnect", {
                delay: `${delay}ms`
              });
              connectSocket(refreshResult.newToken); // Use the new token explicitly
            } else {
              console.log("🔄 Skipping reconnection after server disconnect - already connecting or refreshing");
            }
          }, delay);
        }
      }
    });

    socketRef.current = socket;
  }, [
    getCurrentTokens,
    createSocketConnection,
    updateConnectionState,
    handleTokenRefreshAndReconnect,
    connectionState.reconnectAttempts,
    connectionState.maxReconnectAttempts,
    connectionState.isTokenRefreshing,
    trackConnectionAttempt,
    getReconnectionDelay,
    createManagedTimeout
  ]);

  useEffect(() => {
    // Wait for auth to finish loading
    if (loading) {
      return;
    }

    // Clean up if user is not authenticated
    if (!user) {
      disconnectSocket();
      return;
    }

    // Connect socket
    connectSocket();

    return () => {
      console.log("🧹 useSocket cleanup - clearing all timeouts and disconnecting");
      clearAllManagedTimeouts();
      disconnectSocket();
    };
  }, [user, loading, connectSocket, disconnectSocket, clearAllManagedTimeouts]);

  // Health check effect to monitor for stuck states
  useEffect(() => {
    const healthCheckInterval = setInterval(() => {
      const now = Date.now();

      // Skip if we just did a health check recently
      if (now - lastHealthCheckRef.current < 30000) {
        return;
      }

      lastHealthCheckRef.current = now;

      // Check if we're stuck in token refreshing state
      if (connectionState.isTokenRefreshing) {
        console.warn("🏥 Health check: Token refresh has been active for a while", {
          refreshState: SocketTokenManager.getRefreshState(),
          connectionState: {
            status: connectionState.status,
            isTokenRefreshing: connectionState.isTokenRefreshing,
            refreshAttempts: tokenRefreshAttemptsRef.current
          }
        });

        // If stuck for too long, reset
        if (tokenRefreshAttemptsRef.current >= maxTokenRefreshAttempts) {
          console.error("🏥 Health check: Resetting stuck token refresh state and clearing timeouts");

          // Clear all timeouts to stop any pending operations
          clearAllManagedTimeouts();
          clearReconnectTimeout();

          SocketTokenManager.resetRefreshState();
          tokenRefreshAttemptsRef.current = 0;
          isReconnectingRef.current = false;

          // Clear connection attempts history
          connectionAttemptsRef.current = [];

          updateConnectionState({
            isTokenRefreshing: false,
            status: SocketConnectionStatus.DISCONNECTED,
          });

          console.log("🏥 Health check: Complete reset performed");
        }
      }
    }, 30000); // Check every 30 seconds

    return () => clearInterval(healthCheckInterval);
  }, [connectionState.isTokenRefreshing, updateConnectionState, clearAllManagedTimeouts, clearReconnectTimeout, connectionState.status]);

  // Timeout monitoring effect - periodically check for orphaned timeouts
  useEffect(() => {
    const timeoutMonitorInterval = setInterval(() => {
      const timeoutCount = pendingTimeoutsRef.current.size;

      if (timeoutCount > 0) {
        console.log("⏰ Timeout monitor: Active timeouts", {
          count: timeoutCount,
          connectionStatus: connectionState.status,
          isReconnecting: isReconnectingRef.current,
          isTokenRefreshing: connectionState.isTokenRefreshing
        });

        // If we have many timeouts and we're not actively connecting, something might be wrong
        if (timeoutCount > 5 && !isReconnectingRef.current && !connectionState.isTokenRefreshing) {
          console.warn("⚠️ Detected potential timeout leak, clearing all timeouts");
          clearAllManagedTimeouts();
        }
      }
    }, 15000); // Check every 15 seconds

    return () => clearInterval(timeoutMonitorInterval);
  }, [connectionState.status, connectionState.isTokenRefreshing, clearAllManagedTimeouts]);

  // Socket action functions with connection checks
  const joinChat = useCallback((chatId: string) => {
    if (!socketRef.current || !connectionState.isConnected) {
      console.warn("Cannot join chat - socket not connected");
      return;
    }
    socketRef.current.emit("joinChat", chatId);
  }, [connectionState.isConnected]);

  const leaveChat = useCallback((chatId: string) => {
    if (!socketRef.current || !connectionState.isConnected) {
      console.warn("Cannot leave chat - socket not connected");
      return;
    }
    socketRef.current.emit("leaveChat", chatId);
  }, [connectionState.isConnected]);

  const sendMessage = useCallback((
    chatId: string,
    content: string,
    type: string = "TEXT",
  ) => {
    if (!socketRef.current || !connectionState.isConnected) {
      console.warn("Cannot send message - socket not connected");
      return;
    }
    socketRef.current.emit("sendMessage", { chatId, content, type });
  }, [connectionState.isConnected]);

  const sendTypingStatus = useCallback((chatId: string, isTyping: boolean) => {
    if (!socketRef.current || !connectionState.isConnected) {
      return;
    }
    socketRef.current.emit("typing", { chatId, isTyping });
  }, [connectionState.isConnected]);

  const markAsRead = useCallback((chatId: string) => {
    if (!socketRef.current || !connectionState.isConnected) {
      console.warn("Cannot mark as read - socket not connected");
      return;
    }
    socketRef.current.emit("markAsRead", { chatId });
  }, [connectionState.isConnected]);

  // Force reconnection function
  const forceReconnect = useCallback(() => {
    console.log("🔄 Force reconnecting socket...");

    // Log current state for debugging
    console.log("🔍 Current refresh state:", SocketTokenManager.getRefreshState());
    console.log("🔍 Pending timeouts before cleanup:", pendingTimeoutsRef.current.size);

    // Clear all timeouts first to prevent any delayed attempts
    clearAllManagedTimeouts();

    disconnectSocket();

    // Reset all circuit breakers and counters
    tokenRefreshAttemptsRef.current = 0;
    SocketTokenManager.resetRefreshState();

    // Clear connection attempts history
    connectionAttemptsRef.current = [];

    updateConnectionState({
      reconnectAttempts: 0,
      lastError: undefined,
    });

    createManagedTimeout(() => {
      connectSocket();
    }, 1000);
  }, [disconnectSocket, updateConnectionState, connectSocket, clearAllManagedTimeouts, createManagedTimeout]);

  // Clear error function
  const clearError = useCallback(() => {
    updateConnectionState({
      lastError: undefined,
    });
  }, [updateConnectionState]);

  return {
    socket: socketRef.current,
    connectionState,
    joinChat,
    leaveChat,
    sendMessage,
    sendTypingStatus,
    markAsRead,
    forceReconnect,
    clearError,
  };
}
