import { isValidObjectId } from "mongoose";
import * as yup from "yup";
import { ChatType, MessageType } from "../types/chat";

// TypeScript interface for sendMessage input
export interface SendMessageInput {
  chatId: string;
  content: string;
  type: MessageType;
  campaignId?: string;
  contractId?: string;
}

export const chatIdSchema = yup.object({
  chatId: yup
    .string()
    .test({
      name: "valid-chat-id",
      message: "Invalid chat ID",
      test: (value) => isValidObjectId(value),
    })
    .required("Chat ID is missing"),
});

export const getMessagesSchema = yup.object({
  chatId: yup
    .string()
    .test({
      name: "valid-chat-id",
      message: "Invalid chat ID",
      test: (value) => isValidObjectId(value),
    })
    .required("Chat ID is missing"),
  limit: yup
    .number()
    .min(1, "Limit must be at least 1")
    .max(50, "Limit cannot exceed 50")
    .default(50),
  before: yup.string().optional(),
});

export const createChatSchema = yup.object({
  participantIds: yup
    .array()
    .of(
      yup
        .string()
        .test({
          name: "valid-user-id",
          message: "Invalid user ID",
          test: (value) => isValidObjectId(value),
        })
        .required("User ID is missing"),
    )
    .required("Participant IDs are missing"),
  type: yup
    .string()
    .oneOf(Object.values(ChatType), "Invalid chat type")
    .required("Chat type is missing"),
  campaignId: yup
    .string()
    .test({
      name: "valid-campaign-id",
      message: "Invalid campaign ID",
      test: (value) => !value || isValidObjectId(value),
    })
    .optional(),
});

export const sendMessageSchema = yup.object({
  chatId: yup
    .string()
    .test({
      name: "valid-chat-id",
      message: "Invalid chat ID",
      test: (value) => isValidObjectId(value),
    })
    .required("Chat ID is missing"),
  content: yup
    .string()
    .min(1, "Message content cannot be empty")
    .required("Message content is missing"),
  type: yup
    .string()
    .oneOf(Object.values(MessageType), "Invalid message type")
    .default(MessageType.TEXT),
  campaignId: yup
    .string()
    .test({
      name: "valid-campaign-id",
      message: "Invalid campaign ID",
      test: (value) => !value || isValidObjectId(value),
    })
    .optional(),
  contractId: yup
    .string()
    .test({
      name: "valid-contract-id",
      message: "Invalid contract ID",
      test: (value) => !value || isValidObjectId(value),
    })
    .optional(),
});
