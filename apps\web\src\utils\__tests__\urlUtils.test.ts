/**
 * Tests for URL utility functions
 */

import { validateUrl, createStripeConnectUrls } from '../urlUtils';

// Mock environment variables
const originalEnv = process.env;

beforeEach(() => {
  jest.resetModules();
  process.env = { ...originalEnv };
});

afterAll(() => {
  process.env = originalEnv;
});

describe('validateUrl', () => {
  it('should validate valid HTTP URLs', () => {
    const result = validateUrl('http://localhost:3010/test');
    expect(result.isValid).toBe(true);
    expect(result.details).toMatchObject({
      protocol: 'http:',
      hostname: 'localhost',
      port: '3010',
      pathname: '/test'
    });
  });

  it('should validate valid HTTPS URLs', () => {
    const result = validateUrl('https://example.com/path?query=value');
    expect(result.isValid).toBe(true);
    expect(result.details).toMatchObject({
      protocol: 'https:',
      hostname: 'example.com',
      pathname: '/path',
      search: '?query=value'
    });
  });

  it('should reject invalid protocols', () => {
    const result = validateUrl('ftp://example.com');
    expect(result.isValid).toBe(false);
    expect(result.error).toContain('Invalid protocol');
  });

  it('should reject malformed URLs', () => {
    const result = validateUrl('not-a-url');
    expect(result.isValid).toBe(false);
    expect(result.error).toBeDefined();
  });

  it('should reject URLs without hostname', () => {
    const result = validateUrl('http://');
    expect(result.isValid).toBe(false);
    expect(result.error).toContain('hostname');
  });
});

describe('createStripeConnectUrls', () => {
  it('should create valid URLs with default path', () => {
    process.env.NEXT_PUBLIC_APP_URL = 'http://localhost:3010';
    
    const result = createStripeConnectUrls();
    
    expect(result.returnUrl).toBe('http://localhost:3010/app/athlete/wallet?setup=complete');
    expect(result.refreshUrl).toBe('http://localhost:3010/app/athlete/wallet?setup=refresh');
    expect(result.baseUrl).toBe('http://localhost:3010');
    expect(result.validation.returnUrl).toBeDefined();
    expect(result.validation.refreshUrl).toBeDefined();
  });

  it('should create valid URLs with custom path', () => {
    process.env.NEXT_PUBLIC_APP_URL = 'https://example.com';
    
    const result = createStripeConnectUrls('/custom/path');
    
    expect(result.returnUrl).toBe('https://example.com/custom/path?setup=complete');
    expect(result.refreshUrl).toBe('https://example.com/custom/path?setup=refresh');
  });

  it('should handle base URLs with trailing slash', () => {
    process.env.NEXT_PUBLIC_APP_URL = 'http://localhost:3010/';
    
    const result = createStripeConnectUrls();
    
    expect(result.returnUrl).toBe('http://localhost:3010/app/athlete/wallet?setup=complete');
    expect(result.refreshUrl).toBe('http://localhost:3010/app/athlete/wallet?setup=refresh');
    expect(result.baseUrl).toBe('http://localhost:3010');
  });

  it('should throw error for invalid base URL', () => {
    process.env.NEXT_PUBLIC_APP_URL = 'invalid-url';
    
    expect(() => createStripeConnectUrls()).toThrow();
  });

  it('should use fallback URL when no environment variable is set', () => {
    delete process.env.NEXT_PUBLIC_APP_URL;
    
    const result = createStripeConnectUrls();
    
    expect(result.baseUrl).toBe('http://localhost:3010');
    expect(result.returnUrl).toContain('http://localhost:3010');
  });
});
