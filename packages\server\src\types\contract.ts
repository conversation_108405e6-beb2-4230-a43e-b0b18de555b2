import { Schema } from "mongoose";

export enum ContractStatus {
  DRAFT = "DRAFT",
  PENDING_BRAND_REVIEW = "PENDING_BRAND_REVIEW",
  PENDING_BRAND_APPROVAL = "PENDING_BRAND_APPROVAL",
  PENDING_ATHLETE_SIGNATURE = "PENDING_ATHLETE_SIGNATURE",
  ATHLETE_SIGNED = "ATHLETE_SIGNED",
  PENDING_BRAND_SIGNATURE = "PENDING_BRAND_SIGNATURE",
  BRAND_SIGNED = "BRAND_SIGNED",
  PENDING_PAYMENT = "PENDING_PAYMENT", // After brand signs but before payment
  PAID = "PAID", // After successful payment
  AWAITING_DELIVERABLES = "AWAITING_DELIVERABLES", // After payment completed, athlete should start working on deliverables
  AWAITING_BRAND_APPROVAL = "AWAITING_BRAND_APPROVAL", // After athlete submits all deliverables, brand needs to review and approve them
  FULFILLED = "FULFILLED", // After brand approves all deliverables and marks contract as fulfilled
  CANCELLED = "CANCELLED",
  EXPIRED = "EXPIRED",
}

export enum ContractType {
  CAMPAIGN_AGREEMENT = "CAMPAIGN_AGREEMENT",
  DELIVERABLE_SPECIFIC = "DELIVERABLE_SPECIFIC",
}

export enum PaymentStatus {
  PENDING = "PENDING", // Payment intent created but not processed
  PROCESSING = "PROCESSING", // Payment is being processed
  SUCCEEDED = "SUCCEEDED", // Payment completed successfully
  FAILED = "FAILED", // Payment failed
  CANCELLED = "CANCELLED", // Payment was cancelled
  REQUIRES_ACTION = "REQUIRES_ACTION", // Payment requires additional action (3D Secure, etc.)
}

export interface ContractTerms {
  totalCompensation: number;
  paymentSchedule: PaymentSchedule[];
  deliverables: ContractDeliverable[];
  campaignDuration: {
    startDate: Date;
    endDate: Date;
  };
  additionalTerms?: string[];
  cancellationPolicy?: string;
  intellectualPropertyRights?: string;
  confidentialityClause?: string;
}

export interface PaymentSchedule {
  description: string;
  amount: number;
  dueDate: Date;
  milestone?: string; // e.g., "Upon deliverable completion", "Campaign start"
}

export interface ContractDeliverable {
  deliverableId: string;
  name: string;
  description: string;
  dueDate: Date;
  compensation: number;
  requirements: string[];
  type: string;
}

export interface ContractParticipant {
  userId: Schema.Types.ObjectId;
  userType: "brand" | "athlete";
  signedAt?: Date;
  signatureData?: {
    ipAddress: string;
    userAgent: string;
    timestamp: Date;
  };
}

export interface Contract {
  campaignId: Schema.Types.ObjectId;
  applicationId: Schema.Types.ObjectId;
  brandId: Schema.Types.ObjectId;
  athleteId: Schema.Types.ObjectId;
  contractNumber: string; // Auto-generated unique identifier
  type: ContractType;
  status: ContractStatus;
  version: number;
  parentContractId?: Schema.Types.ObjectId; // For contract revisions

  // Contract content
  title: string;
  terms: ContractTerms;

  // Participants and signatures
  participants: ContractParticipant[];

  // File storage
  pdfUrl?: string; // S3 URL for generated PDF
  pdfKey?: string; // S3 key for file management

  // Workflow tracking
  brandReviewedAt?: Date;
  brandApprovedAt?: Date;
  sentToAthleteAt?: Date;
  athleteSignedAt?: Date;
  brandSignedAt?: Date;

  // Payment tracking
  paymentIntentId?: string; // Stripe payment intent ID
  paymentStatus?: PaymentStatus;
  paymentInitiatedAt?: Date;
  paymentCompletedAt?: Date;
  paymentFailedAt?: Date;
  paymentFailureReason?: string;
  paymentRetryCount?: number;

  // Fulfillment tracking
  fulfilledAt?: Date;

  // Metadata
  createdAt: Date;
  updatedAt: Date;
  expiresAt?: Date; // Contract expiration for signature

  // Audit trail
  revisionHistory?: ContractRevision[];
}

export interface ContractRevision {
  version: number;
  changedBy: Schema.Types.ObjectId;
  changedAt: Date;
  changes: string[];
  reason?: string;
}

// Participant profile interfaces for contract responses
export interface ContractBrandProfile {
  name: string;
  companyName: string;
  logo: {
    url: string | null;
    key: string | null;
    uploadedAt: string | null;
  };
}

export interface ContractAthleteProfile {
  name: string;
  profilePicture: {
    url: string | null;
    key: string | null;
    uploadedAt: string | null;
  };
}

// Serialized interfaces for client-side use
export interface SerializedContract
  extends Omit<
    Contract,
    | "campaignId"
    | "applicationId"
    | "brandId"
    | "athleteId"
    | "parentContractId"
    | "participants"
    | "createdAt"
    | "updatedAt"
    | "expiresAt"
    | "brandReviewedAt"
    | "brandApprovedAt"
    | "sentToAthleteAt"
    | "athleteSignedAt"
    | "brandSignedAt"
    | "paymentInitiatedAt"
    | "paymentCompletedAt"
    | "paymentFailedAt"
    | "fulfilledAt"
    | "revisionHistory"
    | "terms"
  > {
  id: string;
  campaignId: string;
  applicationId: string;
  brandId: string;
  athleteId: string;
  parentContractId?: string;
  participants: SerializedContractParticipant[];
  terms: SerializedContractTerms;
  createdAt: string;
  updatedAt: string;
  expiresAt?: string;
  brandReviewedAt?: string;
  brandApprovedAt?: string;
  sentToAthleteAt?: string;
  athleteSignedAt?: string;
  brandSignedAt?: string;
  paymentInitiatedAt?: string;
  paymentCompletedAt?: string;
  paymentFailedAt?: string;
  fulfilledAt?: string;
  revisionHistory?: SerializedContractRevision[];
  participantProfiles?: {
    brand?: ContractBrandProfile;
    athlete?: ContractAthleteProfile;
  };
}

export interface SerializedContractParticipant
  extends Omit<ContractParticipant, "userId" | "signedAt" | "signatureData"> {
  userId: string;
  signedAt?: string;
  signatureData?: {
    ipAddress: string;
    userAgent: string;
    timestamp: string;
  };
}

export interface SerializedContractTerms
  extends Omit<ContractTerms, "paymentSchedule" | "campaignDuration" | "deliverables"> {
  paymentSchedule: SerializedPaymentSchedule[];
  deliverables: SerializedContractDeliverable[];
  campaignDuration: {
    startDate: string;
    endDate: string;
  };
}

export interface SerializedPaymentSchedule
  extends Omit<PaymentSchedule, "dueDate"> {
  dueDate: string;
}

export interface SerializedContractDeliverable
  extends Omit<ContractDeliverable, "dueDate"> {
  dueDate: string;
}

export interface SerializedContractRevision
  extends Omit<ContractRevision, "changedBy" | "changedAt"> {
  changedBy: string;
  changedAt: string;
}

// Contract generation input interface
export interface ContractGenerationInput {
  campaignId: string;
  applicationId: string;
  customTerms?: {
    additionalTerms?: (string | undefined)[];
    cancellationPolicy?: string;
    intellectualPropertyRights?: string;
    confidentialityClause?: string;
  };
  paymentSchedule?: Omit<PaymentSchedule, "dueDate">[];
  expirationDays?: number; // Days until contract expires for signature
}

export interface ContractUpdateInput {
  contractId: string;
  title?: string;
  terms?: {
    totalCompensation?: number;
    paymentSchedule?: PaymentSchedule[];
    deliverables?: ContractDeliverable[];
    campaignDuration?: {
      startDate?: Date;
      endDate?: Date;
    } | null;
    additionalTerms?: string[];
    cancellationPolicy?: string;
    intellectualPropertyRights?: string;
    confidentialityClause?: string;
  };
  reason?: string;
}
