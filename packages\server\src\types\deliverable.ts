export enum DeliverableType {
  PHOTO_VIDEO_SHOOT = "photo_or_video_shoot",
  IN_PERSON = "in_person",
  CONTENT_CREATION = "content_creation",
  CONTENT_SHARE = "content_share",
  GIFTED_COLLABORATION = "gifted_collaboration",
  CUSTOM = "custom",
}

export const getDeliverableTypeLabel = (type: DeliverableType) => {
  switch (type) {
    case DeliverableType.GIFTED_COLLABORATION:
      return "Gifted Collaboration";
    case DeliverableType.PHOTO_VIDEO_SHOOT:
      return "Photo or Video Shoot";
    case DeliverableType.IN_PERSON:
      return "In-Person";
    case DeliverableType.CONTENT_CREATION:
      return "Content Creation";
    case DeliverableType.CONTENT_SHARE:
      return "Content Share";
    case DeliverableType.CUSTOM:
      return "Custom";
    default:
      return "Unknown";
  }
};

// Base interface for all deliverables
export interface BaseDeliverable {
  id: string;
  campaignId: string;
  name: string;
  daysToComplete: number;
  minimumPayment: number;
  description: string;
  type: DeliverableType;
  createdAt: string;
  updatedAt: string;
}

// Type-specific interfaces
export interface PhotoVideoShootDeliverable extends BaseDeliverable {
  type: DeliverableType.PHOTO_VIDEO_SHOOT;
  location?: string;
  date?: string;
  time?: string; 
}

export interface InPersonDeliverable extends BaseDeliverable {
  type: DeliverableType.IN_PERSON;
  location: string;
  date?: string;
  time?: string;
}

export interface ContentCreationDeliverable extends BaseDeliverable {
  type: DeliverableType.CONTENT_CREATION;
  content: string[];
}

export interface ContentShareDeliverable extends BaseDeliverable {
  type: DeliverableType.CONTENT_SHARE;
  content: string[];
}

export interface GiftedCollaborationDeliverable extends BaseDeliverable {
  type: DeliverableType.GIFTED_COLLABORATION;
  productName?: string;
  productPrice?: number;
}

export interface CustomDeliverable extends BaseDeliverable {
  type: DeliverableType.CUSTOM;
}

// Union type for all deliverable types
export type Deliverable =
  | PhotoVideoShootDeliverable
  | InPersonDeliverable
  | ContentCreationDeliverable
  | ContentShareDeliverable
  | GiftedCollaborationDeliverable
  | CustomDeliverable;
