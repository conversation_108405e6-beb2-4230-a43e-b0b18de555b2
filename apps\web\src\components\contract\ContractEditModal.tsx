"use client";

import { useState, useEffect } from "react";
import { trpc } from "@/lib/trpc/client";

import { SerializedContract, ContractUpdateInput } from "@repo/server/src/types/contract";
import { isContractEditingAllowed, getContractEditingDisabledMessage } from "@repo/server/src/utils/contractPdf";

import { Button } from "../ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "../ui/dialog";
import { Textarea } from "../ui/textarea";
import { Label } from "../ui/label";
import { useToast } from "../ui/toast/use-toast";

interface ContractEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  contract: SerializedContract;
  onSuccess?: () => void;
}

export function ContractEditModal({
  isOpen,
  onClose,
  contract,
  onSuccess,
}: ContractEditModalProps) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    additionalTerms: "",
    cancellationPolicy: "",
    intellectualPropertyRights: "",
    confidentialityClause: "",
  });

  const updateContractMutation = trpc.contract.update.useMutation();

  // Initialize form data when contract changes
  useEffect(() => {
    if (contract) {
      setFormData({
        additionalTerms: contract.terms.additionalTerms?.join('\n') || "",
        cancellationPolicy: contract.terms.cancellationPolicy || "",
        intellectualPropertyRights: contract.terms.intellectualPropertyRights || "",
        confidentialityClause: contract.terms.confidentialityClause || "",
      });
    }
  }, [contract]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const updateInput: ContractUpdateInput = {
        contractId: contract.id,
        terms: {
          additionalTerms: formData.additionalTerms
            ? formData.additionalTerms.split('\n').map(term => term.trim()).filter(term => term.length > 0)
            : [],
          cancellationPolicy: formData.cancellationPolicy || undefined,
          intellectualPropertyRights: formData.intellectualPropertyRights || undefined,
          confidentialityClause: formData.confidentialityClause || undefined,
        },
        reason: "Contract terms updated via edit modal",
      };

      console.log("Sending contract update:", JSON.stringify(updateInput, null, 2));

      await updateContractMutation.mutateAsync(updateInput);

      toast({
        title: "Contract Updated",
        description: `Contract ${contract.contractNumber} has been updated successfully.`,
        variant: "success",
      });

      onSuccess?.();
      onClose();
    } catch (error) {
      console.error("Failed to update contract:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to update contract. Please try again.";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: keyof typeof formData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="tw-max-w-2xl tw-max-h-[90vh] tw-overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Contract</DialogTitle>
          <DialogDescription>
            Update the terms and conditions for contract <strong>{contract.contractNumber}</strong>.
          </DialogDescription>
        </DialogHeader>

        {!isContractEditingAllowed(contract.status) ? (
          <div className="tw-space-y-4">
            <div className="tw-bg-yellow-50 tw-border tw-border-yellow-200 tw-rounded-lg tw-p-4">
              <div className="tw-flex">
                <div className="tw-flex-shrink-0">
                  <svg className="tw-h-5 tw-w-5 tw-text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="tw-ml-3">
                  <h3 className="tw-text-sm tw-font-medium tw-text-yellow-800">
                    Contract Editing Not Available
                  </h3>
                  <div className="tw-mt-2 tw-text-sm tw-text-yellow-700">
                    <p>{getContractEditingDisabledMessage(contract.status)}</p>
                  </div>
                </div>
              </div>
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                className="tw-text-aims-text-primary"
              >
                Close
              </Button>
            </DialogFooter>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="tw-space-y-6">
          {/* Additional Terms */}
          <div className="tw-space-y-1">
            <Label htmlFor="additionalTerms">Additional Terms (Optional)</Label>
            <Textarea
              id="additionalTerms"
              value={formData.additionalTerms}
              onChange={(e) => handleInputChange("additionalTerms", e.target.value)}
              placeholder="Enter additional terms, one per line..."
              rows={4}
              className="!tw-bg-aims-dark-2"
            />
            <p className="tw-text-sm tw-text-aims-dark-6">
              Add any specific terms or requirements for this contract. Each line will be a separate term.
            </p>
          </div>

          {/* Cancellation Policy */}
          <div className="tw-space-y-1">
            <Label htmlFor="cancellationPolicy">Cancellation Policy (Optional)</Label>
            <Textarea
              id="cancellationPolicy"
              value={formData.cancellationPolicy}
              onChange={(e) => handleInputChange("cancellationPolicy", e.target.value)}
              placeholder="Describe the cancellation policy..."
              rows={3}
              className="!tw-bg-aims-dark-2"
            />
            <p className="tw-text-sm tw-text-aims-dark-6">
              Specify the terms under which either party can cancel the contract.
            </p>
          </div>

          {/* Intellectual Property Rights */}
          <div className="tw-space-y-1">
            <Label htmlFor="intellectualPropertyRights">Intellectual Property Rights (Optional)</Label>
            <Textarea
              id="intellectualPropertyRights"
              value={formData.intellectualPropertyRights}
              onChange={(e) => handleInputChange("intellectualPropertyRights", e.target.value)}
              placeholder="Describe intellectual property rights and usage..."
              rows={3}
              className="!tw-bg-aims-dark-2"
            />
            <p className="tw-text-sm tw-text-aims-dark-6">
              Define who owns the content created and how it can be used.
            </p>
          </div>

          {/* Confidentiality Clause */}
          <div className="tw-space-y-1">
            <Label htmlFor="confidentialityClause">Confidentiality Clause (Optional)</Label>
            <Textarea
              id="confidentialityClause"
              value={formData.confidentialityClause}
              onChange={(e) => handleInputChange("confidentialityClause", e.target.value)}
              placeholder="Describe confidentiality requirements..."
              rows={3}
              className="!tw-bg-aims-dark-2"
            />
            <p className="tw-text-sm tw-text-aims-dark-6">
              Specify any confidentiality or non-disclosure requirements.
            </p>
          </div>

          <DialogFooter className="tw-space-x-3">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
              className="tw-text-aims-text-primary"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
            >
              {isSubmitting ? "Updating..." : "Update Contract"}
            </Button>
          </DialogFooter>
        </form>
        )}
      </DialogContent>
    </Dialog>
  );
}
