"use client";

import { use<PERSON><PERSON>back, useEffect, useState } from "react";
import {
  selectCurrentStep,
  selectSocialMedia,
  updateSocialMedia,
} from "@/store/slices/onboarding";
import { useDispatch, useSelector } from "react-redux";

const validateSocialMediaInput = (
  platform: string,
  input: string,
): string | null => {
  if (!input) return null;

  // Check if it's a URL
  if (input.startsWith("http://") || input.startsWith("https://")) {
    try {
      const urlObj = new URL(input);

      switch (platform) {
        case "instagram":
          if (!urlObj.hostname.includes("instagram.com")) {
            return "Please enter a valid Instagram URL";
          }
          break;
        case "twitter":
          if (
            !urlObj.hostname.includes("twitter.com") &&
            !urlObj.hostname.includes("x.com")
          ) {
            return "Please enter a valid Twitter/X URL";
          }
          break;
        case "tiktok":
          if (!urlObj.hostname.includes("tiktok.com")) {
            return "Please enter a valid TikTok URL";
          }
          break;
        case "youtube":
          if (!urlObj.hostname.includes("youtube.com")) {
            return "Please enter a valid YouTube URL";
          }
          break;
      }
      return null;
    } catch {
      return "Please enter a valid URL";
    }
  }

  // Check if it's a username format
  if (input.startsWith("@")) {
    const username = input.slice(1);
    if (username.length === 0) {
      return "Please enter a username after @";
    }
    // Basic username validation (alphanumeric, underscores, dots)
    if (!/^[a-zA-Z0-9._]+$/.test(username)) {
      return "Username can only contain letters, numbers, underscores, and dots";
    }
    return null;
  }

  // For non-@ usernames, check if it looks like a valid username
  if (!/^[a-zA-Z0-9._]+$/.test(input)) {
    return "Please enter a valid username or URL";
  }

  return null;
};

export default function BrandSocial() {
  const dispatch = useDispatch();
  const currentStep = useSelector(selectCurrentStep);
  const savedSocial = useSelector(selectSocialMedia);
  const [socialLinks, setSocialLinks] = useState(savedSocial);
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    dispatch(updateSocialMedia(socialLinks));
  }, [dispatch, socialLinks]);

  const handleInputChange = useCallback(
    (platform: keyof typeof socialLinks) =>
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;

      // Always update the social media state to allow typing
      setSocialLinks((prev) => ({
        ...prev,
        [platform]: value,
      }));

      // Only show validation errors if the field is not empty
      if (value) {
        const error = validateSocialMediaInput(platform, value);
        setErrors((prev) => ({
          ...prev,
          [platform]: error || "",
        }));
      } else {
        // Clear error if field is empty
        setErrors((prev) => ({
          ...prev,
          [platform]: "",
        }));
      }
    },
    [setSocialLinks, setErrors],
  );

  if (currentStep !== 3) {
    return null;
  }

  return (
    <div className="tw-space-y-6 sm:tw-space-y-8 tw-text-aims-text-primary">
      <div className="tw-text-center tw-px-4 sm:tw-px-0">
        <h1 className="tw-text-xl sm:tw-text-2xl tw-font-semibold tw-mb-2">
          Connect your brand&apos;s social media
        </h1>
        <p className="tw-text-sm sm:tw-text-base tw-text-gray-400">
          Add your social media profiles to showcase your online presence
        </p>
      </div>
      <div className="tw-space-y-4 sm:tw-space-y-6">
        <div>
          <label
            htmlFor="instagram"
            className="tw-block tw-text-sm sm:tw-text-base tw-font-medium tw-mb-2"
          >
            Instagram
          </label>
          <input
            type="text"
            id="instagram"
            value={socialLinks.instagram}
            onChange={handleInputChange("instagram")}
            placeholder="@username or https://instagram.com/username"
            className={`tw-w-full tw-bg-aims-dark-3 tw-border tw-rounded-lg tw-p-3 sm:tw-p-4 tw-h-12 sm:tw-h-auto tw-text-base sm:tw-text-sm tw-text-aims-text-primary placeholder:tw-text-gray-400 focus:tw-outline-none focus:tw-ring-2 tw-touch-manipulation ${
              errors.instagram
                ? "tw-border-red-500 focus:tw-ring-red-500"
                : "tw-border-gray-600 focus:tw-ring-aims-primary"
            }`}
          />
          {errors.instagram && (
            <p className="tw-mt-1 tw-text-sm tw-text-red-500">
              {errors.instagram}
            </p>
          )}
        </div>
        <div>
          <label
            htmlFor="tiktok"
            className="tw-block tw-text-sm sm:tw-text-base tw-font-medium tw-mb-2"
          >
            TikTok
          </label>
          <input
            type="text"
            id="tiktok"
            value={socialLinks.tiktok}
            onChange={handleInputChange("tiktok")}
            placeholder="@username or https://tiktok.com/@username"
            className={`tw-w-full tw-bg-aims-dark-3 tw-border tw-rounded-lg tw-p-3 sm:tw-p-4 tw-h-12 sm:tw-h-auto tw-text-base sm:tw-text-sm tw-text-aims-text-primary placeholder:tw-text-gray-400 focus:tw-outline-none focus:tw-ring-2 tw-touch-manipulation ${
              errors.tiktok
                ? "tw-border-red-500 focus:tw-ring-red-500"
                : "tw-border-gray-600 focus:tw-ring-aims-primary"
            }`}
          />
          {errors.tiktok && (
            <p className="tw-mt-1 tw-text-sm tw-text-red-500">
              {errors.tiktok}
            </p>
          )}
        </div>
        <div>
          <label
            htmlFor="youtube"
            className="tw-block tw-text-sm sm:tw-text-base tw-font-medium tw-mb-2"
          >
            YouTube
          </label>
          <input
            type="text"
            id="youtube"
            value={socialLinks.youtube}
            onChange={handleInputChange("youtube")}
            placeholder="Channel name or https://youtube.com/@channel"
            className={`tw-w-full tw-bg-aims-dark-3 tw-border tw-rounded-lg tw-p-3 sm:tw-p-4 tw-h-12 sm:tw-h-auto tw-text-base sm:tw-text-sm tw-text-aims-text-primary placeholder:tw-text-gray-400 focus:tw-outline-none focus:tw-ring-2 tw-touch-manipulation ${
              errors.youtube
                ? "tw-border-red-500 focus:tw-ring-red-500"
                : "tw-border-gray-600 focus:tw-ring-aims-primary"
            }`}
          />
          {errors.youtube && (
            <p className="tw-mt-1 tw-text-sm tw-text-red-500">
              {errors.youtube}
            </p>
          )}
        </div>
        <div>
          <label
            htmlFor="twitter"
            className="tw-block tw-text-sm sm:tw-text-base tw-font-medium tw-mb-2"
          >
            Twitter/X
          </label>
          <input
            type="text"
            id="twitter"
            value={socialLinks.twitter}
            onChange={handleInputChange("twitter")}
            placeholder="@username or https://x.com/username"
            className={`tw-w-full tw-bg-aims-dark-3 tw-border tw-rounded-lg tw-p-3 sm:tw-p-4 tw-h-12 sm:tw-h-auto tw-text-base sm:tw-text-sm tw-text-aims-text-primary placeholder:tw-text-gray-400 focus:tw-outline-none focus:tw-ring-2 tw-touch-manipulation ${
              errors.twitter
                ? "tw-border-red-500 focus:tw-ring-red-500"
                : "tw-border-gray-600 focus:tw-ring-aims-primary"
            }`}
          />
          {errors.twitter && (
            <p className="tw-mt-1 tw-text-sm tw-text-red-500">
              {errors.twitter}
            </p>
          )}
        </div>
      </div>
    </div>
  );
}
