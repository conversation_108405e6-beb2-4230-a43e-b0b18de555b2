"use client";

import { useState } from "react";
import Image from "next/image";
import { useParams } from "next/navigation";
import { FullPageLoadingSpinner } from "@/components/ui/LoadingSpinner";
import { trpc } from "@/lib/trpc/client";
import { useAuth } from "@/hooks/use-auth";
import {
  AcademicCapIcon,
  BoltIcon,
  HomeIcon,
} from "@heroicons/react/24/outline";
import SendMessageButton from "@/components/ui/SendMessageButton";

import type { SerializedAthleteProfile } from "@repo/server/src/models/user";

interface AthleteWithUser extends Omit<SerializedAthleteProfile, "userId"> {
  userId: {
    _id: string;
    name: string;
  };
}

function MinimumPaymentCard({
  options,
}: {
  options?: { label: string; price: number | null }[];
}) {
  return (
    <div className="tw-bg-aims-dark-2 tw-p-6 tw-rounded-lg">
      <h4 className="tw-font-semibold tw-mb-2 tw-text-xl">Minimum payment</h4>
      {/* <div className="tw-text-xs tw-text-aims-dark-6 tw-mb-4">
        Here, we display the minimum payment the athlete will accept for each type of deliverable.
      </div> */}
      <div className="tw-flex tw-flex-col tw-gap-2">
        {options
          ?.filter((opt) => opt.price !== null)
          .map((opt) => (
            <div key={opt.label} className="tw-flex tw-justify-between">
              <span className="tw-text-sm tw-text-aims-dark-6">
                {opt.label}
              </span>
              <span className="tw-font-bold tw-text-2xl">${opt.price}</span>
            </div>
          ))}
      </div>
    </div>
  );
}

// SocialMediaCard component
interface SocialMediaCardProps {
  icon: string;
  handle: string;
  followers: string;
  alt: string;
}
function SocialMediaCard({
  icon,
  handle,
  alt,
}: SocialMediaCardProps) {
  return (
    <div className="tw-bg-aims-dark-2 tw-p-4 tw-rounded-lg tw-flex tw-items-center tw-gap-4">
      <Image src={icon} alt={alt} width={64} height={64} />
      <div className="tw-min-w-0 tw-flex-1">
        <div className="tw-font-semibold tw-text-xl tw-truncate">
          <a href={handle} target="_blank" rel="noopener noreferrer">
            {handle}
          </a>
        </div>
        {/* <div className="tw-text-xs tw-text-aims-dark-6">
          {followers} followers
        </div> */}
      </div>
    </div>
  );
}

export default function AthletePage() {
  const { id } = useParams();
  const { user } = useAuth();
  const { data: athlete, isLoading: isAthleteLoading } =
    trpc.athlete.getAthleteById.useQuery({
      athleteId: id as string,
    }) as {
      data: AthleteWithUser | undefined;
      isLoading: boolean;
    };

  const [activeTab, setActiveTab] = useState<"details" | "social">("details");

  // Check if the current user is viewing their own profile
  const isOwnProfile = user && athlete && user.id === athlete.userId._id;

  if (isAthleteLoading) {
    return <FullPageLoadingSpinner />;
  }

  if (!athlete) {
    return <h1>Profile not found</h1>;
  }

  const paymentOptions = [
    { label: "Photo or vid shoot", price: athlete.minPayment?.shoot ?? null },
    { label: "In-person", price: athlete.minPayment?.inPerson ?? null },
    { label: "Content share", price: athlete.minPayment?.contentShare ?? null },
    {
      label: "Content creation",
      price: athlete.minPayment?.contentCreation ?? null,
    },
    {
      label: "Gifted collaboration",
      price: athlete.minPayment?.giftedCollab ?? null,
    },
    { label: "Custom", price: athlete.minPayment?.other ?? null },
  ];

  const hasSocialMedia =
    (athlete.socialMedia &&
      Object.keys(athlete.socialMedia).length > 0 &&
      athlete.socialMedia.instagram) ||
    athlete.socialMedia?.tiktok ||
    athlete.socialMedia?.twitter;

  const hasMinPayment = paymentOptions.some((option) => option.price !== null);

  return (
    <div className="tw-p-6 tw-text-aims-text-primary">
      {/* Header */}
      <div className="tw-flex tw-items-center tw-justify-between tw-mb-6">
        <div className="tw-flex tw-items-center">
          <div className="tw-relative tw-w-20 tw-h-20 tw-rounded-lg tw-mr-4">
            <Image
              src={athlete.profilePicture.url || "/no-profile-pic.jpg"}
              alt="Profile"
              fill
              className="tw-object-cover tw-rounded-lg"
            />
          </div>
          <div className="tw-flex tw-flex-col tw-gap-2">
            <h1 className="tw-text-2xl tw-font-bold">
              {athlete.name || athlete.userId.name}
            </h1>
            <div className="tw-text-aims-dark-6 tw-gap-2 tw-flex tw-flex-col tw-text-sm">
              <div className="tw-flex tw-items-center tw-gap-2">
                <AcademicCapIcon className="tw-w-4 tw-h-4" />
                {athlete.university} ({athlete.yearInSchool})
              </div>
              <div className="tw-flex tw-items-center tw-gap-2">
                <BoltIcon className="tw-w-4 tw-h-4" />
                {athlete.sport}
              </div>
              {athlete.hometown && (
                <div className="tw-flex tw-items-center tw-gap-2">
                  <HomeIcon className="tw-w-4 tw-h-4" />
                  {athlete.hometown}
                </div>
              )}
            </div>
          </div>
        </div>
        {!isOwnProfile && (
          <div className="tw-flex tw-items-center">
            <SendMessageButton
              targetUserId={athlete.userId._id}
              targetName={athlete.name || athlete.userId.name}
              variant="default"
              className="tw-text-black"
            />
          </div>
        )}
      </div>

      {/* Tabs */}
      <div className="tw-flex tw-gap-8 tw-mb-6 tw-border-b tw-border-gray-700">
        <button
          className={`tw-pb-2 tw-font-semibold ${activeTab === "details" ? "tw-border-b-2 tw-border-aims-primary" : "tw-text-aims-text-secondary"}`}
          onClick={() => setActiveTab("details")}
        >
          Athlete Details
        </button>
        {/* <button
          className={`tw-pb-2 tw-font-semibold ${activeTab === "social" ? "tw-border-b-2 tw-border-aims-primary" : "tw-text-aims-text-secondary"}`}
          onClick={() => setActiveTab("social")}
        >
          Social Media
        </button> */}
      </div>

      {activeTab === "details" && (
        <div className="tw-flex tw-flex-col lg:tw-flex-row tw-gap-8">
          {/* Left/Main Content */}
          <div
            className={`tw-flex-1 tw-flex tw-flex-col ${hasSocialMedia ? "tw-gap-6" : ""}`}
          >
            {/* Social Media Cards */}

            {hasSocialMedia && (
              <div className="tw-grid tw-grid-cols-1 sm:tw-grid-cols-2 tw-gap-4">
                {athlete.socialMedia?.instagram && (
                  <SocialMediaCard
                    icon="/icons/instagram.svg"
                    alt="Instagram"
                    handle={athlete.socialMedia?.instagram || "bonniegreen2006"}
                    followers="11.2k"
                  />
                )}
                {athlete.socialMedia?.tiktok && (
                  <SocialMediaCard
                    icon="/icons/tiktok.svg"
                    alt="TikTok"
                    handle={athlete.socialMedia?.tiktok || "bonniegreen2006"}
                    followers="1.5m"
                  />
                )}
                {athlete.socialMedia?.twitter && (
                  <SocialMediaCard
                    icon="/icons/x.svg"
                    alt="X"
                    handle={athlete.socialMedia?.twitter || "bonniegreen2006"}
                    followers="11.2k"
                  />
                )}
              </div>
            )}

            {/* About Card */}
            <div className="tw-bg-aims-dark-2 tw-p-6 tw-rounded-lg">
              <h3 className="tw-text-lg tw-font-semibold tw-mb-2">
                About {athlete.userId.name?.split(" ")[0] || "the athlete"}
              </h3>
              {athlete.bio && (
                <>
                  <div className="tw-text-aims-dark-6 tw-text-sm tw-mb-2">
                    Biography
                  </div>
                  <div className="tw-mb-4 tw-text-aims-text-primary">
                    {athlete.bio}
                  </div>
                </>
              )}
              {/* <div className="tw-mb-2 tw-text-aims-dark-6 tw-text-sm">
                Business industry interest
              </div>
              <div className="tw-flex tw-flex-wrap tw-gap-2 tw-mb-4">
                {(athlete.businessInterests?.length
                  ? athlete.businessInterests
                  : ["Retail", "Product Brands", "Jewelry", "Technology"]
                ).map((interest) => (
                  <span
                    key={interest}
                    className="tw-bg-aims-dark-4 tw-text-xs tw-rounded-full tw-px-3 tw-py-1 tw-text-aims-text-primary"
                  >
                    {interest}
                  </span>
                ))}
              </div> */}
              <div className="tw-mb-2 tw-text-aims-dark-6 tw-text-sm">
                Interests
              </div>
              <div className="tw-flex tw-flex-wrap tw-gap-2">
                {athlete.businessInterests?.map((interest) => (
                  <span
                    key={interest}
                    className="tw-bg-aims-dark-4 tw-text-xs tw-rounded-full tw-px-3 tw-py-1 tw-text-aims-text-primary"
                  >
                    {interest}
                  </span>
                ))}
              </div>
            </div>
          </div>

          {/* Right/Sidebar */}
          <div className="tw-w-full lg:tw-w-[340px] tw-flex-shrink-0 tw-flex tw-flex-col tw-gap-6">
            {/* Minimum Payment Card */}
            {hasMinPayment && <MinimumPaymentCard options={paymentOptions} />}
            {/* Contracts Card */}
            {/* <div className="tw-bg-aims-dark-2 tw-p-6 tw-rounded-lg">
              <div className="tw-flex tw-items-center tw-justify-between tw-mb-2">
                <h4 className="tw-font-semibold">Contracts</h4>
                <span className="tw-text-xs tw-text-aims-dark-6">
                  Last week
                </span>
              </div> */}
            {/* Pie chart placeholder */}
            {/* <div className="tw-flex tw-justify-center tw-items-center tw-h-40">
                <svg width="120" height="120" viewBox="0 0 32 32">
                  <circle r="16" cx="16" cy="16" fill="#23272f" />
                  <path
                    d="M16 16 L16 0 A16 16 0 1 1 6.7 26.7 Z"
                    fill="#3b82f6"
                  />
                  <path
                    d="M16 16 L6.7 26.7 A16 16 0 0 1 16 0 Z"
                    fill="#22d3ee"
                  />
                </svg>
              </div>
              <div className="tw-flex tw-justify-center tw-gap-4 tw-mt-2">
                <div className="tw-flex tw-items-center tw-gap-1">
                  <span className="tw-inline-block tw-w-3 tw-h-3 tw-rounded-full tw-bg-blue-500"></span>
                  <span className="tw-text-xs">Completed contract</span>
                </div>
                <div className="tw-flex tw-items-center tw-gap-1">
                  <span className="tw-inline-block tw-w-3 tw-h-3 tw-rounded-full tw-bg-cyan-400"></span>
                  <span className="tw-text-xs">Ongoing contract</span>
                </div>
              </div>
              <div className="tw-flex tw-justify-center tw-gap-6 tw-mt-2">
                <div className="tw-text-center">
                  <div className="tw-text-blue-500 tw-font-bold tw-text-lg">
                    21
                  </div>
                  <div className="tw-text-xs tw-text-aims-dark-6">
                    Completed
                  </div>
                </div>
                <div className="tw-text-center">
                  <div className="tw-text-cyan-400 tw-font-bold tw-text-lg">
                    5
                  </div>
                  <div className="tw-text-xs tw-text-aims-dark-6">Ongoing</div>
                </div>
              </div> */}
            {/* </div> */}
          </div>
        </div>
      )}

      {/* {activeTab === "social" && (
        <div className="tw-bg-aims-dark-2 tw-p-6 tw-rounded-lg">
          <h3 className="tw-text-lg tw-font-semibold tw-mb-4">
            Social Media Links
          </h3>
        </div>
      )} */}
    </div>
  );
}
