"use client";

import { useState } from "react";
import { trpc } from "@/lib/trpc/client";
import { useDebounce } from "@/hooks/use-debounce";

import { ContractStatus } from "@repo/server/src/types/contract";

import { ContractList } from "@/components/contract/ContractList";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { MagnifyingGlassIcon } from "@heroicons/react/24/outline";

export default function BrandContractsPage() {
  const [statusFilter, setStatusFilter] = useState<ContractStatus | "all">("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [page, setPage] = useState(1);
  const limit = 10;
  const debouncedSearch = useDebounce(searchQuery, 300);

  // Fetch contracts
  const {
    data: contractsData,
    isLoading,
    refetch,
  } = trpc.contract.getBrandContracts.useQuery({
    status: statusFilter === "all" ? undefined : statusFilter,
    search: debouncedSearch || undefined,
    page,
    limit,
  });

  const handleStatusFilter = (status: string) => {
    const statusValue = status as ContractStatus | "all";
    setStatusFilter(statusValue);
    setPage(1); // Reset to first page when filtering
  };

  const handleRefresh = () => {
    refetch();
  };

  const contracts = contractsData?.contracts || [];
  const totalContracts = contractsData?.total || 0;
  const totalPages = contractsData?.pages || 0;

  const stats = {
    total: totalContracts,
    pending: contracts.filter(c => c.status === "PENDING_BRAND_REVIEW" || c.status === "PENDING_BRAND_APPROVAL").length,
    cancelled: contracts.filter(c => c.status === "CANCELLED").length,
    expired: contracts.filter(c => c.status === "EXPIRED").length,
    paid: contracts.filter(c => c.status === "PAID").length,
    awaitingDeliverables: contracts.filter(c => c.status === "AWAITING_DELIVERABLES").length,
    fulfilled: contracts.filter(c => c.status === "FULFILLED").length,
  };


  return (
    <div className="tw-container tw-mx-auto tw-px-4 tw-py-8 tw-space-y-8">
      {/* Page Header */}
      <div className="tw-flex tw-justify-between tw-items-center">
        <div>
          <h1 className="tw-text-3xl tw-font-bold">Contract Management</h1>
        </div>
      </div>

      {/* Statistics Cards */}
      {stats && (
        <div className="tw-grid tw-grid-cols-1 md:tw-grid-cols-3 tw-gap-4">
          <Card>
            <CardHeader className="tw-pb-2">
              <CardTitle className="tw-text-sm tw-font-medium">
                Total Contracts
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="tw-text-2xl tw-font-bold">{stats.total}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="tw-pb-2">
              <CardTitle className="tw-text-sm tw-font-medium">
                Awaiting Deliverables
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="tw-text-2xl tw-font-bold tw-text-purple-600">{stats.awaitingDeliverables}</div>
            </CardContent>
          </Card>
  
          <Card>
            <CardHeader className="tw-pb-2">
              <CardTitle className="tw-text-sm tw-font-medium">
                Fulfilled
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="tw-text-2xl tw-font-bold tw-text-aims-dark-6">{stats.fulfilled}</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Search and Filters */}
      <div className="tw-flex tw-flex-col sm:tw-flex-row tw-gap-4">
        <div className="tw-relative tw-flex-1">
          <MagnifyingGlassIcon className="tw-absolute tw-left-3 tw-top-1/2 -tw-translate-y-1/2 tw-h-5 tw-w-5 tw-text-aims-text-secondary" />
          <Input
            type="text"
            placeholder="Search contracts..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="tw-pl-10 tw-bg-aims-dark-2 tw-border-aims-dark-3 tw-text-aims-text-primary placeholder:tw-text-aims-text-secondary tw-h-12 sm:tw-h-10 tw-text-base"
          />
        </div>
        <div className="tw-w-full sm:tw-w-48">
          <Select value={statusFilter} onValueChange={handleStatusFilter}>
            <SelectTrigger className="!tw-bg-aims-dark-3 tw-text-aims-text-primary tw-h-12 sm:tw-h-10">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              <SelectItem value={ContractStatus.DRAFT}>Draft</SelectItem>
              <SelectItem value={ContractStatus.PENDING_BRAND_REVIEW}>Pending Review</SelectItem>
              <SelectItem value={ContractStatus.PENDING_BRAND_APPROVAL}>Pending Approval</SelectItem>
              <SelectItem value={ContractStatus.PENDING_ATHLETE_SIGNATURE}>Pending Signature</SelectItem>
              <SelectItem value={ContractStatus.ATHLETE_SIGNED}>Signed</SelectItem>
              <SelectItem value={ContractStatus.CANCELLED}>Cancelled</SelectItem>
              <SelectItem value={ContractStatus.EXPIRED}>Expired</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <Button variant="outline" onClick={handleRefresh} className="tw-text-aims-text-primary tw-h-12 sm:tw-h-10">
          Refresh
        </Button>
      </div>

      {/* Contract List */}
      <ContractList
        contracts={contracts}
        isLoading={isLoading}
      />

      {/* Pagination */}
      {totalPages > 1 && (
        <Card>
          <CardContent className="tw-py-4">
            <div className="tw-flex tw-justify-between tw-items-center">
              <p className="tw-text-sm tw-text-gray-600">
                Showing {((page - 1) * limit) + 1} to {Math.min(page * limit, totalContracts)} of {totalContracts} contracts
              </p>
              <div className="tw-flex tw-gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPage(p => Math.max(1, p - 1))}
                  disabled={page === 1}
                >
                  Previous
                </Button>
                <span className="tw-flex tw-items-center tw-px-3 tw-text-sm">
                  Page {page} of {totalPages}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPage(p => Math.min(totalPages, p + 1))}
                  disabled={page === totalPages}
                >
                  Next
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Help Section */}
      {/* <Card>
        <CardHeader>
          <CardTitle>Getting Started with Contracts</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="tw-space-y-4">
            <div className="tw-flex tw-items-start tw-space-x-3">
              <div className="tw-w-6 tw-h-6 tw-bg-blue-100 tw-text-blue-600 tw-rounded-full tw-flex tw-items-center tw-justify-center tw-text-sm tw-font-medium tw-flex-shrink-0">
                1
              </div>
              <div>
                <h4 className="tw-font-medium">Accept Campaign Applications</h4>
                <p className="tw-text-sm tw-text-gray-600">
                  Review and accept athlete applications for your campaigns.
                </p>
              </div>
            </div>
            
            <div className="tw-flex tw-items-start tw-space-x-3">
              <div className="tw-w-6 tw-h-6 tw-bg-blue-100 tw-text-blue-600 tw-rounded-full tw-flex tw-items-center tw-justify-center tw-text-sm tw-font-medium tw-flex-shrink-0">
                2
              </div>
              <div>
                <h4 className="tw-font-medium">Generate Contracts</h4>
                <p className="tw-text-sm tw-text-gray-600">
                  Once applications are accepted, generate formal contracts with deliverable details and payment terms.
                </p>
              </div>
            </div>
            
            <div className="tw-flex tw-items-start tw-space-x-3">
              <div className="tw-w-6 tw-h-6 tw-bg-blue-100 tw-text-blue-600 tw-rounded-full tw-flex tw-items-center tw-justify-center tw-text-sm tw-font-medium tw-flex-shrink-0">
                3
              </div>
              <div>
                <h4 className="tw-font-medium">Review & Approve</h4>
                <p className="tw-text-sm tw-text-gray-600">
                  Review contract details, make any necessary edits, and approve to send to athletes for signature.
                </p>
              </div>
            </div>
            
            <div className="tw-flex tw-items-start tw-space-x-3">
              <div className="tw-w-6 tw-h-6 tw-bg-blue-100 tw-text-blue-600 tw-rounded-full tw-flex tw-items-center tw-justify-center tw-text-sm tw-font-medium tw-flex-shrink-0">
                4
              </div>
              <div>
                <h4 className="tw-font-medium">Track Progress</h4>
                <p className="tw-text-sm tw-text-gray-600">
                  Monitor contract status and manage the collaboration process from signature to completion.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card> */}
    </div>
  );
}
