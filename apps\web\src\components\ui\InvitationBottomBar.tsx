"use client";

import { Button } from "./button";

interface InvitationBottomBarProps {
  // Content and state
  selectedCount?: number;
  selectedLabel?: string;
  
  // Navigation
  onBack?: () => void;
  onNext?: () => void;
  backLabel?: string;
  nextLabel?: string;
  
  // State management
  isVisible?: boolean;
  isLoading?: boolean;
  isNextDisabled?: boolean;
  isBackDisabled?: boolean;
  
  // Custom content
  children?: React.ReactNode;
}

export function InvitationBottomBar({
  selectedCount,
  selectedLabel,
  onBack,
  onNext,
  backLabel = "Back",
  nextLabel = "Next",
  isVisible = true,
  isLoading = false,
  isNextDisabled = false,
  isBackDisabled = false,
  children,
}: InvitationBottomBarProps) {
  return (
    <div
      className={`
        tw-fixed tw-bottom-0 tw-left-0 tw-right-0 tw-z-50
        tw-transition-all tw-duration-300
        ${
          isVisible
            ? "tw-translate-y-0 tw-opacity-100 pointer-events-auto"
            : "tw-translate-y-full tw-opacity-0 pointer-events-none"
        }
      `}
    >
      <div className="tw-bg-aims-dark-2 tw-p-4 tw-border-t tw-border-aims-dark-4">
        {/* Main content area */}
        <div className="tw-flex tw-flex-col sm:tw-flex-row tw-justify-between tw-items-center tw-gap-3 sm:tw-gap-4">
          {/* Left side - Selection info or custom content */}
          <div className="tw-flex-1 tw-text-center sm:tw-text-left">
            {children ? (
              children
            ) : selectedCount !== undefined ? (
              <div className="tw-text-aims-text-primary tw-text-sm sm:tw-text-base">
                {selectedCount} {selectedLabel || "item"}
                {selectedCount !== 1 ? "s" : ""} selected
              </div>
            ) : null}
          </div>

          {/* Right side - Navigation buttons */}
          <div className="tw-flex tw-gap-3 tw-w-full sm:tw-w-auto">
            {onBack && (
              <Button
                onClick={onBack}
                disabled={isBackDisabled || isLoading}
                variant="outline"
                className="tw-flex-1 sm:tw-flex-none tw-min-w-[100px] tw-h-12 sm:tw-h-10 tw-min-h-[44px] tw-text-aims-text-primary"
              >
                {backLabel}
              </Button>
            )}
            {onNext && (
              <Button
                onClick={onNext}
                disabled={isNextDisabled || isLoading}
                className="tw-flex-1 sm:tw-flex-none tw-min-w-[120px] tw-h-12 sm:tw-h-10 tw-min-h-[44px]"
              >
                {isLoading ? "Loading..." : nextLabel}
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
