"use client";

import { useEffect, useRef, useState } from "react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { AnimatedMount } from "@/components/ui/AnimatedMount";
import { Checkbox } from "@/components/ui/auth/Checkbox";
import { FormInput } from "@/components/ui/auth/FormInput";
import { UserTypeToggle } from "@/components/ui/auth/UserTypeToggle";
import { Button } from "@/components/ui/button";
import { LoadingSpinner } from "@/components/ui/LoadingSpinner";
import { useToast } from "@/components/ui/toast/use-toast";
import { useAuth } from "@/hooks/use-auth";
import { client } from "@/lib/trpc/client";
import { TokensStorage } from "@/lib/utils/tokens-storage";
import { setAuthState } from "@/store/slices/auth";
import { setUserType as setOnboardingUserType } from "@/store/slices/onboarding";
import { C<PERSON><PERSON><PERSON><PERSON>ponse, GoogleLogin } from "@react-oauth/google";
import ReCAPTCHA from "react-google-recaptcha";
import { useDispatch } from "react-redux";
import type { AuthResponse } from "@repo/server/src/models/user";

const RECAPTCHA_SITE_KEY = process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY;

// Type guard to check if the verification response contains auth data
function isVerifiedWithAuth(response: any): response is AuthResponse & { verified: true } {
  return (
    response.verified === true &&
    "profile" in response &&
    "tokens" in response &&
    typeof response.tokens === "object" &&
    "access" in response.tokens &&
    "refresh" in response.tokens
  );
}
export default function Register() {
  const searchParams = useSearchParams();
  const browserUserType = searchParams.get("userType") as "brand" | "athlete";
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [userType, setUserType] = useState<"brand" | "athlete">(
    browserUserType || "brand",
  );
  const [agreeToTerms, setAgreeToTerms] = useState(false);
  const [isOver18, setIsOver18] = useState(false);
  const [registrationSuccess, setRegistrationSuccess] = useState(false);
  const [pollingInterval, setPollingInterval] = useState<NodeJS.Timeout | null>(
    null,
  );

  const router = useRouter();
  const { signUp, signInWithGoogle } = useAuth();
  const dispatch = useDispatch();
  const { toast } = useToast();

  const recaptchaRef = useRef<ReCAPTCHA>(null);

  // Add polling effect
  useEffect(() => {
    if (registrationSuccess && email) {
      const interval = setInterval(async () => {
        try {
          const response = await client.auth.checkEmailVerificationStatus.query(
            { email },
          );
          if (isVerifiedWithAuth(response)) {
            // Clear polling
            if (pollingInterval) {
              clearInterval(pollingInterval);
              setPollingInterval(null);
            }

            // Store tokens and update auth state
            TokensStorage.setTokens(
              response.tokens.access,
              response.tokens.refresh,
            );
            dispatch(setAuthState(response));

            // Redirect based on user type
            if (response.profile.userType === "athlete") {
              router.push("/onboarding/athlete");
            } else {
              router.push("/onboarding/brand");
            }
          }
        } catch (error) {
          console.error("Error checking verification status:", error);
        }
      }, 5000); // Poll every 5 seconds

      setPollingInterval(interval);

      // Cleanup on unmount
      return () => {
        if (interval) {
          clearInterval(interval);
        }
      };
    }
  }, [registrationSuccess, email, router, dispatch, pollingInterval]);

  const validateForm = () => {
    if (!firstName || !lastName) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "First and last name are required",
      });
      return false;
    }
    if (!email) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Email is required",
      });
      return false;
    }
    if (!password) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Password is required",
      });
      return false;
    }
    if (password !== confirmPassword) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Passwords do not match",
      });
      return false;
    }
    if (password.length < 8) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Password must be at least 8 characters long",
      });
      return false;
    }
    if (!agreeToTerms) {
      toast({
        variant: "destructive",
        title: "Error",
        description:
          "You must agree to the Terms of Service and Privacy Policy",
      });
      return false;
    }
    if (userType === "athlete" && !isOver18) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "You must be over 18 to create an account",
      });
      return false;
    }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    const captchaToken = await recaptchaRef.current?.executeAsync();
    if (!captchaToken) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Please complete the CAPTCHA",
      });
      return;
    }

    try {
      setIsLoading(true);
      localStorage.setItem("userType", userType);
      dispatch(setOnboardingUserType(userType));
      const response = await signUp(
        `${firstName} ${lastName}`,
        email,
        password,
        userType,
        captchaToken,
      );
      const dev = process.env.NODE_ENV === "development";
      if (
        dev &&
        response &&
        typeof response === "object" &&
        "tokens" in response &&
        "profile" in response
      ) {
        // Auto-login and redirect in dev
        if (response.profile.userType === "athlete") {
          router.push("/onboarding/athlete");
        } else {
          router.push("/onboarding/brand");
        }
        return;
      }
      setRegistrationSuccess(true);
    } catch (error) {
      if (error instanceof Error && error.message === "User already exists") {
        toast({
          variant: "destructive",
          title: "Error",
          description:
            "An account with this email already exists. Please sign in instead.",
        });
      } else {
        toast({
          variant: "destructive",
          title: "Error",
          description: (error as Error).message,
        });
      }
      recaptchaRef.current?.reset();
    } finally {
      setIsLoading(false);
    }
  };

  if (registrationSuccess) {
    return (
      <div className="tw-w-full">
        <div className="tw-text-center">
          <h1 className="tw-text-2xl tw-font-bold tw-tracking-tight">
            Account Created Successfully!
          </h1>
          <p className="tw-mt-4">
            We&apos;ve sent a verification link to{" "}
            <span className="tw-font-medium">{email}</span>. Please check your
            email and verify your account.
          </p>
          <p className="tw-mt-4">
            Please check your inbox and click the verification link to continue.
          </p>
          <div className="tw-mt-4 tw-flex tw-justify-center">
            <LoadingSpinner />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div>
      <h4 className="tw-mb-6">Create account</h4>
      <p className="tw-text-sm tw-text-aims-text-secondary tw-mb-4">
        Select your profile type:
      </p>

      <UserTypeToggle userType={userType} onChange={setUserType} />

      <form onSubmit={handleSubmit} className="tw-space-y-4">
        <div className="tw-grid tw-grid-cols-2 tw-gap-4">
          <FormInput
            id="firstName"
            label="First name"
            type="text"
            value={firstName}
            onChange={setFirstName}
            required
            disabled={isLoading}
            placeholder="John"
          />
          <FormInput
            id="lastName"
            label="Last name"
            type="text"
            value={lastName}
            onChange={setLastName}
            required
            disabled={isLoading}
            placeholder="Doe"
          />
        </div>

        <FormInput
          id="email"
          label="Your email"
          type="email"
          value={email}
          onChange={setEmail}
          required
          disabled={isLoading}
          placeholder="<EMAIL>"
        />

        <FormInput
          id="password"
          label="Password"
          type="password"
          value={password}
          onChange={setPassword}
          required
          disabled={isLoading}
          isPassword
        />

        <FormInput
          id="confirmPassword"
          label="Confirm password"
          type="password"
          value={confirmPassword}
          onChange={setConfirmPassword}
          required
          disabled={isLoading}
          isPassword
        />

        <Checkbox
          id="terms"
          checked={agreeToTerms}
          onChange={setAgreeToTerms}
          label={
            <>
              I agree to the{" "}
              <a
                href="/aims-tos.pdf"
                target="_blank"
                rel="noopener noreferrer"
                className="tw-text-aims-primary hover:tw-text-aims-primary/90"
              >
                Terms of Service
              </a>{" "}
              and{" "}
              <a
                href="/aims-privacy-policy.pdf"
                target="_blank"
                rel="noopener noreferrer"
                className="tw-text-aims-primary hover:tw-text-aims-primary/90"
              >
                Privacy Policy
              </a>
            </>
          }
        />

        <AnimatedMount show={userType === "athlete"}>
          <Checkbox
            id="age"
            checked={isOver18}
            onChange={setIsOver18}
            label="I confirm that I am over 18 years old"
          />
        </AnimatedMount>

        <ReCAPTCHA
          ref={recaptchaRef}
          size="invisible"
          sitekey={RECAPTCHA_SITE_KEY || ""}
        />

        <Button type="submit" disabled={isLoading} className="tw-w-full">
          {isLoading ? (
            <div className="tw-flex tw-items-center tw-justify-center tw-gap-2">
              <span>Creating account...</span>
              <LoadingSpinner color="black" />
            </div>
          ) : (
            "Create account"
          )}
        </Button>

        <div className="tw-relative tw-my-6">
          <div className="tw-absolute tw-inset-0 tw-flex tw-items-center">
            <div className="tw-w-full tw-border-t tw-border-aims-dark-3" />
          </div>
          <div className="tw-relative tw-flex tw-justify-center tw-text-sm">
            <span className="tw-px-2 tw-text-aims-text-secondary tw-bg-aims-dark-2">
              OR
            </span>
          </div>
        </div>

        <div className="tw-flex tw-gap-4 tw-justify-center">
          <GoogleLogin
            onSuccess={async (credentialResponse: CredentialResponse) => {
              try {
                setIsLoading(true);
                const response = await signInWithGoogle(
                  credentialResponse.credential || "",
                  userType,
                );
                if (
                  response &&
                  typeof response === "object" &&
                  "profile" in response
                ) {
                  if (response.profile.userType === "athlete") {
                    router.push("/onboarding/athlete");
                  } else {
                    router.push("/onboarding/brand");
                  }
                }
              } catch (error) {
                toast({
                  variant: "destructive",
                  title: "Error",
                  description: (error as Error).message,
                });
              } finally {
                setIsLoading(false);
              }
            }}
            onError={() => {
              toast({
                variant: "destructive",
                title: "Error",
                description: "Google Sign-In failed",
              });
            }}
            shape="rectangular"
            text="signup_with"
            locale="en"
          />
        </div>

        <p className="tw-text-center tw-text-sm tw-text-aims-text-secondary">
          I already have an account?{" "}
          <Link
            href="/auth/login"
            className="tw-text-aims-primary hover:tw-text-aims-primary/90"
          >
            Sign in
          </Link>
        </p>
      </form>
    </div>
  );
}
