import * as React from "react";
import { cn } from "@/lib/utils";





export interface TextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}

const baseTextareaClasses =
  "tw-w-full tw-rounded-md tw-text-sm disabled:tw-cursor-not-allowed focus-visible:tw-outline-none disabled:tw-opacity-50 tw-p-2 tw-bg-aims-dark-3 tw-text-aims-text-primary tw-border-aims-dark-3 tw-border tw-border-input tw-break-words tw-whitespace-pre-wrap tw-overflow-wrap-anywhere";

const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ className, ...props }, ref) => {
    const textareaClassName = React.useMemo(
      () => cn(baseTextareaClasses, className),
      [className],
    );

    return <textarea className={textareaClassName} ref={ref} {...props} />;
  },
);
Textarea.displayName = "Textarea";

export { Textarea };