import { useState } from "react";
import { EyeIcon, EyeSlashIcon } from "@heroicons/react/24/outline";

interface FormInputProps {
  id: string;
  label: string;
  type: string;
  value: string;
  onChange: (value: string) => void;
  required?: boolean;
  disabled?: boolean;
  placeholder?: string;
  isPassword?: boolean;
}

export function FormInput({
  id,
  label,
  type,
  value,
  onChange,
  required = false,
  disabled = false,
  placeholder,
  isPassword = false,
}: FormInputProps) {
  const [showPassword, setShowPassword] = useState(false);

  return (
    <div>
      <label
        htmlFor={id}
        className="tw-block tw-text-sm tw-font-medium tw-text-aims-text-secondary tw-mb-2"
      >
        {label}
      </label>
      <div className="tw-relative">
        <input
          id={id}
          type={isPassword ? (showPassword ? "text" : "password") : type}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          required={required}
          disabled={disabled}
          className="tw-w-full tw-bg-aims-dark-3 tw-rounded-lg tw-px-4 tw-py-2.5 tw-text-aims-text-primary tw-border tw-border-transparent tw-placeholder-aims-dark-6 focus:tw-ring-0 focus:tw-outline-none focus:tw-border-aims-text-primary"
          placeholder={placeholder}
        />
        {isPassword && (
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="tw-absolute tw-right-3 tw-top-1/2 tw--translate-y-1/2 tw-text-aims-text-secondary hover:tw-text-aims-text-primary focus:tw-outline-none"
          >
            {showPassword ? (
              <EyeIcon className="tw-w-5 tw-h-5" />
            ) : (
              <EyeSlashIcon className="tw-w-5 tw-h-5" />
            )}
          </button>
        )}
      </div>
    </div>
  );
}
